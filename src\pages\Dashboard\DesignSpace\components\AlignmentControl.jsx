import { useDesignSpace } from '@contexts/DesignSpaceContext';

import { PiSelectionPlus } from 'react-icons/pi';
import { BlockUI } from 'primereact/blockui';
import {
    MdAlignHorizontalLeft,
    MdAlignVerticalCenter,
    MdOutlineAlignHorizontalCenter,
    MdOutlineAlignHorizontalRight
} from 'react-icons/md';

function AlignmentControl() {
    const { selectedIds, designSpaceRef, setElements, isMultiSelectActive, setIsMultiSelectActive } = useDesignSpace();

    const alignElements = (alignment) => {
        const designSpace = designSpaceRef.current;
        const designSpaceRect = designSpace.getBoundingClientRect();

        setElements((prevElements) =>
            prevElements.map((el) => {
                if (selectedIds.includes(el.id)) {
                    switch (alignment) {
                        case 'left':
                            return { ...el, x: 0 };
                        case 'right':
                            return { ...el, x: designSpaceRect.width - el.width };
                        case 'center-horizontal':
                            return {
                                ...el,
                                x: (designSpaceRect.width - el.width) / 2,
                            };
                        case 'center-vertical':
                            return {
                                ...el,
                                y: (designSpaceRect.height - el.height) / 2,
                            };
                        default:
                            return el;
                    }
                }
                return el;
            })
        );
    };

    const multiSelectionHandler = () => {
        setIsMultiSelectActive(prev => !prev)
    };

    return (
        <div className="flex flex-col">
            <label htmlFor="" className='me-1 text-sm'>Group Alignment </label>
            <div className="flex items-center">

                <button
                    className={`border border-[black] rounded-l-[6px] p-2 ${isMultiSelectActive ? "bg-[#cff4f0]" : ""}`}
                    onClick={multiSelectionHandler}  >
                    <PiSelectionPlus size={21} className="cursor-pointer" />
                </button>
                <BlockUI blocked={!isMultiSelectActive} className='me-3 p-2 rounded-r-[6px]'>
                    <div className="flex items-center alignment-container ">
                        <button
                            className="p-2   border"
                            onClick={() => alignElements('left')}
                            disabled={selectedIds.length === 0}
                        >
                            <MdAlignHorizontalLeft size={21} />
                        </button>
                        <button
                            className="p-2 border-x-0 border"
                            onClick={() => alignElements('right')}
                            disabled={selectedIds.length === 0}
                        >
                            <MdOutlineAlignHorizontalRight size={21} />
                        </button>
                        <button
                            className="p-2 border-r-0 border"
                            onClick={() => alignElements('center-horizontal')}
                            disabled={selectedIds.length === 0}
                        >
                            <MdOutlineAlignHorizontalCenter size={21} />
                        </button>
                        <button
                            className="p-2 border rounded-r-[6px]"
                            onClick={() => alignElements('center-vertical')}
                            disabled={selectedIds.length === 0}
                        >
                            <MdAlignVerticalCenter size={21} />
                        </button>
                    </div>
                </BlockUI>
            </div>
        </div>
    )
}

export default AlignmentControl