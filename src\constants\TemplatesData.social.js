// Social Media Templates for DesignSpace
// This file contains professional social media templates

// Instagram Templates
export const instagramTemplates = [
  {
    id: 'ig-001',
    name: 'Instagram Modern Post',
    width: 1080,
    height: 1080,
    thumbnail: 'https://placehold.co/1080x1080/6366f1/ffffff?text=Instagram+Modern',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1080,
        height: 1080,
        backgroundColor: '#6366f1',
        style: { background: 'linear-gradient(135deg, #6366f1, #a855f7)' },
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 80,
        y: 80,
        width: 920,
        height: 920,
        backgroundColor: 'transparent',
        borderColor: '#ffffff',
        borderWidth: 2,
      },
      {
        id: 'el_3',
        type: 'text',
        x: 140,
        y: 400,
        width: 800,
        height: 200,
        value: 'MODERN DESIGN',
        fontSize: 80,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
        letterSpacing: '4px',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 140,
        y: 600,
        width: 800,
        height: 100,
        value: 'Professional Instagram Template',
        fontSize: 30,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'center',
      },
    ]
  },
  {
    id: 'ig-002',
    name: 'Instagram Product Showcase',
    width: 1080,
    height: 1080,
    thumbnail: 'https://placehold.co/1080x1080/ffffff/333333?text=Product+Showcase',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1080,
        height: 1080,
        backgroundColor: '#ffffff',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 40,
        y: 40,
        width: 1000,
        height: 1000,
        backgroundColor: '#f8fafc',
        borderRadius: 20,
      },
      {
        id: 'el_3',
        type: 'shape',
        shapeType: 'rectangle',
        x: 290,
        y: 200,
        width: 500,
        height: 500,
        backgroundColor: '#e2e8f0',
        borderRadius: 10,
      },
      {
        id: 'el_4',
        type: 'text',
        x: 140,
        y: 750,
        width: 800,
        height: 80,
        value: 'PREMIUM PRODUCT',
        fontSize: 50,
        fontWeight: 'bold',
        color: '#0f172a',
        textAlign: 'center',
      },
      {
        id: 'el_5',
        type: 'text',
        x: 140,
        y: 830,
        width: 800,
        height: 60,
        value: '$99.99 - Limited Edition',
        fontSize: 30,
        fontWeight: 'normal',
        color: '#64748b',
        textAlign: 'center',
      },
      {
        id: 'el_6',
        type: 'shape',
        shapeType: 'rectangle',
        x: 440,
        y: 900,
        width: 200,
        height: 50,
        backgroundColor: '#0f172a',
        borderRadius: 25,
      },
      {
        id: 'el_7',
        type: 'text',
        x: 440,
        y: 915,
        width: 200,
        height: 20,
        value: 'SHOP NOW',
        fontSize: 20,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
    ]
  },
  {
    id: 'ig-003',
    name: 'Instagram Quote Post',
    width: 1080,
    height: 1080,
    thumbnail: 'https://placehold.co/1080x1080/0f172a/ffffff?text=Quote+Post',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1080,
        height: 1080,
        backgroundColor: '#0f172a',
      },
      {
        id: 'el_2',
        type: 'text',
        x: 140,
        y: 200,
        width: 800,
        height: 100,
        value: '"',
        fontSize: 150,
        fontWeight: 'bold',
        color: '#f59e0b',
        textAlign: 'center',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 140,
        y: 400,
        width: 800,
        height: 300,
        value: 'Success is not final, failure is not fatal: It is the courage to continue that counts.',
        fontSize: 40,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 140,
        y: 750,
        width: 800,
        height: 50,
        value: '- Winston Churchill',
        fontSize: 24,
        fontWeight: 'bold',
        color: '#f59e0b',
        textAlign: 'center',
      },
      {
        id: 'el_5',
        type: 'shape',
        shapeType: 'rectangle',
        x: 390,
        y: 830,
        width: 300,
        height: 2,
        backgroundColor: '#f59e0b',
      },
      {
        id: 'el_6',
        type: 'text',
        x: 140,
        y: 870,
        width: 800,
        height: 40,
        value: 'YOUR BRAND NAME',
        fontSize: 20,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
        letterSpacing: '3px',
      },
    ]
  },
  {
    id: 'ig-004',
    name: 'Instagram Sale Announcement',
    width: 1080,
    height: 1080,
    thumbnail: 'https://placehold.co/1080x1080/dc2626/ffffff?text=Sale+Announcement',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1080,
        height: 1080,
        backgroundColor: '#dc2626',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 40,
        y: 40,
        width: 1000,
        height: 1000,
        backgroundColor: 'transparent',
        borderColor: '#ffffff',
        borderWidth: 4,
      },
      {
        id: 'el_3',
        type: 'text',
        x: 140,
        y: 200,
        width: 800,
        height: 100,
        value: 'FLASH SALE',
        fontSize: 100,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 140,
        y: 400,
        width: 800,
        height: 300,
        value: '50% OFF',
        fontSize: 180,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_5',
        type: 'text',
        x: 140,
        y: 700,
        width: 800,
        height: 80,
        value: 'EVERYTHING',
        fontSize: 60,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_6',
        type: 'text',
        x: 140,
        y: 800,
        width: 800,
        height: 60,
        value: 'Limited time only. Use code:',
        fontSize: 30,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_7',
        type: 'text',
        x: 140,
        y: 870,
        width: 800,
        height: 60,
        value: 'FLASH50',
        fontSize: 40,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
        letterSpacing: '5px',
      },
    ]
  },
  {
    id: 'ig-005',
    name: 'Instagram Minimalist Post',
    width: 1080,
    height: 1080,
    thumbnail: 'https://placehold.co/1080x1080/f8fafc/333333?text=Minimalist+Post',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1080,
        height: 1080,
        backgroundColor: '#f8fafc',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'circle',
        x: 440,
        y: 340,
        width: 200,
        height: 200,
        backgroundColor: '#0f172a',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 140,
        y: 600,
        width: 800,
        height: 100,
        value: 'LESS IS MORE',
        fontSize: 60,
        fontWeight: 'bold',
        color: '#0f172a',
        textAlign: 'center',
        letterSpacing: '5px',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 140,
        y: 700,
        width: 800,
        height: 60,
        value: 'Minimalist Design Collection',
        fontSize: 24,
        fontWeight: 'normal',
        color: '#64748b',
        textAlign: 'center',
      },
      {
        id: 'el_5',
        type: 'shape',
        shapeType: 'rectangle',
        x: 490,
        y: 780,
        width: 100,
        height: 2,
        backgroundColor: '#0f172a',
      },
      {
        id: 'el_6',
        type: 'text',
        x: 140,
        y: 820,
        width: 800,
        height: 40,
        value: 'www.minimalist-brand.com',
        fontSize: 18,
        fontWeight: 'normal',
        color: '#64748b',
        textAlign: 'center',
      },
    ]
  },
];

// Facebook Templates
export const facebookTemplates = [
  {
    id: 'fb-001',
    name: 'Facebook Cover Photo',
    width: 1640,
    height: 924,
    thumbnail: 'https://placehold.co/1640x924/3b82f6/ffffff?text=Facebook+Cover',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1640,
        height: 924,
        backgroundColor: '#3b82f6',
        style: { background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)' },
      },
      {
        id: 'el_2',
        type: 'text',
        x: 100,
        y: 400,
        width: 800,
        height: 124,
        value: 'YOUR BUSINESS NAME',
        fontSize: 80,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'left',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 100,
        y: 524,
        width: 800,
        height: 60,
        value: 'Professional Services & Solutions',
        fontSize: 36,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'left',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 100,
        y: 600,
        width: 800,
        height: 40,
        value: 'www.yourbusiness.com',
        fontSize: 24,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'left',
      },
    ]
  }
];

// Export all social media templates
export const allSocialTemplates = [
  ...instagramTemplates,
  ...facebookTemplates
];
