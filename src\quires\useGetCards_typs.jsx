import { useQuery } from 'react-query';
import axiosInstance from '../config/Axios';

const fetchCards = async () => {
    console.log("📌 Base URL:", axiosInstance.defaults.baseURL);
    console.log("📌 Token:", localStorage.getItem("token"));

    try {
        const { data } = await axiosInstance.get('/get-card-types');

        console.log("📌 API Raw Response:", data); 
        console.log("📌 Card Types:", data.data); 

        return data.data; 
    } catch (error) {
        console.error("❌ API Error:", error.response ? error.response.data : error.message);
        throw new Error("Failed to fetch cards.");
    }
};

export const useGetCards_type = () => {
    return useQuery('cards_type', fetchCards, {
        staleTime: 0,   
        cacheTime: 0,  
        retry: 1,       
    });
};
