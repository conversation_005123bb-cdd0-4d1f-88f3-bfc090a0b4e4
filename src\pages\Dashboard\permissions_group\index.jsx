import { useState, useRef } from 'react';
import { GroupPermissions } from '@quires';
import Container from '@components/Container';

import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Tag } from 'primereact/tag'; // ✅ عرض الصلاحيات كشارات
import { Button } from 'primereact/button'; // ✅ زر "More"
import { OverlayPanel } from 'primereact/overlaypanel'; // ✅ قائمة منبثقة لعرض جميع الصلاحيات

function PermissionsGroupIndex() {
    const { data: groups, isLoading } = GroupPermissions();
    const op = useRef(null); // ✅ مرجع لعنصر OverlayPanel
    const [selectedPermissions, setSelectedPermissions] = useState([]); // ✅ قائمة الصلاحيات المؤقتة

    // ✅ دالة عرض الصلاحيات بطريقة احترافية
    const permissionsTemplate = (rowData) => {
        const permissions = rowData.permissions || [];
        const visiblePermissions = permissions.slice(0, 2); // ✅ عرض أول صلاحيتين فقط
        const hiddenPermissions = permissions.slice(2); // ✅ بقية الصلاحيات

        return (
            <div className="flex items-center relative">
                {/* ✅ عرض أول صلاحيتين فقط */}
                {visiblePermissions.map((perm, index) => (
                    <Tag key={index} value={perm} className="mr-2" />
                ))}

                {/* ✅ زر "More" عند وجود صلاحيات إضافية */}
                {hiddenPermissions.length > 0 && (
                    <>
                        <Button 
                            label={`+${hiddenPermissions.length} more`} 
                            className="p-button-text p-button-sm text-blue-600" 
                            onClick={(e) => {
                                setSelectedPermissions(permissions); // ✅ تحديث القائمة المختارة
                                op.current.toggle(e); // ✅ فتح القائمة المنبثقة
                            }} 
                        />
                        {/* ✅ القائمة المنبثقة عند النقر على الزر */}
                        <OverlayPanel 
                            ref={op} 
                            className="w-64" 
                            style={{
                                position: 'absolute', // ✅ تحديد وضع القائمة بشكل مطلق
                                right: '0', // ✅ تحديد أن تكون القائمة على اليمين
                                top: '0', // ✅ تحديد موقع القائمة فوق الزر
                                zIndex: 9999, // ✅ التأكد من ظهور القائمة فوق العناصر الأخرى
                            }}
                            hideOnClickOutside={true} // ✅ إغلاق القائمة عند النقر في أي مكان آخر
                        >
                            <div className="p-3">
                                <h3 className="text-lg font-semibold mb-2">All Permissions</h3>
                                <div className="flex flex-col gap-2"> {/* ✅ عرض الصلاحيات بشكل عمودي */}
                                    {selectedPermissions.map((perm, index) => (
                                        <Tag key={index} value={perm} className="p-tag-info w-full text-center" />
                                    ))}
                                </div>
                            </div>
                        </OverlayPanel>
                    </>
                )}
            </div>
        );
    };

    return (
        <Container>
            <div className="w-full flex justify-center mb-5">
                <div className='w-full'>
                    <h1 className='text-xl font-bold'>Permissions Groups</h1>
                </div>
            </div>

            <div className='table-responsive text-nowrap'>
                <DataTable
                    lazy
                    responsiveLayout="scroll"
                    className="table w-full border"
                    value={groups}
                    loading={isLoading}
                >
                    <Column field="id" header="ID" className='text-center' />
                    <Column field="name" header="Name" className='text-center' />
                    <Column 
                        field="permissions" 
                        header="Permissions" 
                        body={permissionsTemplate} // ✅ استخدام الدالة الجديدة
                        className='text-center' 
                    />
                </DataTable>
            </div>
        </Container>
    );
}

export default PermissionsGroupIndex;
