import React, { useContext, createContext, useEffect, useState, useRef } from "react";
import { useGetManagersDataTable } from "../quires/UsersDatatable";

const ManagerDataTableContext = createContext({});

export const DataTableProvider = ({ children }) => {
    const getDataTable = useGetManagersDataTable();
    const didMountRef = useRef(false);
    const loadLazyTimeout = useRef(null);

    const [reload, setReload] = useState(false);
    const [loading, setLoading] = useState(false);
    const [totalRecords, setTotalRecords] = useState(0);
    const [data, setData] = useState([]);

    const [lazyManagersParams, setLazyManagersParams] = useState({
        url: "",
        first: 0,
        rows: 10,
        page: 0,
        sortField: 'id',
        sortOrder: 1,
        filters: {},
    });

    const updateLazyManagersParams = (event) => {
        setLazyManagersParams(prev => ({ ...prev, ...event }));
    };

    const loadLazyData = () => {
        // Skip URL check since the API query handles the endpoint directly
        setLoading(true);

        if (loadLazyTimeout.current) {
            clearTimeout(loadLazyTimeout.current);
        }

        loadLazyTimeout.current = setTimeout(async () => {
            try {
                console.log("📡 Loading managers data with params:", lazyManagersParams);
                const response = await getDataTable.mutateAsync(lazyManagersParams);
                console.log("📡 Managers data response:", response);
                setTotalRecords(response.pagination.total);
                setData(response.data);
            } catch (err) {
                console.error("❌ Error loading managers data:", err);
                // Optional: handle error
            } finally {
                setLoading(false);
            }
        }, Math.random() * 1000 + 450);
    };

    useEffect(() => {
        setData([]);
        if (didMountRef.current) {
            loadLazyData();
        } else {
            didMountRef.current = true;
        }
    }, [lazyManagersParams]);

    useEffect(() => {
        if (reload) {
            loadLazyData();
            setReload(false);
        }
    }, [reload]);

    useEffect(() => {
        return () => {
            setData([]);
            setReload(false);
            setTotalRecords(0);
        };
    }, []);

    return (
        <ManagerDataTableContext.Provider
            value={{
                totalRecords, setTotalRecords,
                lazyManagersParams, setLazyManagersParams,
                data, setData,
                loading, setLoading,
                setReload,
                getDataTable,
                dataHandler: updateLazyManagersParams,
                onPage: updateLazyManagersParams,
                onSort: updateLazyManagersParams,
                onFilter: updateLazyManagersParams
            }}
        >
            {children}
        </ManagerDataTableContext.Provider>
    );
};

export const useDataTableContext = () => useContext(ManagerDataTableContext);
