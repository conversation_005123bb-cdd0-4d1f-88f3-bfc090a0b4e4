import React, { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom';
import PropTypes from 'prop-types';
import parse from 'html-react-parser';

import { designsTableConfig, defaultTableConfig } from '@constants';
import { useDataTableContext } from '@contexts/DataTableContext';
import { useDeleteTemplate } from '@quires/template';
import { useLayout } from '@contexts/LayoutContext';

import { TfiTrash } from "react-icons/tfi";
import { FiEdit } from 'react-icons/fi';
import { FaSearch } from 'react-icons/fa';
import { HiDotsVertical } from 'react-icons/hi';

import { createPortal } from 'react-dom';
import { confirmDialog, ConfirmDialog } from 'primereact/confirmdialog';
import { Toast } from 'primereact/toast';
import { Dialog } from 'primereact/dialog';

const CARD_WIDTH = 240;
const CARD_HEIGHT = 416;
const CARD_CONTAINER_MAX_WIDTH = 280;
const CARD_CONTAINER_MAX_HEIGHT = 420;

function getScale(containerW, containerH, contentW, contentH) {
    return Math.min(containerW / contentW, containerH / contentH, 1);
}

function TemplatesDataTable() {
    const { totalRecords, lazyParams, setLazyParams, data, dataHandler, loading } = useDataTableContext();
    const { isMobile } = useLayout();
    const navigate = useNavigate();

    const deleteTemplate = useDeleteTemplate()
    const [searchQuery, setSearchQuery] = useState('');
    const [mobileActionMenuOpen, setMobileActionMenuOpen] = useState(null);
    const [toastRef] = useState(React.createRef());
    const [selectedTemplate, setSelectedTemplate] = useState(null);
    const [templateModalVisible, setTemplateModalVisible] = useState(false);
    const [templateZoomLevel, setTemplateZoomLevel] = useState(1.0);

    useEffect(() => {
        setLazyParams({ ...defaultTableConfig, ...designsTableConfig, rows: 12 })
    }, [])

    // Add debounced search handler
    useEffect(() => {
        const timeout = setTimeout(() => {
            setLazyParams(prev => ({
                ...prev,
                url: 'get-designs-list',
                rows: 12,
                filters: {
                    ...prev.filters,
                    name: { value: searchQuery, matchMode: 'contains' }
                }
            }));
        }, 300);

        return () => clearTimeout(timeout);
    }, [searchQuery, setLazyParams]);

    // --- ConfirmDialog handler for delete ---
    const confirmDeleteTemplate = (templateId) => {
        confirmDialog({
            group: 'headless',
            message: 'Are you sure you want to delete this template?',
            header: 'Delete Confirmation',
            icon: 'pi pi-exclamation-triangle',
            acceptClassName: 'p-button-danger',
            acceptLabel: 'Yes',
            rejectLabel: 'No',
            accept: () => handleDeleteConfirmed(templateId),
        });
    };

    // --- Actual delete handler after confirmation ---
    const handleDeleteConfirmed = async (id) => {
        try {
            await deleteTemplate.mutateAsync({ id: id }, {
                onSuccess: () => {
                    setLazyParams(prev => ({ ...prev }));
                    toastRef.current && toastRef.current.show({
                        severity: 'success',
                        summary: 'Success',
                        detail: 'Template deleted successfully',
                        life: 3000
                    });
                },
                onError: () => {
                    toastRef.current && toastRef.current.show({
                        severity: 'error',
                        summary: 'Error',
                        detail: 'Failed to delete template',
                        life: 3000
                    });
                }
            });
        } catch (error) {
            toastRef.current && toastRef.current.show({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to delete template',
                life: 3000
            });
        }
    };

    // Mobile action menu component
    const MobileActionMenu = ({ template, isOpen, onClose }) => {
        if (!isOpen) return null;

        return createPortal(
            <div
                className="fixed inset-0 bg-black bg-opacity-60 z-[9999] flex items-center justify-center"
                style={{
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    backdropFilter: 'blur(2px)'
                }}
                onClick={onClose}
            >
                <div
                    className="bg-white rounded-lg p-4 m-4 w-full max-w-sm relative shadow-2xl"
                    style={{
                        zIndex: 10000,
                        backgroundColor: '#ffffff',
                        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.5)',
                        border: '1px solid rgba(0, 0, 0, 0.1)'
                    }}
                    onClick={(e) => e.stopPropagation()}
                >
                    <div className="flex items-center mb-4 border-b pb-3">
                        <div className="w-10 h-10 rounded-full bg-purple-500 flex items-center justify-center mr-3">
                            <span className="text-white font-bold">{template.name?.charAt(0)?.toUpperCase()}</span>
                        </div>
                        <div>
                            <h3 className="font-semibold">{template.name}</h3>
                            <p className="text-sm text-gray-500">{template.card_type_name}</p>
                        </div>
                    </div>

                    <div className="space-y-2 md:space-y-0 md:flex md:gap-4">
                        <button
                            className="w-full flex items-center p-3 text-left bg-[#18191C]/70 border border-green-500 text-green-400 hover:bg-green-900/30 hover:shadow-[0_0_12px_2px_#22c55e] hover:text-green-300 hover:border-green-400 hover:scale-[1.03] transition-all duration-200 rounded-xl font-bold group"
                            onClick={() => {
                                navigate(`/manager/design-space/${template.id}`);
                                onClose();
                            }}
                        >
                            <FiEdit className="mr-3 text-green-400 group-hover:text-green-300 drop-shadow-[0_0_4px_#22c55e]" size={18} />
                            <span>Edit Template</span>
                        </button>
                        <button
                            className="w-full flex items-center p-3 text-left bg-[#18191C]/70 border border-red-500 text-red-400 hover:bg-red-900/30 hover:shadow-[0_0_12px_2px_#ef4444] hover:text-red-300 hover:border-red-400 hover:scale-[1.03] transition-all duration-200 rounded-xl font-bold group"
                            onClick={() => {
                                confirmDeleteTemplate(template.id);
                                onClose();
                            }}
                        >
                            <TfiTrash className="mr-3 text-red-400 group-hover:text-red-300 drop-shadow-[0_0_4px_#ef4444]" size={18} />
                            <span>Delete Template</span>
                        </button>
                    </div>

                    <button
                        className="w-full mt-4 p-2 bg-gray-200 hover:bg-gray-300 rounded-lg text-center font-medium transition-colors"
                        onClick={onClose}
                    >
                        Cancel
                    </button>
                </div>
            </div>,
            document.body
        );
    };

    // PropTypes validation for MobileActionMenu
    MobileActionMenu.propTypes = {
        template: PropTypes.shape({
            id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
            name: PropTypes.string,
            card_type_name: PropTypes.string,
        }),
        isOpen: PropTypes.bool,
        onClose: PropTypes.func,
    };

    // Mobile list view component
    const MobileListView = () => {
        if (loading) {
            return (
                <div className="space-y-2">
                    {[...Array(5)].map((_, index) => (
                        <div key={index} className="bg-[#23272F] border border-gray-800 rounded-lg p-4 shadow-lg animate-pulse">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center flex-1">
                                    <div className="w-12 h-12 bg-gray-300 rounded-lg mr-3"></div>
                                    <div className="flex-1">
                                        <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                                        <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                                    </div>
                                </div>
                                <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                            </div>
                        </div>
                    ))}
                </div>
            );
        }

        if (!data || data.length === 0) {
            return (
                <div className="text-center py-8">
                    <p className="text-gray-500">No templates found</p>
                </div>
            );
        }

        return (
            <div className="space-y-2">
                {data.map((template) => (
                    <div key={template.id} className="border border-gray-900 rounded-2xl p-0 shadow-[0_8px_32px_0_rgba(0,0,0,0.65),0_1.5px_8px_0_rgba(0,0,0,0.25)] transition-all duration-300 hover:shadow-[0_16px_48px_0_rgba(0,0,0,0.80),0_3px_16px_0_rgba(0,0,0,0.35)] hover:-translate-y-1 group" style={{background: 'linear-gradient(135deg, #2d2d32 0%, #23272F 50%, #111114 100%)'}}>
                        <div className="flex items-center justify-between pl-3 pr-3 pt-4 pb-4">
                            <div className="flex items-center flex-1">
                                <div className="w-12 h-12 rounded-lg bg-purple-500 flex items-center justify-center mr-3">
                                    <span className="text-white font-bold text-lg">
                                        {template.name?.charAt(0)?.toUpperCase()}
                                    </span>
                                </div>
                                <div className="flex-1">
                                    <h3 className="text-2xl font-extrabold text-white tracking-wide drop-shadow-sm mb-1 truncate" style={{letterSpacing: '0.5px'}}>{template.name}</h3>
                                    <p className="text-sm text-gray-400 font-medium mt-1 truncate" style={{letterSpacing: '0.2px'}}>{template.card_type_name}</p>
                                </div>
                            </div>
                            <button
                                className="p-2 hover:bg-gray-800 rounded-full"
                                onClick={() => setMobileActionMenuOpen(template.id)}
                            >
                                <HiDotsVertical className="text-gray-300" size={20} />
                            </button>
                        </div>
                    </div>
                ))}

                {/* Mobile Action Menu */}
                {mobileActionMenuOpen && (
                    <MobileActionMenu
                        template={data.find(t => t.id === mobileActionMenuOpen)}
                        isOpen={!!mobileActionMenuOpen}
                        onClose={() => setMobileActionMenuOpen(null)}
                    />
                )}
            </div>
        );
    };

    // --- Card View for Desktop ---
    const CardListView = () => {
        if (loading) {
            return (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {[...Array(lazyParams?.rows || 5)].map((_, idx) => (
                        <div key={idx} className="bg-[#23272F] border rounded-xl p-6 shadow-lg animate-pulse h-[340px] flex flex-col justify-between" />
                    ))}
                </div>
            );
        }
        if (!data || data.length === 0) {
            return (
                <div className="text-center py-16 text-gray-500 text-lg font-medium">No templates found</div>
            );
        }
        return (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8">
                {data.map((template) => (
                    <div key={template.id} className="border border-gray-900 rounded-2xl shadow-[0_8px_32px_0_rgba(0,0,0,0.65),0_1.5px_8px_0_rgba(0,0,0,0.25)] hover:shadow-[0_16px_48px_0_rgba(0,0,0,0.80),0_3px_16px_0_rgba(0,0,0,0.35)] hover:-translate-y-2 transition-all duration-300 flex flex-col overflow-hidden group" style={{background: 'linear-gradient(135deg, #2d2d32 0%, #23272F 50%, #111114 100%)'}}>
                        <div className="relative group cursor-pointer px-4 pt-4" onClick={() => {
                            setSelectedTemplate(template.template);
                            setTemplateModalVisible(true);
                        }}>
                            {/* Responsive aspect-ratio wrapper */}
                            <div
                                className="relative w-full bg-gradient-to-br from-gray-50 to-gray-200 rounded-xl border border-gray-100 shadow-inner"
                                style={{
                                    aspectRatio: `${CARD_WIDTH} / ${CARD_HEIGHT}`,
                                    maxWidth: `${CARD_CONTAINER_MAX_WIDTH}px`,
                                    maxHeight: `${CARD_CONTAINER_MAX_HEIGHT}px`,
                                    margin: '0 auto',
                                    padding: '12px',
                                    overflow: 'hidden',
                                    position: 'relative',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    height: '100%',
                                    width: '100%',
                                }}
                            >
                                <div
                                    style={{
                                        width: '100%',
                                        height: '100%',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'flex-end',
                                        paddingRight: '45px',
                                         paddingBottom: '90px',
                                    }}
                                >
                                    <div
                                        style={{
                                            width: `${CARD_WIDTH}px`,
                                            height: `${CARD_HEIGHT}px`,
                                            transform: `scale(${getScale(CARD_CONTAINER_MAX_WIDTH - 24, CARD_CONTAINER_MAX_HEIGHT - 24, CARD_WIDTH, CARD_HEIGHT) * 0.5})`,
                                            transformOrigin: 'center center',
                                            pointerEvents: 'none',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                        }}
                                    >
                                        {template.template ? (
                                            <div style={{ width: '100%', height: '100%' }}>{parse(template.template)}</div>
                                        ) : (
                                            <div className="text-gray-400 text-center">No design</div>
                                        )}
                                    </div>
                                </div>
                                {/* Overlay covers the entire preview area, icon is centered absolutely */}
                                <div
                                    className="absolute top-0 left-0 w-full h-full bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 rounded-xl"
                                    style={{ pointerEvents: 'none' }}
                                >
                                    <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                                        <div className="text-white opacity-0 group-hover:opacity-100 transform scale-90 group-hover:scale-100 transition-all duration-300">
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="flex-1 flex flex-col justify-between pt-5 pb-5 pl-3 pr-3">
                            <div>
                                <h3 className="text-2xl font-extrabold text-white tracking-wide drop-shadow-sm mb-1 truncate" style={{letterSpacing: '0.5px'}}>{template.name}</h3>
                                <p className="text-sm text-gray-400 font-medium mt-1 mb-2 truncate" style={{letterSpacing: '0.2px'}}>{template.card_type_name}</p>
                            </div>
                            <div className="flex gap-4 mt-4">
                                <button
                                    className="flex-1 flex items-center justify-center gap-2 px-4 py-2 rounded-xl bg-[#18191C]/70 border border-green-500 text-green-400 font-bold hover:bg-green-900/30 hover:shadow-[0_0_16px_2px_#22c55e] hover:text-green-300 hover:border-green-400 hover:scale-[1.04] transition-all duration-200 group"
                                    onClick={() => navigate(`/manager/design-space/${template.id}`)}
                                >
                                    <FiEdit size={18} className="text-green-400 group-hover:text-green-300 drop-shadow-[0_0_4px_#22c55e]" /> Edit
                                </button>
                                <button
                                    className="flex-1 flex items-center justify-center gap-2 px-4 py-2 rounded-xl bg-[#18191C]/70 border border-red-500 text-red-400 font-bold hover:bg-red-900/30 hover:shadow-[0_0_16px_2px_#ef4444] hover:text-red-300 hover:border-red-400 hover:scale-[1.04] transition-all duration-200 group"
                                    onClick={() => confirmDeleteTemplate(template.id)}
                                >
                                    <TfiTrash size={18} className="text-red-400 group-hover:text-red-300 drop-shadow-[0_0_4px_#ef4444]" /> Delete
                                </button>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        );
    };

    return (
        <div className="w-full h-full flex flex-col">
            <Toast ref={toastRef} position="top-right" />
            <ConfirmDialog group="headless"
                header={null}
                icon={null}
                message={null}
                content={(options) => (
                    <div className="custom-delete-confirm flex flex-col items-center p-7 rounded-2xl border-4 border-[#00ffea] shadow-[0_8px_32px_0_rgba(0,0,0,0.85),0_1.5px_8px_0_rgba(0,0,0,0.35)] border-gray-900"
                        style={{
                            background: '#181A20',
                            minWidth: 320,
                            maxWidth: 400,
                        }}
                    >
                        <i className="pi pi-exclamation-triangle text-6xl mb-4" style={{ color: '#facc15', textShadow: '0 0 16px #facc15aa' }}/>
                        <span className="text-2xl font-extrabold text-white mb-3 text-center drop-shadow-sm" style={{letterSpacing: '0.5px'}}>{options.message}</span>
                        <div className="flex gap-5 mt-2 w-full justify-center">
                            <button
                                className="flex-1 flex items-center justify-center gap-2 px-4 py-2 rounded-xl bg-[#18191C]/70 border border-green-500 text-green-400 font-bold hover:bg-green-900/30 hover:shadow-[0_0_16px_2px_#22c55e] hover:text-green-300 hover:border-green-400 hover:scale-[1.04] transition-all duration-200 group"
                                onClick={options.accept}
                            >
                                نعم
                            </button>
                            <button
                                className="flex-1 flex items-center justify-center gap-2 px-4 py-2 rounded-xl bg-[#18191C]/70 border border-red-500 text-red-400 font-bold hover:bg-red-900/30 hover:shadow-[0_0_16px_2px_#ef4444] hover:text-red-300 hover:border-red-400 hover:scale-[1.04] transition-all duration-200 group"
                                onClick={options.reject}
                            >
                                لا
                            </button>
                        </div>
                    </div>
                )}
            />
            {/* Search Bar Section */}
            <div className={`w-full mb-10 mt-1 ${isMobile ? 'px-2' : 'flex justify-center items-center'}`}>
                <div className={`relative ${isMobile ? 'w-full' : 'flex-grow max-w-[700px]'}`}>
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <FaSearch className="h-4 w-4 text-gray-400" aria-hidden="true" />
                    </div>
                    <input
                        type="text"
                        placeholder="Search by template name..."
                        className="w-full pl-10 pr-4 py-2 border rounded-md shadow-md
                                    focus:outline-none focus:ring-2 focus:ring-blue-300
                                    focus:border-blue-300 transition-all duration-200"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                    />
                </div>
            </div>
            {/* Conditional rendering for mobile vs desktop */}
            <div className="flex-grow h-full">
                {isMobile ? (
                    <MobileListView />
                ) : (
                    <>
                        <CardListView />
                        {/* Pagination Controls */}
                        <div className="flex justify-center mt-8">
                            <div className="flex items-center gap-2">
                                <button
                                    onClick={() => {
                                        const newFirst = Math.max(0, (lazyParams?.first || 0) - (lazyParams?.rows || 12));
                                        const newPage = Math.floor(newFirst / 12);
                                        dataHandler({ first: newFirst, page: newPage, rows: 12, url: 'get-designs-list' });
                                    }}
                                    disabled={!lazyParams?.first || lazyParams.first === 0}
                                    className="px-4 py-2 text-sm bg-white border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                                >
                                    Previous
                                </button>
                                <span className="px-4 py-2 text-sm text-gray-600">
                                    {Math.floor((lazyParams?.first || 0) / (lazyParams?.rows || 12)) + 1} of {Math.ceil(totalRecords / (lazyParams?.rows || 12))}
                                </span>
                                <button
                                    onClick={() => {
                                        const newFirst = (lazyParams?.first || 0) + 12;
                                        const newPage = Math.floor(newFirst / 12);
                                        dataHandler({ first: newFirst, page: newPage, rows: 12, url: 'get-designs-list' });
                                    }}
                                    disabled={(lazyParams?.first || 0) + 12 >= totalRecords}
                                    className="px-4 py-2 text-sm bg-white border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                                >
                                    Next
                                </button>
                            </div>
                        </div>
                        {/* Template Modal */}
                        <Dialog
                            visible={templateModalVisible}
                            onHide={() => {
                                setTemplateModalVisible(false);
                                setTemplateZoomLevel(1.0);
                            }}
                            header={
                                <div className="flex justify-between items-center w-full">
                                    <span className="mr-1">Template Preview</span>
                                </div>
                            }
                            style={{ width: '96vw', maxWidth: '1280px' }}
                            modal
                            className="template-preview-dialog"
                            contentClassName="p-0 overflow-hidden"
                        >
                            <div className="template-preview-container p-0 sm:p-6 bg-white rounded-b-lg flex justify-center items-center overflow-auto min-h-[60vh]" style={{ maxHeight: '80vh' }}>
                                {selectedTemplate && (
                                    <div style={{ width: `${CARD_WIDTH * templateZoomLevel}px`, height: `${CARD_HEIGHT * templateZoomLevel}px`, transition: 'width 0.2s, height 0.2s' }}>
                                        {parse(selectedTemplate)}
                                    </div>
                                )}
                            </div>
                        </Dialog>
                    </>
                )}
            </div>
        </div>
    )
}

export default TemplatesDataTable
