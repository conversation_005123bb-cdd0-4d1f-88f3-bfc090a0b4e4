import React, { useRef } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate } from 'react-router-dom';
import { Toast } from 'primereact/toast';
import { classNames } from 'primereact/utils';
import { InputText } from 'primereact/inputtext';
import { Password } from 'primereact/password';
import SideImage from './SideImage';
import { useRegisterMutation } from '../../quires';
import { getFormErrorMessage } from '@utils/helper';

function Registration() {
  const { t } = useTranslation("auth");
  const { formState: { errors }, handleSubmit, control, setError } = useForm();
  const register = useRegisterMutation();
  const toast = useRef(null);
  const navigate = useNavigate();

  const onSubmit = async (data) => {
    try {
      const response = await register.mutateAsync(data);
      
      // Check if email verification is required
      if (response.email_verification_required) {
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: response.message || 'Registration successful! Please check your email to verify your account.',
          life: 5000
        });
        
        // Redirect to email verification page
        navigate(`/verify-email?email=${encodeURIComponent(data.email)}`);
      } else {
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: 'Account Created Successfully',
          life: 3000
        });
        navigate('/login');
      }
    } catch (error) {
      if (error.response?.data?.details) {
        const serverErrors = error.response.data.details;
        Object.keys(serverErrors).forEach(field => {
          const message = serverErrors[field][0];
          setError(field, {
            type: 'manual',
            message: message,
          });

          if (toast.current) {
            toast.current.show({
              severity: 'error',
              summary: 'Error',
              detail: message,
              life: 3000
            });
          }

        });
      } else {
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: error.response?.data?.error || 'Registration failed',
          life: 3000
        });
      }
    }

  }

  return (
    <div className='w-full md:h-[100vh] overflow-hidden flex'>
      <Toast ref={toast} />
      <SideImage />
      <div className='w-full sm:w-7/12 h-full p-6 md:p-12 flex flex-col justify-center '>
        <h1 className='text-3xl font-bold pb-6 md:mb-12'>{t('registration_title')}</h1>
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-wrap">


          <div className="mb-4 w-full md:w-6/12">
            <div className="field mx-1 ">
              <label className="form-label mb-2 text-[#696F79]">{t('inputs.name')}</label>
              <span className="p-float-label mt-2">
                <Controller name="name" control={control}
                  rules={{ required: t('messages.required') }}
                  render={({ field, fieldState }) => (
                    <InputText
                      id={field.name}
                      {...field}
                      ref={field.ref}
                      placeholder={t('placeholders.name')}
                      className={`w-full text-[#696F79] p-3 ${classNames({ 'p-invalid': fieldState.invalid })}`} />
                  )} />
              </span>
              {getFormErrorMessage('name', errors)}
            </div>
          </div>

          <div className="mb-4 w-full md:w-6/12">
            <div className="field mx-2">
              <label className="form-label mb-2 text-[#696F79]">{t('inputs.phone')}</label>
              <span className="p-float-label mt-2">
                <Controller name="phone" control={control}
                  rules={{ required: t('messages.required') }}
                  render={({ field, fieldState }) => (
                    <InputText
                      id={field.name}
                      {...field}
                      ref={field.ref}
                      placeholder={t('placeholders.phone')}
                      className={`w-full text-[#696F79] p-3 ${classNames({ 'p-invalid': fieldState.invalid })}`} />
                  )} />
              </span>
              {getFormErrorMessage('phone', errors)}
            </div>
          </div>

          <div className="mb-4 w-full md:w-6/12">
            <div className="field mx-2">
              <label className="form-label mb-2 text-[#696F79]">{t('inputs.company_name')}</label>
              <span className="p-float-label mt-2">
                <Controller name="company" control={control}
                  rules={{ required: t('messages.required') }}
                  render={({ field, fieldState }) => (
                    <InputText
                      id={field.name}
                      {...field}
                      ref={field.ref}
                      placeholder={t('placeholders.company_name')}
                      className={`w-full text-[#696F79] p-3 ${classNames({ 'p-invalid': fieldState.invalid })}`} />
                  )} />
              </span>
              {getFormErrorMessage('company', errors)}
            </div>
          </div>

          <div className="mb-4 w-full md:w-6/12">
            <div className="field mx-2">
              <label className="form-label mb-2 text-[#696F79]">{t('inputs.company_address')}</label>
              <span className="p-float-label mt-2">
                <Controller name="Address" control={control}
                  rules={{ required: t('messages.required') }}
                  render={({ field, fieldState }) => (
                    <InputText
                      id={field.name}
                      {...field}
                      ref={field.ref}
                      placeholder={t('placeholders.company_address')}
                      className={`w-full text-[#696F79] p-3 ${classNames({ 'p-invalid': fieldState.invalid })}`} />
                  )} />
              </span>
              {getFormErrorMessage('Address', errors)}
            </div>
          </div>

          <div className="mb-4 w-full md:w-6/12">
            <div className="field mx-2">
              <label className="form-label mb-2 text-[#696F79]">{t('inputs.email')}</label>
              <span className="p-float-label mt-2">
                <Controller name="email" control={control}
                  rules={{
                    required: t('messages.required'),
                    pattern: {
                      value: /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
                      message: t('messages.email'),
                    }
                  }}
                  render={({ field, fieldState }) => (
                    <InputText
                      id={field.email}
                      {...field}
                      ref={field.ref}
                      placeholder={t('placeholders.email')}
                      className={`w-full text-[#696F79] p-3 ${classNames({ 'p-invalid': fieldState.invalid })}`} />
                  )} />
              </span>
              {getFormErrorMessage('email', errors)}
            </div>
          </div>

          <div className="mb-4 form-password-toggle w-full md:w-6/12">
            <div className="field mx-2">
              <label className="form-label mb-2 text-[#696F79]" htmlFor="password">{t('inputs.password')}</label>
              <span className="p-float-label mt-2">
                <Controller name="password" control={control}
                  rules={{ required: t('messages.required') }}
                  render={({ field, fieldState }) => (
                    <Password
                      id={field.password}
                      {...field}
                      ref={field.ref}
                      placeholder={t('placeholders.password')}
                      className={`text-[#696F79] pass-input w-full ${classNames({ 'p-invalid': fieldState.invalid })}`}
                      toggleMask />
                  )} />
              </span>
              {getFormErrorMessage('password', errors)}
            </div>
          </div>

          <button className="main-btn w-full mt-8 text-md sm:text-xl">{t('register')}</button>
        </form>

        <p className="mt-3 fs-8 text-[#696F79] text-sm">
          {t('go_to.login')}
          <Link to="/login">
            <span className="mx-1 capitalize text-[#427bf0]">{t('login')}</span>
          </Link>
        </p>
      </div>
    </div>
  )
}

export default Registration;;