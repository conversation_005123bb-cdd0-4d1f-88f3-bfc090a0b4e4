import { Route, Routes } from 'react-router-dom';

import { DesignSpaceProvider } from '@contexts/DesignSpaceContext';

import SettingsIndex from '@dashboard/Setting';
import MembersIndex from '@dashboard/Users';
import MyBackagesIndex from '../pages/Dashboard/Backages/MyBackagesIndex';
import OrginalBackagesIndex from '../pages/Dashboard/Backages/OrginalBackagesIndex';
import SoldBackagesIndex from '../pages/Dashboard/Backages/SoldBackagesIndex';
import CardsIndex from '@dashboard/Cards';
import AllCardsIndex from '@dashboard/All_Cards';
import GroupMembersPage from '../pages/Dashboard/Users/<USER>/GroupMembersPage';
import BillingHistory from '../pages/Dashboard/Billing';

import EventsIndex from '@dashboard/Events';
import DesignIndex from '@dashboard/DesignSpace';
import PermissionsGroupIndex from '@dashboard/permissions_group';
import Templates from '@dashboard/Templates';
import Dashboard from '@dashboard';

import Companies from '@pages/Admin/Companies';

import Layout from '@pages/Layout';

function CompanyRoutes() {
    return (
        <Routes>
            <Route path='/manager/dashboard' element={<Layout><Dashboard /></Layout>} />         {/*Overview*/}
            <Route path='/manager/settings' element={<Layout><SettingsIndex /></Layout>} />      {/*Re-Enabled as of 6th of May for the users/managers to use*/}
            <Route path='/users/:type?' element={<Layout><MembersIndex /></Layout>} />           {/*Members*/}
            <Route path='/manager/users/groups' element={<Layout><></></Layout>} />              {/*Groups page*/}
            <Route path="/members/group" element={ <Layout><GroupMembersPage /></Layout> } />
            <Route path='/manager/events' element={<Layout><EventsIndex /></Layout>} />
            <Route path='/manager/permissions_group' element={<Layout><PermissionsGroupIndex/></Layout>} />
            <Route path='/manager/companies' element={<Layout><Companies /></Layout>} />
            <Route path='/manager/Packages' element={<Layout><OrginalBackagesIndex /></Layout>} />
            <Route path='/manager/my_cards' element={<Layout><MyBackagesIndex /></Layout>} />       {/*Page for both cards and purchased packages*/}
            <Route path='/manager/cards' element={<Layout><AllCardsIndex /></Layout>} />
            <Route path='/manager/billing' element={<Layout><BillingHistory /></Layout>} />


            <Route path='/manager/design-space/:id?' element={
                    <DesignSpaceProvider>
                        <DesignIndex />
                    </DesignSpaceProvider>

            } />

            <Route path='/manager/template-design' element={
                <Layout>
                    <DesignSpaceProvider>
                        <Templates />
                    </DesignSpaceProvider>
                </Layout>
            } />
        </Routes>
    )
}

export default CompanyRoutes               //This should really be renamed manager Routes instead of "company". Kinda confusing
