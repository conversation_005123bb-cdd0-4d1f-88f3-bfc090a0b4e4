import { useState } from 'react';
import ImageEditSidebar from './ImageEditSidebar';
import AIToolsHub from './AIToolsHub';
import { TabView, TabPanel } from 'primereact/tabview';
import { FaImage } from 'react-icons/fa';
import { MdOutlinePhotoFilter } from 'react-icons/md';

const tabData = [
    {
        key: 'manual',
        icon: <FaImage className="mr-1" />,
        label: 'Manual',
    },
    {
        key: 'style',
        icon: <MdOutlinePhotoFilter className="mr-1" />,
        label: 'Style',
    },

];

const ImageEditorPanel = () => {
    const [activeTab, setActiveTab] = useState(0);
    return (
        <div className="image-editor-panel h-full flex flex-col bg-white">
            <div className="flex-1 overflow-hidden">
                <TabView 
                    activeIndex={activeTab} 
                    onTabChange={e => setActiveTab(e.index)} 
                    className="h-full image-editor-tabview"
                >
                    <TabPanel 
                        header={<span className="flex items-center">{tabData[0].icon}{tabData[0].label}</span>} 
                        className="h-full image-editor-tabpanel"
                    >
                        <div className="h-full overflow-y-auto">
                            <ImageEditSidebar />
                        </div>
                    </TabPanel>
                    <TabPanel 
                        header={<span className="flex items-center">{tabData[1].icon}{tabData[1].label}</span>} 
                        className="h-full image-editor-tabpanel"
                    >
                        <div className="h-full overflow-y-auto">
                            <AIToolsHub onlyImageTab={false} onlyTextTab={false} />
                        </div>
                    </TabPanel>
                </TabView>
            </div>
        </div>
    );
};

export default ImageEditorPanel;

// إضافة CSS مخصص لإصلاح مشكلة الـ scroll
const imageEditorStyles = `
.image-editor-panel {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  background-color: white !important;
}

.image-editor-panel .flex-1 {
  flex: 1 !important;
  overflow: hidden !important;
}

.image-editor-tabview {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

.image-editor-tabview .p-tabview-panels {
  flex: 1 !important;
  overflow: hidden !important;
  height: 100% !important;
}

.image-editor-tabview .p-tabview-panel {
  height: 100% !important;
  overflow: hidden !important;
  padding: 0 !important;
}

.image-editor-tabpanel {
  height: 100% !important;
  overflow: hidden !important;
}

.image-editor-tabpanel > div {
  height: 100% !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
}

/* إصلاح مشكلة الـ scroll في PrimeReact TabView */
.image-editor-tabview .p-tabview-nav {
  flex-shrink: 0 !important;
}

.image-editor-tabview .p-tabview-nav-container {
  flex-shrink: 0 !important;
}

.image-editor-tabview .p-tabview-nav-content {
  flex-shrink: 0 !important;
}

.image-editor-tabview .p-tabview-nav-btn {
  flex-shrink: 0 !important;
}

/* ضمان عمل الـ scroll بشكل صحيح */
.image-editor-tabpanel > div::-webkit-scrollbar {
  width: 6px !important;
}

.image-editor-tabpanel > div::-webkit-scrollbar-track {
  background: #f1f1f1 !important;
  border-radius: 3px !important;
}

.image-editor-tabpanel > div::-webkit-scrollbar-thumb {
  background: #c1c1c1 !important;
  border-radius: 3px !important;
}

.image-editor-tabpanel > div::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8 !important;
}

/* إصلاح مشكلة الـ height في PrimeReact */
.image-editor-tabview .p-tabview-panels {
  min-height: 0 !important;
}

.image-editor-tabview .p-tabview-panel {
  min-height: 0 !important;
}

/* ضمان عدم تداخل المحتوى مع الـ tabs */
.image-editor-tabview .p-tabview-nav {
  border-bottom: 1px solid #dee2e6 !important;
  background-color: #f8f9fa !important;
}

.image-editor-tabview .p-tabview-nav-link {
  padding: 0.75rem 1rem !important;
  border: none !important;
  background: transparent !important;
  color: #6c757d !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.image-editor-tabview .p-tabview-nav-link:hover {
  background-color: #e9ecef !important;
  color: #495057 !important;
}

.image-editor-tabview .p-tabview-nav-link.p-highlight {
  background-color: #007bff !important;
  color: white !important;
  border-bottom: 2px solid #0056b3 !important;
}

/* تحسين مظهر الأيقونات */
.image-editor-tabview .p-tabview-nav-link svg {
  margin-right: 0.5rem !important;
  font-size: 1rem !important;
}

/* إصلاح مشكلة الـ flex في المحتوى */
.image-editor-tabpanel > div > * {
  height: auto !important;
  min-height: 100% !important;
}
`;

// إضافة الـ styles إلى الـ document
if (typeof document !== 'undefined') {
    const styleId = 'image-editor-styles';
    let existingStyle = document.getElementById(styleId);
    
    if (!existingStyle) {
        const styleElement = document.createElement('style');
        styleElement.id = styleId;
        styleElement.textContent = imageEditorStyles;
        document.head.appendChild(styleElement);
    }
} 