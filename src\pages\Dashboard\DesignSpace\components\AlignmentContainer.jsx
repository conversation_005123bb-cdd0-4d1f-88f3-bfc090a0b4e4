function AlignmentContainer({ alignmentLines }) {
    const isVLineTypeSolid = alignmentLines?.vertical?.type === "solid";
    const isHLineType = alignmentLines?.horizontal?.type === "solid";

    return (
        <>
            {
                alignmentLines.vertical && (
                    <div
                        className="absolute"
                        style={{
                            left: alignmentLines.vertical.position,
                            top: 0,
                            bottom: 0,
                            width: "1px",
                            backgroundColor: isVLineTypeSolid ? "red" : "blue",
                            opacity: 0.5,
                            borderStyle: isVLineTypeSolid ? "solid" : "dashed",
                        }}
                    >
                    </div>
                )}
            {
                alignmentLines.horizontal && (
                    <div
                        className="absolute"
                        style={{
                            top: alignmentLines.horizontal.position,
                            left: 0,
                            right: 0,
                            height: "1px",
                            backgroundColor: isHLineType ? "red" : "blue",
                            opacity: 0.5,
                            borderStyle:isHLineType ? "solid" : "dashed",
                        }}
                    >
                    </div>
                )}
        </>
    )
}

export default AlignmentContainer;