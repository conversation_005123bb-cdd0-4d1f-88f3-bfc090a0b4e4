import React, { useEffect, useState } from "react";

import { companiesTableConfig, defaultTableConfig } from "@constants";
import { useDataTableContext } from "@contexts/DataTableContext";
import { useGlobalContext } from "@contexts/GlobalContext";
import Container from "@components/Container";
import Button from "@components/Button";
import axiosInstance from "../../../config/Axios";
import { FaRegEye } from 'react-icons/fa';

import { DataTable } from "primereact/datatable";
import { Tooltip } from "primereact/tooltip";
import { Column } from "primereact/column";
import { FiEdit } from "react-icons/fi";
import { Image } from "primereact/image";
import { Link } from 'react-router-dom'

import CompanyDialog from "./CompanyDialog";

export default function Companies() {
  const { dialogHandler, openDialog } = useGlobalContext();
  // const [companies, setCompanies] = useState([]);
  const [totalCompanyRecords, setTotalCompanyRecords] = useState(0);
  const [loading, setLoading] = useState(false);
  const [lazyParams, setLazyParams] = useState({
  ...defaultTableConfig,
  ...companiesTableConfig,
});

  const [selectedRow, setSelectedRow] = useState(null);
  const [actionType, setActionType] = useState("create");

  const [allCompanies, setAllCompanies] = useState([]); 
  const [displayedCompanies, setDisplayedCompanies] = useState([]);

  //Delete member Handler
  const confirmDelete = (data) => {
    setSelectedRow(data);
    deleteDialogHandler("member");
  };
  const packageNameBodyTemplate = (rowData) => {
    return rowData?.package_name ?? "No Package";
  };

  //Dialog Handler
  const createRow = () => {
    setActionType("create");
    setSelectedRow({});
    dialogHandler("companyDialog");
  };

  const editRow = (data) => {
    setActionType("update");
    setSelectedRow(data);
    dialogHandler("companyDialog");
  };

  useEffect(() => {
    setLazyParams({ ...defaultTableConfig, ...companiesTableConfig });
  }, []);

  const getCompanies = async () => {
    try {
        console.log("Fetching ALL companies (backend pagination broken)...");
        setLoading(true);
        // Send params even if ignored, because I have no idea how the backend operates 
        const response = await axiosInstance.get("/companies", { 
            params: { 
                // You might only send filter/sort params here now
                filters: lazyParams.filters,
                sortField: lazyParams.sortField,
                sortOrder: lazyParams.sortOrder
             } 
        });
        console.log("API Response (Full List):", response.data);

        if (Array.isArray(response.data)) {
            // Process package name 
            const companiesWithPackage = response.data.map((company) => ({
                ...company,
                packageName: company.owner?.package?.name ?? "No Package",
            }));
            setAllCompanies(companiesWithPackage); // Store the full list
            setTotalCompanyRecords(companiesWithPackage.length); // Set total based on full list
        } 

        setLoading(false);
    } catch (error) {
        console.error("Error:", error);
        setLoading(false);
    }
};

// Fetch data initially (Like on site/page launch)
useEffect(() => {
    getCompanies();
}, []); // Fetch only once on mount (need to adjust if server-side filters/sorts DO work)  ##TODO @ Backend


useEffect(() => {
  if (!allCompanies || allCompanies.length === 0) {
      setDisplayedCompanies([]);
      return;
  }

  setLoading(true); // Indicate processing

  let processedCompanies = [...allCompanies];

  // 1. Client-Side Filtering (Example - needs robust implementation based on lazyParams.filters)
  // This is complex! You need to parse lazyParams.filters and apply logic.
  // Example: if (lazyParams.filters?.name?.value) {
  //   processedCompanies = processedCompanies.filter(c => 
  //       c.name.toLowerCase().includes(lazyParams.filters.name.value.toLowerCase()));
  // }
  // Add logic for all filterable columns...

  // 2. Client-Side Sorting (Example)
  if (lazyParams.sortField) {
      processedCompanies.sort((a, b) => {
          const valueA = a[lazyParams.sortField];
          const valueB = b[lazyParams.sortField];
          let comparison = 0;
          if (valueA > valueB) {
              comparison = 1;
          } else if (valueA < valueB) {
              comparison = -1;
          }
          return lazyParams.sortOrder === 1 ? comparison : comparison * -1;
      });
  }

  // 3. Client-Side Pagination (Slicing)
  const first = lazyParams.first || 0;
  const rows = lazyParams.rows || 10; // Use default if needed
  const slicedCompanies = processedCompanies.slice(first, first + rows);

  setDisplayedCompanies(slicedCompanies); // Update the table display
  // Update total records based on the *filtered* list length if filtering is client-side
  // setTotalCompanyRecords(processedCompanies.length); 
  setLoading(false);

}, [allCompanies, lazyParams]); // Rerun when data or params change

const onTableChange = (event) => {
  console.log("Table event triggered:", event);
  setLazyParams(prev => ({
      ...prev,
      first: event.first,
      rows: event.rows,
      sortField: event.sortField || prev.sortField,
      sortOrder: event.sortOrder || prev.sortOrder,
      filters: event.filters || prev.filters
  }));
  // No need to call getCompanies here anymore since fetch works initially
};

  // Data Table Body Template
  const actionBodyTemplate = (rowData) => {
    return (
      <div className="d-inline-block text-nowrap">
        {/* preview   */}
        <Tooltip
          target={`.delete-button-${rowData.id}`}
          showDelay={100}
          className="fs-8"
        />
        <Link to={`/${rowData.owner_id}/packages-history`}>
          <button
            className={`btn btn-sm btn-icon delete-button-${rowData.id} me-4`}
            data-pr-position="bottom"
            data-pr-tooltip="Groups Members"
          >
            <FaRegEye size={20} />
          </button>
        </Link>

        {/* Edit */}
        <Tooltip
          target={`.update-button-${rowData.id}`}
          showDelay={100}
          className="fs-8"
        />
        <button
          className={`btn btn-sm btn-icon update-button-${rowData.id} me-3`}
          data-pr-position="bottom"
          data-pr-tooltip="Update"
          onClick={() => editRow(rowData)}
        >
          <FiEdit />
        </button>

        {/* Delete  */}
        {/* <Tooltip target={`.delete-button-${rowData.id}`} showDelay={100} className="fs-8" />
        <button
          className={`btn btn-sm btn-icon userType delete-button-${rowData.id}`}
          data-pr-position="bottom"
          data-pr-tooltip="Delete"
          onClick={() => confirmDeleteMember(rowData)} >
          <TfiTrash />
        </button> */}
      </div>
    );
  };

  const statusBodyTemplate = (rowData) => {
    let statusClass = "";
  
    if (rowData?.status === "active") {
      statusClass = "bg-[#22C55E]";
    } else if (rowData?.status === "No Package") {
      statusClass = "bg-[#6B7280]";
    } else {
      statusClass = "bg-[#DC2626]"; 
    }
  
    return (
      <span
        className={`text-white rounded-[6px] font-bold text-sm py-2 px-3 capitalize  ${statusClass} min-w-[100px] inline-block text-center `}
      >
        {rowData?.status}
      </span>
    );
  };
  
  

  const Header = () => {
    return (
      <div className="w-full flex justify-end">
        <Button actionHandler={createRow} title="Add New Company" />
      </div>
    );
  };

  const logoBodyTemplate = (rowData) => {
    return <Image src={rowData.company_logo} alt="Image" width="100" preview />;
  };

  return (
    <Container>
      <div className="w-full flex justify-center mb-5">
        <div className="w-full">
          <h1 className="text-xl font-bold">Companies</h1>
        </div>
      </div>
      <div className="w-full h-full   ">
        <div className="flex-grow">
          <DataTable
            lazy
            // filterDisplay="row"
            header={Header}
            responsiveLayout="stack"
            breakpoint="960px"
            dataKey="id"
            paginator
            className="table w-full border "
            value={displayedCompanies}
            first={lazyParams?.first}
            rows={lazyParams?.rows}
            rowsPerPageOptions={[5, 25, 50, 100]}
            totalRecords={totalCompanyRecords}
            onPage={onTableChange}
            onSort={onTableChange}
            sortField={lazyParams?.sortField}
            sortOrder={lazyParams?.sortOrder}
            onFilter={onTableChange}
            filters={lazyParams?.filters}
            loading={loading}
            paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
            currentPageReportTemplate="Showing {first} to {last} of {totalRecords} products"
            scrollable
            scrollHeight="100%"
          >
            <Column body={logoBodyTemplate} header="Logo" />
            <Column field="name" header="Company Name" filter />
            <Column field="owner_name" header="Owner Name" filter />

            <Column
              field="package_name"
              header="Package Name"
              sortable
              filter
              showFilterMenu={false}
            />

            {/* <Column
              field="description"           //No Need for a description field in the table
              header="Description"
              filter
              showFilterMenu={false}
            /> */}
            <Column
              field="card_limit"
              header="Card Limit"
              filter
              sortable
              showFilterMenu={false}
            />
            <Column
              field="card_types"
              header="Type"
              filter
              sortable
              showFilterMenu={false}
            />
            <Column
              field="package_status"
              body={statusBodyTemplate}
              header="Status"
              showFilterMenu={false}
              filter
              sortable
            />

            <Column
              body={actionBodyTemplate}
              exportable={false}
              style={{ minWidth: "8rem" }}
            ></Column>
          </DataTable>
        </div>
      </div>
      {openDialog?.companyDialog ? (
        <CompanyDialog actionType={actionType} data={selectedRow} />
      ) : (
        <></>
      )}
    </Container>
  );
}
