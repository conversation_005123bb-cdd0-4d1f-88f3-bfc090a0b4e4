import { TabMenu } from 'primereact/tabmenu';
import { useEffect, useState } from 'react';

import { PiUsersFourFill, PiUsersThreeFill } from "react-icons/pi";
import { useNavigate, useParams } from 'react-router-dom';

function TypesTabMenu({ activeTap, setActiveTap }) {
    const { type } = useParams();
    const navigate = useNavigate();
    const userRole = localStorage.getItem('user_role');

    const items = [
        {
            label: 'Members',
            icon: <PiUsersThreeFill size={20} className='me-2' />,
            command: () => navigate("/users/members")

        },

       
            ...(userRole !== "user" ? [{
                label: 'Groups',
                icon: <PiUsersFourFill size={20} className='me-2' />,
                command: () => navigate("/users/groups")
            }] : [])
        
        
    ];

    useEffect(() => {
        let _active = type && type === "groups" ? 1 : 0
        setActiveTap(_active)
    }, [type])

    // return (
    //     <TabMenu
    //         model={items}
    //         activeIndex={activeTap}
    //         onTabChange={(e) => setActiveTap(e.index)}   //Commented out the groups/members tabs feature for managers due to redundancy, since there are side menu tabs for that
    //         className='types-tab-menu'
    //     />
    // )
}

export default TypesTabMenu