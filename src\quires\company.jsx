import { useMutation, useQuery } from 'react-query';
import axiosInstance from '../config/Axios';

import { companiesTableConfig } from '@constants';
import { useDataTableContext } from '@contexts/DataTableContext';
import { useGlobalContext } from '@contexts/GlobalContext';
import { handleErrors } from '@utils/helper';

//--------------Create Company-------------- //
const createCompany = async (payload) => {
    const { data } = await axiosInstance.post("/companies", payload);

    return data.data;
}

export const useCreateCompanyMutation = () => {
    const { showToast, dialogHandler } = useGlobalContext();
    const { setLazyParams } = useDataTableContext();

    return useMutation(createCompany, {
        onSuccess: async () => {
            setLazyParams(prev => ({ ...prev, ...companiesTableConfig }))
            dialogHandler("companyDialog")
            showToast("success", "Success", "Company created successfully!")
        },
        onError: (error) => {
            handleErrors(showToast, error)
        }
    })
}

//--------------Update Company-------------- //
const updateCompany = async (payload) => {
    const { data } = await axiosInstance.post(`/companies/${payload?.id}`, payload.data);

    return data.data;
}

export const useUpdateCompanyMutation = () => {
    const { showToast, dialogHandler } = useGlobalContext();
    const { setLazyParams } = useDataTableContext();

    return useMutation(updateCompany, {
        onSuccess: async () => {
            setLazyParams(prev => ({ ...prev, ...companiesTableConfig }))
            dialogHandler("companyDialog")
            showToast("success", "Success", "Company updated successfully!")
        },
        onError: (error) => {
            handleErrors(showToast, error)
        }
    })
}

//--------------Get Companies-------------- //
const getCompanies = async () => {
    const { data } = await axiosInstance.get(`/companies`);
    console.log(" new Response Data:", data);  
    return data;
}

export const useGetCompanies = () => {
    const { showToast } = useGlobalContext();
    let { isLoading, data, error, isError } = useQuery('getCompanies', getCompanies);

    if (isError) {
        showToast("error", "Fetch Types ", error.response?.data?.message)
    }

    return { isLoading, data };
}
