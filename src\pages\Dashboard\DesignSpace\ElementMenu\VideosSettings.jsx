import React, { useState } from 'react';
import { useDesignSpace } from '@contexts/DesignSpaceContext';

// Video Templates
import {
  videoThumbnailTemplates,
  videoIntroTemplates
} from '@constants/TemplatesData.videos';

const VideosSettings = () => {
    const { setElements } = useDesignSpace();
    const [activeCategory, setActiveCategory] = useState('all');
    const [searchTerm, setSearchTerm] = useState('');

    // Filter templates based on category and search term
    const filteredTemplates = () => {
        let templates = [];

        switch(activeCategory) {
            case 'thumbnails':
                templates = videoThumbnailTemplates;
                break;
            case 'intros':
                templates = videoIntroTemplates;
                break;
            default:
                templates = [...videoThumbnailTemplates, ...videoIntroTemplates];
        }

        // Filter by search term if provided
        if (searchTerm) {
            templates = templates.filter(template =>
                template.name.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        return templates;
    };

    // Apply template
    const applyTemplate = (template) => {
        setElements(template.elements);
    };

    return (
        <div className="canva-videos">
            <h3 className="text-lg font-semibold mb-4 text-gray-800">Video Templates</h3>

            {/* Search */}
            <div className="mb-4">
                <div className="relative">
                    <input
                        type="text"
                        placeholder="Search video templates..."
                        className="w-full p-2 pr-8 border border-gray-200 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <div className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                </div>
            </div>

            {/* Categories */}
            <div className="mb-4">
                <h4 className="text-sm font-medium mb-2 text-gray-700">Categories</h4>
                <div className="flex flex-wrap gap-2">
                    {[
                        { id: 'all', name: 'All Videos' },
                        { id: 'thumbnails', name: 'Thumbnails' },
                        { id: 'intros', name: 'Intros' },
                    ].map(category => (
                        <button
                            key={category.id}
                            onClick={() => setActiveCategory(category.id)}
                            className={`px-3 py-1 text-xs rounded-full ${
                                activeCategory === category.id
                                ? 'bg-purple-600 text-white'
                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                            }`}
                        >
                            {category.name}
                        </button>
                    ))}
                </div>
            </div>

            {/* Templates Grid */}
            <div className="grid grid-cols-2 gap-2">
                {filteredTemplates().slice(0, 10).map(template => (
                    <div
                        key={template.id}
                        className="rounded-lg overflow-hidden shadow-sm cursor-pointer hover:shadow-md transition-all duration-200 border border-gray-200"
                        onClick={() => applyTemplate(template)}
                    >
                        <div className="aspect-video bg-gray-100 relative">
                            <img
                                src={template.thumbnail}
                                alt={template.name}
                                className="w-full h-full object-cover"
                            />
                            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent h-8"></div>
                            <div className="absolute bottom-1 left-1 right-1">
                                <p className="text-xs font-medium truncate text-white">{template.name}</p>
                            </div>
                        </div>
                    </div>
                ))}

                {filteredTemplates().length === 0 && (
                    <div className="col-span-2 py-6 text-center text-gray-500">
                        <div className="text-2xl mb-2">🔍</div>
                        <div className="font-medium text-sm">No video templates found</div>
                        <div className="text-xs mt-1">Try a different search term or category</div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default VideosSettings;
