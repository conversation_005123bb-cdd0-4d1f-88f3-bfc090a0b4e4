import { useState, useEffect } from "react";
import { Dialog } from "primereact/dialog";
import { InputText } from "primereact/inputtext";
import { InputNumber } from "primereact/inputnumber";
import { Button } from "primereact/button";
import { Toast } from 'primereact/toast';
import { useRef } from 'react';
import { Dropdown } from "primereact/dropdown";

const CreateTypeForm = ({
  isModalOpen,
  setIsModalOpen,
  fetchCardTypes,
  editData,
  isEditMode,
  resetEditMode
}) => {
  const [formData, setFormData] = useState({
    name: "",
    setting: {
      width: "",
      height: ""
    },
    number_of_colors: 1,
    type_of_connection:"Bluetooth"
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const toast = useRef(null);

  const backendUrl = import.meta.env.VITE_BACKEND_URL;
  const token = localStorage.getItem("token");

  // Set form data when editData changes
  useEffect(() => {
    if (isEditMode && editData) {
      setFormData({
        name: editData.name,
        setting: {
          width: editData.setting?.width || "",
          height: editData.setting?.height || ""
        },
        number_of_colors: editData.number_of_colors || 1,
        type_of_connection: editData.type_of_connection || ""
      });
    } else {
      setFormData({
        name: "",
        setting: {
          width: "",
          height: ""
        },
        number_of_colors: 1,
       

      });
    }
  }, [editData, isEditMode]);

  const handleChange = (name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSettingChange = (key, value) => {
    setFormData(prev => ({
      ...prev,
      setting: {
        ...prev.setting,
        [key]: value
      }
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setErrors({});

    // Basic validation
    const newErrors = {};
    if (!formData.name) newErrors.name = "Name is required";
    if (!formData.setting.width) newErrors.width = "Width is required";
    if (!formData.setting.height) newErrors.height = "Height is required";
    if (formData.number_of_colors < 1 || formData.number_of_colors > 10) {
      newErrors.number_of_colors = "Number of colors must be between 1 and 10";
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    const requestData = {
      name: formData.name,
      setting: {
        width: formData.setting.width,
        height: formData.setting.height
      },
      number_of_colors: formData.number_of_colors,
      type_of_connection: formData.type_of_connection

    };

    let requestUrl, method;
    if (isEditMode) {
      requestUrl = `${backendUrl}/card-types/${editData.id}`;
      method = "PUT";
    } else {
      requestUrl = `${backendUrl}/addCardType`;
      method = "POST";
    }

    try {
      setLoading(true);
      const response = await fetch(requestUrl, {
        method: method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(requestData),
      });

      const data = await response.json();

      if (response.ok) {
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: isEditMode 
            ? 'Card type updated successfully' 
            : 'Card type created successfully',
          life: 3000
        });
        setFormData({
          name: "",
          setting: {
            width: "",
            height: ""
          },
          number_of_colors: 1
        });
        setIsModalOpen(false);
        if (fetchCardTypes) fetchCardTypes();
        if (isEditMode && resetEditMode) resetEditMode();
      } else {
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: data.message || (isEditMode 
            ? 'Failed to update card type' 
            : 'Failed to create card type'),
          life: 3000
        });
        if (data.errors) {
          setErrors(data.errors);
        }
      }
    } catch (error) {
      console.error("Error:", error);
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'An error occurred',
        life: 3000
      });
    } finally {
      setLoading(false);
    }
  };

  const onHide = () => {
    setIsModalOpen(false);
    if (isEditMode && resetEditMode) resetEditMode();
  };

  return (
    <>
      <Toast ref={toast} />
      <Dialog
        header={isEditMode ? "Edit Card Type" : "Create New Card Type"}
        visible={isModalOpen}
        onHide={onHide}
        style={{ width: '50vw' }}
        breakpoints={{ '960px': '75vw', '641px': '90vw' }}
        modal
      >
        <form onSubmit={handleSubmit} className="p-fluid space-y-4">
          <div className="p-field">
            <label htmlFor="name">Name</label>
            <InputText
              id="name"
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
              className={errors.name ? 'p-invalid' : ''}
            />
            {errors.name && <small className="p-error">{errors.name}</small>}
          </div>

          <div className="p-field">
            <label htmlFor="width">Width (px)</label>
            <InputText
              id="width"
              value={formData.setting.width}
              onChange={(e) => handleSettingChange('width', e.target.value)}
              className={errors.width ? 'p-invalid' : ''}
            />
            {errors.width && <small className="p-error">{errors.width}</small>}
          </div>

          <div className="p-field">
            <label htmlFor="height">Height (px)</label>
            <InputText
              id="height"
              value={formData.setting.height}
              onChange={(e) => handleSettingChange('height', e.target.value)}
              className={errors.height ? 'p-invalid' : ''}
            />
            {errors.height && <small className="p-error">{errors.height}</small>}
          </div>

          <div className="p-field">
          <label htmlFor="type_of_connection">Type of Connection</label>
          <Dropdown
            id="type_of_connection"
            value={formData.type_of_connection}
            options={[
              { label: 'Bluetooth', value: 'Bluetooth' },
              { label: 'NFC', value: 'NFC' }
            ]}
            onChange={(e) => handleChange('type_of_connection', e.value)}
            placeholder="Select a connection type"
            className={errors.type_of_connection ? 'p-invalid' : ''}
          />
          {errors.type_of_connection && (
            <small className="p-error">{errors.type_of_connection}</small>
          )}
        </div>


          <div className="p-field">
            <label htmlFor="number_of_colors">Number of Colors</label>
            <InputNumber
              id="number_of_colors"
              value={formData.number_of_colors}
              onValueChange={(e) => handleChange('number_of_colors', e.value)}
              min={1}
              max={10}
              className={errors.number_of_colors ? 'p-invalid' : ''}
            />
            {errors.number_of_colors && <small className="p-error">{errors.number_of_colors}</small>}
          </div>

          <div className="flex justify-end gap-2 mt-4">
            <Button
              label="Cancel"
              icon="pi pi-times"
              className="p-button-text"
              onClick={onHide}
            />
            <Button
              label={isEditMode ? "Update" : "Create"}
              icon={isEditMode ? "pi pi-check" : "pi pi-plus"}
              type="submit"
              loading={loading}
            />
          </div>
        </form>
      </Dialog>
    </>
  );
};

export default CreateTypeForm;