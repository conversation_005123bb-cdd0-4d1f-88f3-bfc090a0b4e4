import React, { useContext, createContext, useEffect, useState, useRef } from "react";
import { useGetDataTable } from "@quires";

const DataTableContext = createContext({});

export const DataTableProvider = (props) => {
    const getDataTable = useGetDataTable();

    let loadLazyTimeout = null;
    const didMountRef = useRef(null)

    const [reload, setReload] = useState(false);
    const [loading, setLoading] = useState(false);
    const [totalRecords, setTotalRecords] = useState(0);
    const [data, setData] = useState();

    const [lazyParams, setLazyParams] = useState({
        url: "",
        first: 0,
        rows: 10,
        page: 0,
        sortField: 'id',
        sortOrder: 1,
        filters: {},
    });

    const onPage = (event) => {
        event = { ...lazyParams, ...event }
        setLazyParams(event);
    }

    const onSort = (event) => {
        event = { ...lazyParams, ...event }
        setLazyParams(event);
    }

    const onFilter = (event) => {
        event = { ...lazyParams, ...event }
        setLazyParams(event);
    }

    const dataHandler = (event) => {
        event = { ...lazyParams, ...event }
        setLazyParams(event);
    }
    

    const loadLazyData = () => {
        if (lazyParams.url) {
            setLoading(true);

            if (loadLazyTimeout) {
                clearTimeout(loadLazyTimeout);
            }

            //imitate delay of a backend call
            loadLazyTimeout = setTimeout(async () => {
                await getDataTable.mutateAsync(lazyParams, {
                    onSuccess: async (data) => {
                        setTotalRecords(data.pagination.total);
                        setData(data.data);
                        setLoading(false);
                    },
                    onError : () => {
                        setLoading(false);
                    }
                })
            }, Math.random() * 1000 + 450);
        }
    }

    useEffect(() => {
        setData([])
        if (!didMountRef.current) {
            didMountRef.current = true;
        } else {
            loadLazyData();
        }
    }, [lazyParams])

    useEffect(() => {
        if (reload) {
            loadLazyData()
        }
        setReload(false)
    }, [reload])

    useEffect(() => {
        return () => {
            setData([])
            setReload(false)
            setTotalRecords(0)
        }
    }, [])

    return (
        <DataTableContext.Provider value={{
            totalRecords, setTotalRecords,
            lazyParams, setLazyParams,
            data, setData,
            loading, setLoading,
            setReload,
            getDataTable,
            dataHandler,
            onPage,
            onSort,
            onFilter
        }}>
            {props.children}
        </DataTableContext.Provider>
    )
}

export const useDataTableContext = () => {
    return useContext(DataTableContext);
}