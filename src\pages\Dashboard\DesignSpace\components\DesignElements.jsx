import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import {
    IoShapesOutline,
    IoColorPaletteOutline,
    IoImageOutline,
    IoTextOutline,
    IoLayersOutline,
    IoGridOutline,
    IoResizeOutline,
    IoEllipseOutline,
    IoSquareOutline,
    IoTriangleOutline
} from 'react-icons/io5';
import {
    FaPencilRuler,
    FaPalette,
    FaRulerCombined,
    FaShapes,
    FaFont,
    FaImage
} from 'react-icons/fa';
import {
    MdOutlinePhotoLibrary,
    MdOutlineFormatColorFill,
    MdOutlineTextFields,
    MdOutlineGradient,
    MdOutlineFilter,
    MdOutlinePhotoFilter,
    MdOutlinePhotoCamera,
    MdOutlinePhotoSizeSelectActual
} from 'react-icons/md';
import {
    RxText,
    RxImage,
    RxPencil2,
    RxRulerHorizontal,
    RxRulerSquare,
    RxScissors,
    RxMagicWand,
    RxCrop,
    RxDesktop,
    RxMobile
} from 'react-icons/rx';
import {
    TbIcons,
    TbBrush,
    TbPalette,
    TbColorPicker,
    TbCircle,
    TbSquare,
    TbTriangle,
    TbHexagon,
    TbStar,
    TbArrowRight,
    TbArrowLeft,
    TbArrowUp,
    TbArrowDown
} from 'react-icons/tb';
import { BiChart, BiCrop, BiRectangle, BiCircle, BiSquare } from 'react-icons/bi';

// Design elements to be displayed randomly on the background
const DesignElements = () => {
    // Generate random positions for elements
    const generateElements = () => {
        const elements = [];
        const icons = [
            <IoShapesOutline />, <IoColorPaletteOutline />, <IoImageOutline />, <IoTextOutline />,
            <IoLayersOutline />, <IoGridOutline />, <IoResizeOutline />, <IoEllipseOutline />,
            <IoSquareOutline />, <IoTriangleOutline />, <FaPencilRuler />, <FaPalette />,
            <FaRulerCombined />, <FaShapes />, <FaFont />, <FaImage />, <MdOutlinePhotoLibrary />,
            <MdOutlineFormatColorFill />, <MdOutlineTextFields />, <MdOutlineGradient />,
            <MdOutlineFilter />, <MdOutlinePhotoFilter />, <MdOutlinePhotoCamera />,
            <MdOutlinePhotoSizeSelectActual />, <RxText />, <RxImage />, <RxPencil2 />,
            <RxRulerHorizontal />, <RxRulerSquare />, <RxScissors />, <RxMagicWand />,
            <RxCrop />, <RxDesktop />, <RxMobile />, <TbIcons />, <TbBrush />,
            <TbPalette />, <TbColorPicker />, <TbCircle />, <TbSquare />, <TbTriangle />,
            <TbHexagon />, <TbStar />, <TbArrowRight />, <TbArrowLeft />, <TbArrowUp />,
            <TbArrowDown />, <BiChart />, <BiCrop />, <BiRectangle />, <BiCircle />, <BiSquare />
        ];

        // Generate shapes
        const shapes = [
            { type: 'circle', size: 20 },
            { type: 'square', size: 20 },
            { type: 'triangle', size: 20 },
            { type: 'rectangle', width: 30, height: 15 },
            { type: 'line', length: 30, angle: 45 }
        ];

        // Generate 30 random elements
        for (let i = 0; i < 30; i++) {
            const x = Math.random() * 100; // % position
            const y = Math.random() * 100; // % position
            const size = Math.random() * 20 + 10; // 10-30px
            const opacity = Math.random() * 0.15 + 0.05; // 0.05-0.2
            const rotation = Math.random() * 360; // 0-360 degrees
            const delay = Math.random() * 5; // 0-5s delay
            const duration = Math.random() * 10 + 10; // 10-20s duration
            const iconIndex = Math.floor(Math.random() * icons.length);
            const shapeIndex = Math.floor(Math.random() * shapes.length);

            // Randomly choose between icon and shape
            const elementType = Math.random() > 0.5 ? 'icon' : 'shape';

            elements.push({
                id: `element-${i}`,
                x,
                y,
                size,
                opacity,
                rotation,
                delay,
                duration,
                elementType,
                icon: icons[iconIndex],
                shape: shapes[shapeIndex]
            });
        }

        return elements;
    };

    const elements = generateElements();

    return (
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
            {elements.map((element) => (
                <motion.div
                    key={element.id}
                    className="absolute"
                    style={{
                        left: `${element.x}%`,
                        top: `${element.y}%`,
                        opacity: element.opacity,
                        color: 'rgba(255, 255, 255, 0.3)',
                        fontSize: `${element.size}px`,
                        zIndex: 0
                    }}
                    initial={{ opacity: 0, rotate: 0 }}
                    animate={{
                        opacity: [element.opacity, element.opacity * 1.5, element.opacity],
                        rotate: [element.rotation, element.rotation + 10, element.rotation - 10, element.rotation],
                        x: [0, 10, -10, 0],
                        y: [0, -10, 10, 0]
                    }}
                    transition={{
                        duration: element.duration,
                        delay: element.delay,
                        repeat: Infinity,
                        repeatType: "reverse"
                    }}
                >
                    {element.elementType === 'icon' ? (
                        React.cloneElement(element.icon, { size: element.size })
                    ) : (
                        <div className="relative">
                            {element.shape.type === 'circle' && (
                                <div
                                    className="rounded-full border border-white/30"
                                    style={{
                                        width: `${element.shape.size}px`,
                                        height: `${element.shape.size}px`
                                    }}
                                />
                            )}
                            {element.shape.type === 'square' && (
                                <div
                                    className="border border-white/30"
                                    style={{
                                        width: `${element.shape.size}px`,
                                        height: `${element.shape.size}px`
                                    }}
                                />
                            )}
                            {element.shape.type === 'triangle' && (
                                <div
                                    className="border-l border-r border-b border-white/30"
                                    style={{
                                        width: `${element.shape.size}px`,
                                        height: `${element.shape.size}px`,
                                        clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)'
                                    }}
                                />
                            )}
                            {element.shape.type === 'rectangle' && (
                                <div
                                    className="border border-white/30"
                                    style={{
                                        width: `${element.shape.width}px`,
                                        height: `${element.shape.height}px`
                                    }}
                                />
                            )}
                            {element.shape.type === 'line' && (
                                <div
                                    className="border-t border-white/30"
                                    style={{
                                        width: `${element.shape.length}px`,
                                        transform: `rotate(${element.shape.angle}deg)`,
                                        transformOrigin: 'left center'
                                    }}
                                />
                            )}
                        </div>
                    )}
                </motion.div>
            ))}
        </div>
    );
};

export default DesignElements;
