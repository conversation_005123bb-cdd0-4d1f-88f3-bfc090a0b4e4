import React, { useState, useRef } from 'react';
import { Dialog } from 'primereact/dialog';
import { Toast } from 'primereact/toast';
import { ProgressBar } from 'primereact/progressbar';
import { FaFileExcel, FaUpload } from 'react-icons/fa';
import axiosInstance from "../../../../config/Axios";

const ImportDialog = ({ visible, onHide, onImportSuccess }) => {
    const [file, setFile] = useState(null);
    const [uploading, setUploading] = useState(false);
    const [progress, setProgress] = useState(0);
    const toast = useRef(null);
    const fileInputRef = useRef(null);

    const handleFileChange = (e) => {
        const selectedFile = e.target.files[0];
        if (selectedFile) {
            const allowedTypes = ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv'];
            if (allowedTypes.includes(selectedFile.type)) {
                setFile(selectedFile);
            } else {
                toast.current.show({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Please upload a valid Excel or CSV file',
                    life: 3000
                });
            }
        }
    };

    const handleImport = async () => {
        if (!file) {
            toast.current.show({
                severity: 'warn',
                summary: 'Warning',
                detail: 'Please select a file first',
                life: 3000
            });
            return;
        }

        setUploading(true);
        setProgress(0);

        const formData = new FormData();
        formData.append('file', file);

        try {
            const token = localStorage.getItem('token');
            const response = await axiosInstance.post('users/import', formData, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'multipart/form-data'
                },
                onUploadProgress: (progressEvent) => {
                    const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                    setProgress(percentCompleted);
                }
            });

            toast.current.show({
                severity: 'success',
                summary: 'Success',
                detail: 'Users imported successfully',
                life: 3000
            });

            onImportSuccess();
            onHide();
        } catch (error) {
            console.error('Import error:', error);
            toast.current.show({
                severity: 'error',
                summary: 'Error',
                detail: error.response?.data?.message || 'Import failed',
                life: 3000
            });
        } finally {
            setUploading(false);
            setProgress(0);
            setFile(null);
            if (fileInputRef.current) {
                fileInputRef.current.value = '';
            }
        }
    };

    const footer = (
        <div>
            <button 
                onClick={onHide} 
                className="p-button p-component p-button-text mr-2"
                disabled={uploading}
            >
                Cancel
            </button>
            <button 
                onClick={handleImport} 
                className="p-button p-component"
                disabled={uploading || !file}
            >
                <FaUpload className="mr-2" />
                Import
            </button>
        </div>
    );

    return (
        <>
            <Toast ref={toast} position="top-right" />
            <Dialog 
                header={
                    <div className="flex items-center">
                        <FaFileExcel className="mr-2 text-green-600" size={20} />
                        <span>Import Users from Excel</span>
                    </div>
                }
                visible={visible} 
                style={{ width: '40vw' }} 
                footer={footer}
                onHide={onHide}
                closable={!uploading}
            >
                <div className="p-fluid">
                    <div className="mb-4">
                        <p className="mb-2 text-gray-600">Select an Excel file to import:</p>
                        <input 
                            type="file" 
                            accept=".xlsx,.xls,.csv"
                            onChange={handleFileChange}
                            ref={fileInputRef}
                            disabled={uploading}
                            className="block w-full text-sm text-gray-500
                                file:mr-4 file:py-2 file:px-4
                                file:rounded-md file:border-0
                                file:text-sm file:font-semibold
                                file:bg-blue-50 file:text-blue-700
                                hover:file:bg-blue-100"
                        />
                        <small className="text-gray-500">Supported formats: .xlsx, .xls, .csv</small>
                    </div>

                    {file && (
                        <div className="mb-4 p-3 border rounded bg-gray-50">
                            <div className="flex justify-between items-center">
                                <span className="font-medium">{file.name}</span>
                                <span className="text-sm text-gray-500">{(file.size / 1024 / 1024).toFixed(2)} MB</span>
                            </div>
                        </div>
                    )}

                    {uploading && (
                        <div className="mt-4">
                            <ProgressBar value={progress} showValue={false} style={{ height: '6px' }} />
                            <div className="flex justify-between mt-1 text-sm text-gray-600">
                                <span>Uploading...</span>
                                <span>{progress}%</span>
                            </div>
                        </div>
                    )}
                </div>
            </Dialog>
        </>
    );
};

export default ImportDialog;