import { useEffect } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";

import { useUpdateGroupMutation } from '@quires';
import { getFormErrorMessage } from '@utils/helper'
import { groupStatusOptions } from "@constants/group";
import { useGlobalContext } from '@contexts/GlobalContext';

import { RadioButton } from 'primereact/radiobutton';
import { classNames } from 'primereact/utils';
import { InputText } from 'primereact/inputtext';
import { Button } from 'primereact/button';
import { InputTextarea } from "primereact/inputtextarea";
import { useNavigate } from "react-router-dom";
import { Dropdown } from "primereact/dropdown";


function UpdateGroupForm() {
    const { control, formState: { errors }, handleSubmit, reset, watch } = useForm();
    const { openDialog, dialogHandler, disableBtn, selectedMembers, setSelectedMembers } = useGlobalContext();
    const updateGroup = useUpdateGroupMutation();
    const navigate = useNavigate();

    const submitHandler = async (formData) => {
        const userIds = selectedMembers.data.map(item => item.id);
        await updateGroup.mutateAsync(
            {
                ...formData,
                user_ids: userIds,
                delete_old_users_group: true
            },
            {
                onSuccess: () => {
                    reset();
                    navigate("/users/groups");
                    setSelectedMembers({
                        groupData: {},
                        action: "create",
                        data: [],
                    });
                }
            })
    }

    // const deleteOldUser = watch("delete_old_users_group")

    useEffect(() => {
        reset({
            ...selectedMembers?.groupData,
            "delete_old_users_group": "no"
        })
    }, [reset])

    return (

        <form onSubmit={handleSubmit(submitHandler)} className="w-full flex flex-col justify-center">
            <div className="col-full flex flex-wrap  justify-start py-4 border-[gray]">
                {/* Name */}
                {/* <div className='w-6/12 mb-3 px-2'>
                    <div className="field ">
                        <label className="form-label text-sm"> Name </label>
                        <span className="p-float-label">
                            <Controller name="name" control={control}
                                rules={{ required: 'Name is required.' }}
                                render={({ field, fieldState }) => (
                                    <InputText
                                        id={field.name}
                                        {...field}
                                        autoFocus
                                        ref={field.ref}
                                        className={`w-100  ${classNames({ 'p-invalid': fieldState.invalid })}`}
                                    />
                                )} />
                        </span>
                        {getFormErrorMessage('name', errors)}
                    </div>
                </div> */}

                {/* Title */}
                <div className='w-full mb-3 px-2'>
                    <div className="field ">
                        <label className="form-label text-sm"> Title </label>
                        <span className="p-float-label">
                            <Controller name="title" control={control}
                                rules={{ required: 'Title is required.' }}
                                render={({ field, fieldState }) => (
                                    <InputText
                                        id={field.name}
                                        {...field}
                                        ref={field.ref}
                                        className={`w-100  ${classNames({ 'p-invalid': fieldState.invalid })}`}
                                    />
                                )} />
                        </span>
                        {getFormErrorMessage('title', errors)}
                    </div>
                </div>

                {/* Description */}
                <div className='w-full mb-3 px-2'>
                    <div className="field ">
                        <label className="form-label text-sm"> Description </label>
                        <span className="p-float-label">
                            <Controller name="description" control={control}
                                rules={{ required: 'Description is required.' }}
                                render={({ field, fieldState }) => (
                                    <InputTextarea
                                        rows={3}
                                        cols={30}
                                        id={field.name}
                                        {...field}
                                        ref={field.ref}
                                        className={`w-100  ${classNames({ 'p-invalid': fieldState.invalid })}`}
                                    />
                                )} />
                        </span>
                        {getFormErrorMessage('description', errors)}
                    </div>
                </div>

                {/* Status */}
                <div className='w-full mb-3 px-2'>
                    <label htmlFor="" className='form-label text-sm'>Status</label>
                    <Controller name="status" control={control}
                        rules={{ required: false }}
                        render={({ field, fieldState }) => (
                            <Dropdown
                                id={field.name} {...field}
                                value={field.value}
                                options={groupStatusOptions}
                                onChange={(e) => { field.onChange(e.value); }}
                                optionLabel="label"
                                optionValue="value"
                                ref={field.ref}
                                placeholder="select..."
                                className={`w-100 ${classNames({ 'p-invalid': fieldState.invalid })}`}
                            />
                        )
                        } />
                    {getFormErrorMessage('status', errors)}
                </div>

                {/* Type
                    <div className='w-6/12 mb-3 px-2'>
                        <label htmlFor="" className='form-label text-sm'>Type</label>
                        <Controller name="type" control={control}
                            rules={{ required: "type is required!" }}
                            render={({ field, fieldState }) => (
                                <Dropdown
                                    id={field.name} {...field}
                                    value={field.value}
                                    options={typeOptions}
                                    onChange={(e) => { field.onChange(e.value) }}
                                    optionLabel="label"
                                    optionValue="value"
                                    ref={field.ref}
                                    placeholder="select..."
                                    className={`w-100 ${classNames({ 'p-invalid': fieldState.invalid })}`}
                                />
                            )
                            } />
                        {getFormErrorMessage('type', errors)}
                    </div> */}


            </div>

            <div className="col-full text-center flex items-end justify-start px-24 py-4">
                <Button
                    label="Cancel"
                    aria-label="Close"
                    type="reset"
                    className="gray-btn w-3/12  me-2 text-center"
                    disabled={disableBtn || updateGroup.isLoading}
                    data-bs-dismiss="modal"
                    onClick={() => dialogHandler("updateGroup")} />

                <Button
                    label="Update"
                    aria-label="Add"
                    type="submit"
                    className="main-btn w-auto  ms-2 text-center"
                    disabled={disableBtn}
                    loading={updateGroup.isLoading}
                />
            </div>
        </form>
    )
}

export default UpdateGroupForm