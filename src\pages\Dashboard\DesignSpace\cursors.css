/* Professional cursor styles for design space */

/* Base cursor for resize handles */
.resize-handle {
  cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="white" stroke="black" stroke-width="1"><circle cx="12" cy="12" r="6" fill="white" stroke="black" stroke-width="1.5"/><circle cx="12" cy="12" r="2" fill="black"/></svg>') 12 12, auto;
}

/* Simple rotation mode cursor */
.resize-handle.rotation-mode {
  cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="%238a3dff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M23 4v6h-6"></path><path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10"></path></svg>') 12 12, auto !important;
}

/* Top-left resize cursor */
.resize-handle[style*="top-left"] {
  cursor: nwse-resize;
}

/* Top-right resize cursor */
.resize-handle[style*="top-right"] {
  cursor: nesw-resize;
}

/* Bottom-left resize cursor */
.resize-handle[style*="bottom-left"] {
  cursor: nesw-resize;
}

/* Bottom-right resize cursor */
.resize-handle[style*="bottom-right"] {
  cursor: nwse-resize;
}

/* Dragging cursor for elements */
.draggable-element {
  cursor: grab;
}

.draggable-element:active {
  cursor: grabbing;
}

/* Rotation handle cursor */
.rotation-handle {
  cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none"><circle cx="16" cy="16" r="14" fill="rgba(0, 196, 204, 0.2)" stroke="rgba(0, 196, 204, 0.9)" stroke-width="1.5"/><path d="M16,6 A10,10 0 0,1 26,16" stroke="rgba(0, 196, 204, 0.9)" stroke-width="2" fill="none" stroke-linecap="round"/><path d="M26,16 L23,13 M26,16 L23,19" stroke="rgba(0, 196, 204, 0.9)" stroke-width="2" fill="none" stroke-linecap="round"/></svg>') 16 16, auto;
}
