import { Route, Routes, Navigate } from 'react-router-dom';
import { useState, useEffect } from 'react';

import { DesignSpaceProvider } from '@contexts/DesignSpaceContext';

import DesignIndex from '@dashboard/DesignSpace';
import CardsIndex from '@dashboard/Cards';
import Templates from '@dashboard/Templates';
import Dashboard from '@dashboard';
import Layout from '@pages/Layout';
import MembersIndex from '@dashboard/Users';


function UserRoutes() {  
    const [userRole, setUserRole] = useState(localStorage.getItem("user_role"));

    if (!userRole) {
        return <Navigate to='/login' replace />;
    }
    return (
        <Routes>
            {/* <Route path='/user/dashboard' element={<Layout><Dashboard /></Layout>} />

            <Route path='/user/design-space/:id?' element={
                <Layout>
                    <DesignSpaceProvider>
                        <DesignIndex />
                    </DesignSpaceProvider>
                </Layout>
            } />
            <Route path='/user/template-design' element={
                <Layout>
                    <DesignSpaceProvider>
                        <Templates />
                    </DesignSpaceProvider>
                </Layout>
            } /> */}
        </Routes>
    );
}

export default UserRoutes;
