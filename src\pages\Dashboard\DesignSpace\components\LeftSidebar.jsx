import React, { useState, useEffect, useRef } from 'react';
import { FiChevronLeft, FiChevronRight } from 'react-icons/fi';
import { motion, AnimatePresence } from 'framer-motion';
import PropTypes from 'prop-types';

// Icons
import { RxText, RxImage } from 'react-icons/rx';
import { BsQrCode } from 'react-icons/bs';
import { TbIcons } from 'react-icons/tb';
import { MdOutlinePhotoLibrary, MdOutlineUpload, MdOutlinePhotoFilter } from 'react-icons/md';
import { IoShapesOutline, IoColorPaletteOutline } from 'react-icons/io5';

// Components
import TextSettings from '../ElementMenu/TextSettings';
import ImageSettings from '../ElementMenu/ImageSettings';
import IconsSettings from '../ElementMenu/IconsSettings';
import QrCodeSettings from '../ElementMenu/QrCodeSettings';
import ElementsSettings from '../ElementMenu/ElementsSettings';
import BackgroundSettings from '../ElementMenu/BackgroundSettings';
import ImageEditorPanel from './ImageEditorPanel';
import UploadsSettings from '../ElementMenu/UploadsSettings';

const LeftSidebar = ({ isMobile = false }) => {
    const [activeTab, setActiveTab] = useState(null);
    const [sidebarWidth, setSidebarWidth] = useState(280); // Increased from 240px to 280px
    const [contentWidth, setContentWidth] = useState(500); // Increased from 380px to 500px
    const [isDragging, setIsDragging] = useState(false);
    const [startX, setStartX] = useState(0);
    const [startWidth, setStartWidth] = useState(0);
    const [isHovered, setIsHovered] = useState(null);
    const [isCollapsed, setIsCollapsed] = useState(false);
    const sidebarRef = useRef(null);

    // Define tool categories
    const toolCategories = {
        design: "Design",
        content: "Content",
        media: "Media"
    };

    // Track expanded/collapsed categories
    const [expandedCategories, setExpandedCategories] = useState({
        design: true,
        content: true,
        media: true
    });

    const tools = [
        // Design Category
        // { id: 'templates', label: 'Templates', icon: <FaRegFileAlt size={22} />, component: <TemplatesSettings />, category: 'design' },
        { id: 'elements', label: 'Elements', icon: <IoShapesOutline size={22} />, component: <ElementsSettings />, category: 'design' },
        { id: 'backgrounds', label: 'Backgrounds', icon: <IoColorPaletteOutline size={22} />, component: <BackgroundSettings />, category: 'design' },

        // Content Category
        { id: 'text', label: 'Text', icon: <RxText size={22} />, component: <TextSettings />, category: 'content' },
        { id: 'icons', label: 'Icons', icon: <TbIcons size={22} />, component: <IconsSettings />, category: 'content' },
        { id: 'qr', label: 'QR Code', icon: <BsQrCode size={22} />, component: <QrCodeSettings />, category: 'content' },

        // Media Category
        // { id: 'photos', label: 'Photos', icon: <MdOutlinePhotoLibrary size={22} />, component: <PhotosSettings />, category: 'media' },
        // { id: 'videos', label: 'Videos', icon: <MdOutlineVideoLibrary size={22} />, component: <VideosSettings />, category: 'media' },
        { id: 'images', label: 'Images', icon: <RxImage size={22} />, component: <ImageSettings />, category: 'media' },
        { id: 'uploads', label: 'Uploads', icon: <MdOutlineUpload size={22} />, component: <UploadsSettings />, category: 'media' },
        
        { id: 'image-editor', label: 'Image Editor', icon: <MdOutlinePhotoFilter size={22} />, component: <ImageEditorPanel />, category: 'media' },
    ];

    // Toggle active tab with animation
    const toggleTab = (tabId) => {
        if (activeTab === tabId) {
            setActiveTab(null);
        } else {
            setActiveTab(tabId);
        }
    };

    // Get tools by category
    const getToolsByCategory = (category) => {
        return tools.filter(tool => tool.category === category);
    };

    // Handle tool hover
    const handleToolHover = (id) => {
        setIsHovered(id);
    };

    // Handle tool leave
    const handleToolLeave = () => {
        setIsHovered(null);
    };

    // Toggle category expansion
    const toggleCategory = (category) => {
        setExpandedCategories(prev => ({
            ...prev,
            [category]: !prev[category]
        }));
    };

    // Handle resize start
    const handleResizeStart = (e, type) => {
        setIsDragging(true);
        setStartX(e.clientX);

        if (type === 'sidebar') {
            setStartWidth(sidebarWidth);
        } else if (type === 'content') {
            setStartWidth(contentWidth);
        }

        // Add event listeners
        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
    };

    // Handle mouse move during resize
    const handleMouseMove = (e) => {
        if (!isDragging) return;

        const deltaX = e.clientX - startX;

        // If dragging sidebar resizer
        if (startWidth === sidebarWidth) {
            const newWidth = Math.max(90, Math.min(400, startWidth + deltaX));
            setSidebarWidth(newWidth);
        }
        // If dragging content resizer
        else if (startWidth === contentWidth) {
            const newWidth = Math.max(300, Math.min(800, startWidth + deltaX));
            setContentWidth(newWidth);
        }
    };

    // Handle mouse up to end resize
    const handleMouseUp = () => {
        setIsDragging(false);
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
    };

    // Clean up event listeners
    useEffect(() => {
        return () => {
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
        };
    }, [isDragging]);

    // استقبال إشارة إغلاق جميع القوائم من DesignSpace
    useEffect(() => {
        const handleCloseTabs = () => {
            console.log('تم استقبال حدث إغلاق القوائم، activeTab الحالي:', activeTab);
            setActiveTab(null);
            
            // إغلاق جميع الفئات المفتوحة أيضاً
            setExpandedCategories({
                design: false,
                content: false,
                media: false
            });
            
            console.log('تم إغلاق جميع القوائم والفئات');
        };
        document.addEventListener('closeSidebarTabs', handleCloseTabs);
        return () => document.removeEventListener('closeSidebarTabs', handleCloseTabs);
    }, [activeTab]);

    // استقبال إشارة فتح القائمة المناسبة من DesignSpace
    useEffect(() => {
        const handleOpenTab = (event) => {
            const { tabId } = event.detail;
            
            // التحقق من أن القائمة موجودة
            const toolExists = tools.find(tool => tool.id === tabId);
            if (toolExists) {
                setActiveTab(tabId);
                
                // تأكد من أن الفئة المطلوبة مفتوحة
                const toolCategory = toolExists.category;
                if (!expandedCategories[toolCategory]) {
                    setExpandedCategories(prev => ({
                        ...prev,
                        [toolCategory]: true
                    }));
                }
            }
        };

        // إضافة مستمع الحدث
        document.addEventListener('openSidebarTab', handleOpenTab);

        // تنظيف المستمع عند إزالة المكون
        return () => {
            document.removeEventListener('openSidebarTab', handleOpenTab);
        };
    }, [tools, expandedCategories]);

    // Collapse/Expand sidebar
    const handleCollapseToggle = () => {
        if (!isCollapsed) {
            setSidebarWidth(60); // عرض مصغر
            setIsCollapsed(true);
        } else {
            setSidebarWidth(280); // عرض افتراضي
            setIsCollapsed(false);
        }
    };

    // Mobile layout - horizontal scrolling toolbar at bottom
    if (isMobile) {
        return (
            <div className="w-full bg-gradient-to-r from-gray-900 to-gray-800 p-2 mobile-bottom-toolbar" ref={sidebarRef}>
                {/* Mobile horizontal tool bar */}
                <div className="flex items-center justify-start space-x-4 overflow-x-auto mobile-toolbar custom-scrollbar pb-2">
                    {tools.map((tool) => (
                        <motion.button
                            key={tool.id}
                            className={`mobile-tool-button mobile-touch-target flex flex-col items-center justify-center p-3 rounded-lg min-w-[60px] ${
                                activeTab === tool.id
                                    ? 'bg-purple-600 text-white active'
                                    : 'text-gray-400 hover:text-gray-200 hover:bg-gray-700'
                            }`}
                            onClick={() => toggleTab(tool.id)}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            title={tool.label}
                        >
                            <div className="mb-1">
                                {React.cloneElement(tool.icon, { size: 20 })}
                            </div>
                            <span className="text-xs font-medium text-center leading-tight">
                                {tool.label}
                            </span>
                        </motion.button>
                    ))}
                </div>

                {/* Mobile content panel - slides up when tool is selected */}
                <AnimatePresence>
                    {activeTab && (
                        <>
                            {/* Mobile overlay */}
                            <motion.div
                                className="fixed inset-0 bg-black bg-opacity-50 z-40"
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                exit={{ opacity: 0 }}
                                onClick={() => setActiveTab(null)}
                            />

                            <motion.div
                                className="mobile-content-panel mobile-slide-up fixed inset-x-0 bottom-0 bg-white border-t border-gray-200 shadow-2xl z-50"
                                style={{ height: '70vh', maxHeight: '600px' }}
                                initial={{ y: '100%' }}
                                animate={{ y: 0 }}
                                exit={{ y: '100%' }}
                                transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                            >
                            {/* Mobile content header */}
                            <div className="p-4 border-b border-gray-200 flex items-center justify-between bg-gray-50">
                                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                                    {tools.find(tool => tool.id === activeTab)?.icon &&
                                        React.cloneElement(tools.find(tool => tool.id === activeTab).icon, { size: 20, className: "mr-2" })
                                    }
                                    {tools.find(tool => tool.id === activeTab)?.label}
                                </h3>
                                <motion.button
                                    className="mobile-touch-target text-gray-500 hover:text-gray-700 p-2 rounded-full bg-gray-200 hover:bg-gray-300"
                                    whileHover={{ scale: 1.1 }}
                                    whileTap={{ scale: 0.95 }}
                                    onClick={() => setActiveTab(null)}
                                >
                                    <FiChevronLeft size={20} />
                                </motion.button>
                            </div>

                            {/* Mobile content body */}
                            <div className="mobile-content-body p-4 overflow-y-auto custom-scrollbar" style={{ height: 'calc(70vh - 80px)' }}>
                                {tools.find(tool => tool.id === activeTab)?.component}
                            </div>
                        </motion.div>
                        </>
                    )}
                </AnimatePresence>
            </div>
        );
    }

    // Desktop layout - original sidebar design
    return (
        <div className="flex h-full" ref={sidebarRef}>
            {/* Tools sidebar - Professional Design */}
            <motion.div
                className="bg-gradient-to-b from-gray-900 to-gray-800 h-full flex flex-col items-center py-0 relative"
                style={{ width: `${sidebarWidth}px` }}
                initial={{ width: sidebarWidth }}
                animate={{ width: sidebarWidth }}
                transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            >
                {/* زر السهم في الأعلى، ثم فاصل، ثم زر الإعدادات */}
                <div className={`w-full flex flex-col py-3 border-b border-gray-700 gap-2 ${isCollapsed ? 'items-center' : 'items-start'}`}>
                    {/* زر السهم والعنوان */}
                    <div className="relative flex items-center w-full h-12">
                        {/* العنوان */}
                        {!isCollapsed && (
                            <span className="text-lg font-semibold text-white mx-auto block">Toolbox</span>
                        )}
                        <motion.button
                            className="bg-white border border-gray-300 shadow transition-colors w-10 h-10 flex items-center justify-center rounded-full cursor-pointer text-white hover:bg-cyan-500 hover:shadow-neumorph absolute right-2 top-2"
                            style={{ boxShadow: '4px 4px 12px #d1d9e6, -4px -4px 12px #ffffff' }}
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={handleCollapseToggle}
                            title={isCollapsed ? 'توسيع الشريط' : 'تصغير الشريط'}
                        >
                            {isCollapsed ? <FiChevronRight size={22} /> : <FiChevronLeft size={22} />}
                        </motion.button>
                    </div>
                </div>

                {/* Tool categories with dividers */}
                <div className="w-full flex-1 overflow-y-auto custom-scrollbar">
                    {Object.keys(toolCategories).map((category, categoryIndex) => (
                        <div key={category} className="mb-4">
                            {/* Category header with gradient background */}
                            <div
                                className="px-5 py-3 bg-gradient-to-r from-gray-800 to-gray-700 border-y border-gray-600 cursor-pointer"
                                onClick={() => toggleCategory(category)}
                            >
                                <div className="text-sm text-gray-200 uppercase tracking-wider font-semibold flex items-center">
                                    {/* Category icon based on category name with animation */}
                                    <motion.span
                                        className="mr-3 bg-gray-700/50 p-1.5 rounded-md"
                                        whileHover={{
                                            backgroundColor: '#6d28d9',
                                            scale: 1.1,
                                            transition: { duration: 0.3 }
                                        }}
                                        whileTap={{ scale: 0.95 }}
                                    >
                                        {category === 'design' && (
                                            <div>
                                                <IoShapesOutline size={16} />
                                            </div>
                                        )}
                                        {category === 'content' && (
                                            <div>
                                                <RxText size={16} />
                                            </div>
                                        )}
                                        {category === 'media' && (
                                            <div>
                                                <MdOutlinePhotoLibrary size={16} />
                                            </div>
                                        )}
                                    </motion.span>
                                    {!isCollapsed && toolCategories[category]}

                                    {/* Category count badge with animation */}
                                    <motion.span
                                        className="ml-auto bg-gray-700/50 px-2 py-0.5 rounded-full text-xs text-gray-300"
                                        whileHover={{
                                            backgroundColor: '#6d28d9',
                                            color: '#ffffff',
                                            scale: 1.1,
                                            transition: { duration: 0.3 }
                                        }}
                                        initial={{ opacity: 0, y: -5 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ delay: 0.2 * categoryIndex }}
                                    >
                                        {!isCollapsed && getToolsByCategory(category).length}
                                    </motion.span>

                                    {/* Toggle expand/collapse icon */}
                                    <motion.div
                                        className="ml-2 text-gray-300"
                                        animate={{
                                            rotate: expandedCategories[category] ? 0 : -90
                                        }}
                                        transition={{ duration: 0.3 }}
                                    >
                                        <FiChevronLeft size={16} />
                                    </motion.div>
                                </div>
                            </div>

                            {/* Tools in this category with dividers - only show when expanded */}
                            {expandedCategories[category] && (
                                <div className="py-2">
                                    {getToolsByCategory(category).map((tool, toolIndex) => (
                                    <React.Fragment key={tool.id}>
                                        <motion.div
                                            className={`px-5 py-4 flex items-center cursor-pointer w-full relative ${
                                                activeTab === tool.id
                                                    ? 'text-white bg-gradient-to-r from-purple-900/40 to-transparent'
                                                    : 'text-gray-400 hover:text-gray-200'
                                            }`}
                                            onClick={() => toggleTab(tool.id)}
                                            title={tool.label}
                                            onMouseEnter={() => handleToolHover(tool.id)}
                                            onMouseLeave={handleToolLeave}
                                            whileHover={{ backgroundColor: 'rgba(255,255,255,0.1)' }}
                                            whileTap={{ scale: 0.98 }}
                                            style={isCollapsed ? { justifyContent: 'center', paddingLeft: 0, paddingRight: 0 } : {}}
                                        >
                                            {/* Active indicator with animation */}
                                            {activeTab === tool.id && !isCollapsed && (
                                                <motion.div
                                                    className="absolute left-0 top-0 bottom-0 w-2 bg-gradient-to-b from-purple-400 to-purple-600"
                                                    layoutId="activeIndicator"
                                                    initial={{ opacity: 0 }}
                                                    animate={{ opacity: 1 }}
                                                    exit={{ opacity: 0 }}
                                                />
                                            )}

                                            {/* Tool icon with glow effect when active and hover animations */}
                                            <div className={`relative mr-4 ${activeTab === tool.id ? 'text-white' : ''}`} style={isCollapsed ? { marginRight: 0 } : {}}>
                                                {activeTab === tool.id && !isCollapsed && (
                                                    <div
                                                        className="absolute inset-0 bg-purple-500 rounded-full opacity-20 filter blur-md"
                                                    />
                                                )}
                                                <div>
                                                    {React.cloneElement(tool.icon, { size: 24 })}
                                                </div>
                                            </div>

                                            {/* Tool label */}
                                            {!isCollapsed && sidebarWidth > 60 && (
                                                <div className="text-base font-medium flex-1">
                                                    {tool.label}
                                                </div>
                                            )}

                                            {/* Tool shortcut or count indicator with animation */}
                                            {!isCollapsed && (
                                            <div className="text-xs text-gray-500 mr-2">
                                                {tool.id === 'templates' && (
                                                    <motion.span
                                                        whileHover={{
                                                            color: '#a855f7',
                                                            scale: 1.2,
                                                            transition: { duration: 0.3 }
                                                        }}
                                                        animate={isHovered === tool.id ?
                                                            { y: [0, -3, 0], color: ['#6b7280', '#a855f7', '#6b7280'] } :
                                                            {}
                                                        }
                                                        transition={{ duration: 0.5 }}
                                                    >
                                                        12+
                                                    </motion.span>
                                                )}
                                                {tool.id === 'elements' && (
                                                    <motion.span
                                                        whileHover={{
                                                            color: '#a855f7',
                                                            scale: 1.2,
                                                            transition: { duration: 0.3 }
                                                        }}
                                                        animate={isHovered === tool.id ?
                                                            { y: [0, -3, 0], color: ['#6b7280', '#a855f7', '#6b7280'] } :
                                                            {}
                                                        }
                                                        transition={{ duration: 0.5 }}
                                                    >
                                                        50+
                                                    </motion.span>
                                                )}
                                                {tool.id === 'photos' && (
                                                    <motion.span
                                                        whileHover={{
                                                            color: '#a855f7',
                                                            scale: 1.2,
                                                            transition: { duration: 0.3 }
                                                        }}
                                                        animate={isHovered === tool.id ?
                                                            { y: [0, -3, 0], color: ['#6b7280', '#a855f7', '#6b7280'] } :
                                                            {}
                                                        }
                                                        transition={{ duration: 0.5 }}
                                                    >
                                                        100+
                                                    </motion.span>
                                                )}
                                            </div>
                                            )}

                                            {/* Active indicator dot with pulse animation */}
                                            {activeTab === tool.id && !isCollapsed && (
                                                <motion.div
                                                    className="w-2 h-2 rounded-full bg-purple-400 mr-1 relative"
                                                    initial={{ scale: 0 }}
                                                    animate={{ scale: 1 }}
                                                    transition={{ delay: 0.2 }}
                                                >
                                                    {/* Pulse effect */}
                                                    <motion.div
                                                        className="absolute inset-0 rounded-full bg-purple-400"
                                                        animate={{
                                                            scale: [1, 1.8, 1],
                                                            opacity: [1, 0, 1]
                                                        }}
                                                        transition={{
                                                            duration: 1.5,
                                                            repeat: Infinity,
                                                            repeatType: "loop"
                                                        }}
                                                    />
                                                </motion.div>
                                            )}
                                        </motion.div>

                                        {/* Add divider between tools (but not after the last one) */}
                                        {toolIndex < getToolsByCategory(category).length - 1 && (
                                            <div className="mx-5 border-b border-gray-700/50 my-1">
                                                <div className="w-1/3 border-b border-gray-600/30 ml-8"></div>
                                            </div>
                                        )}
                                    </React.Fragment>
                                ))}
                                </div>
                            )}

                            {/* Add divider between categories (but not after the last one) */}
                            {categoryIndex < Object.keys(toolCategories).length - 1 && (
                                <div className="mx-2 mb-2 border-b border-gray-600/30"></div>
                            )}
                        </div>
                    ))}
                </div>

                {/* Sidebar resize handle - more visible with dots pattern */}
                <motion.div
                    className="absolute top-0 right-0 w-2 h-full cursor-ew-resize bg-transparent hover:bg-purple-500/50 transition-colors flex flex-col items-center justify-center"
                    onMouseDown={(e) => handleResizeStart(e, 'sidebar')}
                    whileHover={{ width: 4 }}
                >
                    {/* Dots pattern for resize handle */}
                    <div className="flex flex-col items-center justify-center h-32 space-y-2">
                        {[...Array(6)].map((_, i) => (
                            <motion.div
                                key={i}
                                className="w-1 h-1 rounded-full bg-gray-500"
                                whileHover={{ backgroundColor: '#a855f7' }}
                                animate={{ opacity: [0.3, 0.7, 0.3] }}
                                transition={{
                                    duration: 1.5,
                                    repeat: Infinity,
                                    delay: i * 0.1
                                }}
                            />
                        ))}
                    </div>
                </motion.div>
            </motion.div>

            {/* Content area with animation */}
            <AnimatePresence>
                {activeTab && (
                    <motion.div
                        className="bg-white border-r border-gray-200 h-full overflow-y-auto relative"
                        style={{ width: `${contentWidth}px` }}
                        initial={{ width: 0, opacity: 0 }}
                        animate={{ width: contentWidth, opacity: 1 }}
                        exit={{ width: 0, opacity: 0 }}
                        transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                    >
                        {/* Content header */}
                        <div className="p-3 border-b border-gray-200 flex items-center justify-between bg-gray-50">
                            <h3 className="text-lg font-medium text-gray-800">
                                {tools.find(tool => tool.id === activeTab)?.label}
                            </h3>
                            <motion.button
                                className="text-gray-500 hover:text-gray-700 p-1 rounded-full"
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => setActiveTab(null)}
                            >
                                <FiChevronLeft size={20} />
                            </motion.button>
                        </div>

                        {/* Content body */}
                        <div className="p-4 custom-scrollbar overflow-y-auto" style={{ height: 'calc(100vh - 200px)' }}>
                            {tools.find(tool => tool.id === activeTab)?.component}
                        </div>

                        {/* Content resize handle - matching the sidebar handle style */}
                        <motion.div
                            className="absolute top-0 right-0 w-2 h-full cursor-ew-resize bg-transparent hover:bg-purple-500/50 transition-colors flex flex-col items-center justify-center"
                            onMouseDown={(e) => handleResizeStart(e, 'content')}
                            whileHover={{ width: 4 }}
                        >
                            {/* Dots pattern for resize handle */}
                            <div className="flex flex-col items-center justify-center h-32 space-y-2">
                                {[...Array(6)].map((_, i) => (
                                    <motion.div
                                        key={i}
                                        className="w-1 h-1 rounded-full bg-gray-400"
                                        whileHover={{ backgroundColor: '#a855f7' }}
                                        animate={{ opacity: [0.3, 0.7, 0.3] }}
                                        transition={{
                                            duration: 1.5,
                                            repeat: Infinity,
                                            delay: i * 0.1
                                        }}
                                    />
                                ))}
                            </div>
                        </motion.div>
                    </motion.div>
                )}
            </AnimatePresence>
        </div>
    );
};

LeftSidebar.propTypes = {
    isMobile: PropTypes.bool
};

// إضافة CSS مخصص للـ scroll
const scrollbarStyles = `
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(139, 92, 246, 0.6) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 10px;
    margin: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.8), rgba(168, 85, 247, 0.6));
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 
        0 2px 4px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, rgba(139, 92, 246, 1), rgba(168, 85, 247, 0.8));
    box-shadow: 
        0 4px 8px rgba(139, 92, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.custom-scrollbar::-webkit-scrollbar-thumb:active {
    background: linear-gradient(135deg, rgba(124, 58, 237, 1), rgba(147, 51, 234, 0.9));
    box-shadow: 
        0 2px 4px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.custom-scrollbar::-webkit-scrollbar-corner {
    background: transparent;
}

/* تحسين للـ mobile scroll */
.mobile-toolbar.custom-scrollbar::-webkit-scrollbar {
    height: 4px;
}

.mobile-toolbar.custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(90deg, rgba(139, 92, 246, 0.7), rgba(168, 85, 247, 0.5));
    border-radius: 6px;
}

.mobile-toolbar.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(90deg, rgba(139, 92, 246, 0.9), rgba(168, 85, 247, 0.7));
}

/* تحسين للـ content area scroll */
.mobile-content-body.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
}

.mobile-content-body.custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, rgba(156, 163, 175, 0.8), rgba(107, 114, 128, 0.6));
    border-radius: 12px;
}

.mobile-content-body.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, rgba(156, 163, 175, 1), rgba(107, 114, 128, 0.8));
}
`;

// إضافة الـ styles إلى الـ document
if (typeof document !== 'undefined') {
    const styleId = 'left-sidebar-scrollbar-styles';
    let existingStyle = document.getElementById(styleId);
    
    if (!existingStyle) {
        const styleElement = document.createElement('style');
        styleElement.id = styleId;
        styleElement.textContent = scrollbarStyles;
        document.head.appendChild(styleElement);
    }
}

export default LeftSidebar;
