import { useQuery } from 'react-query';
import axiosInstance from '../config/Axios'; 



const fetchGroupPermission = async () => {
    console.log("📌 Base URL:", axiosInstance.defaults.baseURL);
    console.log("📌 Token:", localStorage.getItem("token"));

    try {
        const response = await axiosInstance.get('/permission-groups');  
        console.log("📌 Full API Response:", response); 
        
        const groups = response.data.groups; 
        console.log("📌 Extracted Permissions Groups:", groups);

        return groups;
    } catch (error) {
        console.error("❌ API Error:", error.response ? error.response.data : error.message);
        throw new Error("Failed to fetch permission-groups.");
    }
};





export const GroupPermissions = () => {
    return useQuery('group_permission', fetchGroupPermission);
};
