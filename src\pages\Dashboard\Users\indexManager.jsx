import React, { useEffect, useState } from 'react';
import TypesTabMenu from './components/TypesTabMenu';
import ManagersDataTable from './Members/ManagersDataTable';
import GroupsDataTable from './Groups/GroupsDataTable';
import { useGetDesigns } from "@quires/template";
import Container from '@components/Container';
import { useParams } from 'react-router-dom';
import { DataTableProvider } from '../../../contexts/UsersDataTableContext';
function MembersIndex() {
    useGetDesigns();

    const { type } = useParams();
    const [activeTap, setActiveTap] = useState(type === "members" ? 0 : 1);

    return (
        <Container>
            <div className='w-full'>
                <TypesTabMenu activeTap={activeTap} setActiveTap={setActiveTap} />
            </div>
            <div className="w-full overflow-x-auto">
                <DataTableProvider>
                    <ManagersDataTable />
                </DataTableProvider>
            </div>
        </Container>
    );
}

export default MembersIndex;
