import { useState, useEffect } from "react";
import { Dropdown } from "primereact/dropdown";
import { Dialog } from "primereact/dialog";
import { InputText } from "primereact/inputtext";
import { Button } from "primereact/button";
import { Toast } from 'primereact/toast';
import { useRef } from 'react';

const CreateCardForm = ({
  fetchCards,
  isModalOpen,
  setIsModalOpen,
  editData,
  isEditMode,
  resetEditMode
}) => {
  const [formData, setFormData] = useState({
    name: "",
    number: "",
    type: null,
  });
  const [errors, setErrors] = useState({});
  const [cardTypes, setCardTypes] = useState([]);
  const [loading, setLoading] = useState(false);
  const toast = useRef(null);
  const backendUrl = import.meta.env.VITE_BACKEND_URL;
  const token = localStorage.getItem("token");

  useEffect(() => {
    if (isEditMode && editData) {
      setFormData({
        name: editData.name,
        number: editData.number,
        type: editData.card_type?.id || null
      });
    } else {
      setFormData({
        name: "",
        number: "",
        type: null,
      });
    }
  }, [editData, isEditMode]);

  useEffect(() => {
    const fetchCardTypes = async () => {
      try {
        const response = await fetch(`${backendUrl}/card-types`, {
          headers: {
            Authorization: `Bearer ${token}`,
            Accept: "application/json",
          },
        });

        if (!response.ok) throw new Error("Failed to fetch card types.");

        const data = await response.json();
        const types = data.data || data;
        
        const formattedTypes = Array.isArray(types) ? types.map((type) => ({
          id: type.id,
          name: type.name,
        })) : [];

        setCardTypes(formattedTypes);
      } catch (error) {
        console.error("Error fetching card types:", error);
        setCardTypes([]);
      }
    };

    if (isModalOpen) {
      fetchCardTypes();
    }
  }, [isModalOpen]);

  const handleChange = (name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const extractErrorMessage = (data) => {
    if (data.details) {
      const firstErrorField = Object.keys(data.details)[0];
      if (firstErrorField && Array.isArray(data.details[firstErrorField])) {
        return data.details[firstErrorField][0];
      }
    }
    return data.message || null;
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    setErrors({});

    if (!formData.name || !formData.number || !formData.type) {
      setErrors({ message: "All fields are required." });
      return;
    }

    let requestUrl, method, successMessage;
    const requestData = {
      name: formData.name,
      number: formData.number,
      type: formData.type,
    };

    if (isEditMode) {
      requestUrl = `${backendUrl}/new_update_cards/${editData.id}`;
      method = "PUT";
      successMessage = "Card updated successfully";
    } else {
      requestUrl = `${backendUrl}/create_cards`;
      method = "POST";
      successMessage = "The card has been created successfully.";
    }

    try {
      setLoading(true);
      const response = await fetch(requestUrl, {
        method: method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(requestData),
      });

      const data = await response.json();

      if (response.ok) {
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: successMessage,
          life: 3000
        });
        setFormData({ name: "", number: "", type: null });
        setIsModalOpen(false);
        if (fetchCards) fetchCards();
        if (isEditMode && resetEditMode) resetEditMode();
      } else {
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: extractErrorMessage(data) || (isEditMode ? 'Failed to update card' : 'Failed to create card'),
          life: 3000
        });
      }
    } catch (error) {
      console.error("Error:", error);
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'An error occurred',
        life: 3000
      });
    } finally {
      setLoading(false);
    }
  };

  const onHide = () => {
    setIsModalOpen(false);
    if (isEditMode && resetEditMode) resetEditMode();
  };

  return (
    <>
      <Toast ref={toast} />
      <Dialog
        header={isEditMode ? "Edit Card" : "Create New Card"}
        visible={isModalOpen}
        onHide={onHide}
        style={{ width: "50vw" }}
        modal
        className="p-fluid"
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-600">
              Card Name
            </label>
            <InputText
              id="name"
              value={formData.name}
              onChange={(e) => handleChange("name", e.target.value)}
              placeholder="Enter card name"
              required
            />
          </div>

          <div>
            <label htmlFor="number" className="block text-sm font-medium text-gray-600">
              Card Number
            </label>
            <InputText
              id="number"
              value={formData.number}
              onChange={(e) => handleChange("number", e.target.value)}
              placeholder="Enter card number"
              required
            />
          </div>

          <div>
            <label htmlFor="type" className="block text-sm font-medium text-gray-600">
              Card Type
            </label>
            <Dropdown
              id="type"
              value={formData.type}
              options={cardTypes}
              onChange={(e) => handleChange("type", e.value)}
              optionLabel="name"
              optionValue="id"
              placeholder="Select card type"
              required
              disabled={loading || cardTypes.length === 0}
            />
            {cardTypes.length === 0 && (
              <small className="text-red-500">No card types available</small>
            )}
          </div>

          <div className="flex justify-center gap-2">
            <Button
              type="button"
              label="Cancel"
              className="p-button-secondary"
              onClick={onHide}
              disabled={loading}
            />
            <Button
              type="submit"
              label={loading ? (isEditMode ? "Updating..." : "Creating...") : (isEditMode ? "Update" : "Create")}
              className="p-button-primary"
              disabled={loading || cardTypes.length === 0}
            />
          </div>
        </form>
      </Dialog>
    </>
  );
};

export default CreateCardForm;