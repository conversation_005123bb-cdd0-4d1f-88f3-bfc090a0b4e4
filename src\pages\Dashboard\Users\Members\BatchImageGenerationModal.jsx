import React, { useState, useEffect } from 'react';
import { Dialog } from 'primereact/dialog';
import { motion } from 'framer-motion';
import { FaSpinner } from 'react-icons/fa';
import { MdCameraEnhance } from 'react-icons/md';
import { IoPhonePortraitOutline } from 'react-icons/io5';
import axiosInstance from '../../../../config/Axios';

const BatchImageGenerationModal = ({ visible, designId, onHide }) => {
  const [status, setStatus] = useState('pending');
  const [progress, setProgress] = useState(0);
  const [totalUsers, setTotalUsers] = useState(0);
  const [completedImages, setCompletedImages] = useState(0);
  const [currentPhase, setCurrentPhase] = useState('pending');
  const [batchId, setBatchId] = useState(null);

  useEffect(() => {
    console.log("BatchImageGenerationModal useEffect:", { visible, designId });

    if (visible && designId) {
      // Get batch ID from localStorage
      const storedBatchId = localStorage.getItem(`batch_${designId}`);
      console.log("Stored batch ID:", storedBatchId);

      if (storedBatchId) {
        setBatchId(storedBatchId);
        console.log("Using batch ID:", storedBatchId);
      } else {
        console.warn("No batch ID found for design:", designId);
      }

      // Reset state
      setStatus('pending');
      setProgress(0);
      setCurrentPhase('pending');

      // Start with pending phase for 5 seconds
      const pendingTimer = setTimeout(() => {
        setCurrentPhase('processing');
        if (storedBatchId) {
          startStatusPolling(storedBatchId);
        }
      }, 5000);

      return () => {
        clearTimeout(pendingTimer);
      };
    }
  }, [visible, designId]);

  const startStatusPolling = (batchId) => {
    if (!batchId) {
      console.error("No batch ID available for polling");
      return;
    }

    const pollStatus = async () => {
      try {
        console.log("Polling batch status for:", batchId);
        const response = await axiosInstance.get(`/designs/batch-status/${batchId}`);
        const data = response.data;

        console.log("Batch status response:", data);

        setStatus(data.status);
        setTotalUsers(data.total_users);
        setCompletedImages(data.completed_images);
        setProgress(data.progress);

        if (data.status === 'completed') {
          setCurrentPhase('completed');
          // Clean up batch ID from localStorage
          localStorage.removeItem(`batch_${designId}`);
          return; // Stop polling
        }

        // Continue polling every 3 seconds
        setTimeout(pollStatus, 3000);
      } catch (error) {
        console.error("Error polling batch status:", error);
        // Continue polling even on error
        setTimeout(pollStatus, 3000);
      }
    };

    // Start polling immediately
    pollStatus();
  };

  const getPhoneContent = () => {
    switch (currentPhase) {
      case 'pending':
        return (
          <div className="text-center p-5">
            <div className="relative mb-8 max-w-md mx-auto">
              {/* Smartphone container with 3D effect */}
              <motion.div
                initial={{ scale: 0.9, opacity: 0, rotateY: -20 }}
                animate={{ scale: 1, opacity: 1, rotateY: 0 }}
                transition={{ duration: 0.7, type: "spring" }}
                className="relative z-10 mx-auto"
                style={{ perspective: "1000px", transformStyle: "preserve-3d" }}
              >
                {/* Phone frame with metallic effect */}
                <div className="relative w-64 h-[470px] mx-auto rounded-[36px] overflow-hidden"
                  style={{
                    background: "linear-gradient(145deg, #2a2a2a, #3a3a3a)",
                    boxShadow: "0 10px 30px rgba(0,0,0,0.3), inset 0 1px 1px rgba(255,255,255,0.2), inset 0 -1px 1px rgba(0,0,0,0.5)",
                    border: "6px solid #222",
                    transform: "rotateY(0deg) rotateX(5deg)",
                    transformStyle: "preserve-3d"
                  }}>

                  {/* Phone screen with glossy effect */}
                  <div className="absolute inset-0 bg-gradient-to-br from-yellow-600 via-orange-500 to-red-600 overflow-hidden"
                    style={{
                      boxShadow: "inset 0 0 10px rgba(255,255,255,0.3)",
                    }}>

                    {/* Screen content */}
                    <div className="relative w-full h-full flex flex-col items-center justify-center p-4 overflow-hidden">
                      {/* App header */}
                      <div className="absolute top-0 left-0 right-0 bg-black bg-opacity-50 backdrop-blur-sm p-2 flex items-center justify-between">
                        <div className="text-white text-xs font-medium flex items-center">
                          <IoPhonePortraitOutline className="mr-1" />
                          <span>New User Processor</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <div className="w-1.5 h-1.5 rounded-full bg-green-400"></div>
                          <div className="w-1.5 h-1.5 rounded-full bg-yellow-400"></div>
                          <div className="w-1.5 h-1.5 rounded-full bg-red-400"></div>
                        </div>
                      </div>

                      {/* AI Initialization Animation */}
                      <div className="relative w-full h-[280px] mt-12 flex items-center justify-center">
                        {/* Central AI brain visualization */}
                        <div className="relative">
                          {/* Glowing orb */}
                          <motion.div
                            className="absolute inset-0 rounded-full"
                            style={{
                              background: "radial-gradient(circle, rgba(251,191,36,0.6) 0%, rgba(251,191,36,0) 70%)",
                              filter: "blur(10px)"
                            }}
                            animate={{
                              scale: [1, 1.2, 1],
                              opacity: [0.5, 0.8, 0.5]
                            }}
                            transition={{
                              repeat: Infinity,
                              duration: 3,
                              ease: "easeInOut"
                            }}
                          />

                          {/* Central orb */}
                          <motion.div
                            className="relative w-24 h-24 rounded-full bg-gradient-to-br from-yellow-500 to-orange-600 flex items-center justify-center"
                            animate={{
                              boxShadow: [
                                "0 0 20px rgba(251,191,36,0.5)",
                                "0 0 30px rgba(251,191,36,0.7)",
                                "0 0 20px rgba(251,191,36,0.5)"
                              ]
                            }}
                            transition={{
                              repeat: Infinity,
                              duration: 3,
                              ease: "easeInOut"
                            }}
                          >
                            <motion.div
                              className="absolute inset-2 rounded-full bg-gradient-to-br from-yellow-400 to-orange-500"
                              animate={{
                                opacity: [0.8, 1, 0.8],
                                scale: [0.95, 1, 0.95]
                              }}
                              transition={{
                                repeat: Infinity,
                                duration: 2,
                                ease: "easeInOut"
                              }}
                            />

                            {/* AI Icon */}
                            <MdCameraEnhance className="text-white text-4xl relative z-10" />
                          </motion.div>

                          {/* Orbiting particles */}
                          {[...Array(8)].map((_, i) => {
                            const angle = (i * Math.PI * 2) / 8;
                            const delay = i * 0.15;

                            return (
                              <motion.div
                                key={`particle-${i}`}
                                className="absolute w-3 h-3 rounded-full bg-yellow-400"
                                style={{
                                  top: "50%",
                                  left: "50%",
                                  marginTop: "-1.5px",
                                  marginLeft: "-1.5px"
                                }}
                                animate={{
                                  x: [
                                    Math.cos(angle) * 50,
                                    Math.cos(angle + Math.PI) * 50,
                                    Math.cos(angle + Math.PI * 2) * 50
                                  ],
                                  y: [
                                    Math.sin(angle) * 50,
                                    Math.sin(angle + Math.PI) * 50,
                                    Math.sin(angle + Math.PI * 2) * 50
                                  ],
                                  opacity: [0.8, 1, 0.8],
                                  scale: [1, 1.5, 1]
                                }}
                                transition={{
                                  repeat: Infinity,
                                  duration: 4,
                                  delay: delay,
                                  ease: "easeInOut"
                                }}
                              />
                            );
                          })}
                        </div>

                        {/* Floating text elements */}
                        {["Initializing", "Processing", "Loading", "Preparing"].map((text, i) => {
                          const angle = (i * Math.PI * 2) / 4;
                          const x = Math.cos(angle) * 100;
                          const y = Math.sin(angle) * 80;

                          return (
                            <motion.div
                              key={`text-${i}`}
                              className="absolute text-xs font-bold text-yellow-300 bg-yellow-900 bg-opacity-30 px-2 py-1 rounded-lg"
                              style={{
                                top: "50%",
                                left: "50%",
                                transform: `translate(${x}px, ${y}px)`,
                                textShadow: "0 0 10px rgba(251,191,36,0.5)"
                              }}
                              animate={{
                                opacity: [0.5, 1, 0.5],
                                y: [y, y - 5, y]
                              }}
                              transition={{
                                repeat: Infinity,
                                duration: 2,
                                delay: i * 0.5,
                                ease: "easeInOut"
                              }}
                            >
                              {text}
                            </motion.div>
                          );
                        })}
                      </div>
                    </div>
                  </div>

                  {/* Dynamic Island */}
                  <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-32 h-8 bg-black rounded-full z-10 flex items-center justify-center overflow-hidden">
                    <motion.div
                      className="w-full h-full relative flex items-center justify-center"
                      animate={{
                        width: ["60%", "95%", "60%"],
                        borderRadius: ["9999px", "16px", "9999px"]
                      }}
                      transition={{
                        repeat: Infinity,
                        repeatType: "reverse",
                        duration: 3,
                        ease: "easeInOut",
                        repeatDelay: 2
                      }}
                    >
                      {/* Status indicator */}
                      <div className="absolute left-2 top-1/2 transform -translate-y-1/2 w-2 h-2 rounded-full bg-yellow-500"></div>

                      {/* Status text that appears when expanded */}
                      <motion.div
                        className="text-white text-[10px] font-medium flex items-center"
                        animate={{
                          opacity: [0, 1, 0],
                          x: [10, 0, 10]
                        }}
                        transition={{
                          repeat: Infinity,
                          repeatType: "reverse",
                          duration: 3,
                          ease: "easeInOut",
                          repeatDelay: 2
                        }}
                      >
                        <span className="ml-4">Preparing New Users</span>
                      </motion.div>

                      {/* Right side icons */}
                      <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex space-x-1">
                        <div className="w-1.5 h-1.5 rounded-full bg-white"></div>
                        <div className="w-1.5 h-1.5 rounded-full bg-white"></div>
                      </div>
                    </motion.div>
                  </div>
                </div>

                {/* Phone reflection */}
                <div className="absolute bottom-[-10px] left-1/2 transform -translate-x-1/2 w-56 h-8 bg-black opacity-20 blur-md rounded-full"></div>
              </motion.div>

              {/* Floating particles */}
              {[...Array(15)].map((_, i) => (
                <motion.div
                  key={`float-particle-${i}`}
                  className="absolute w-2 h-2 rounded-full"
                  style={{
                    background: i % 3 === 0
                      ? "radial-gradient(circle, rgba(251,191,36,0.8) 0%, rgba(251,191,36,0) 70%)"
                      : i % 3 === 1
                        ? "radial-gradient(circle, rgba(249,115,22,0.8) 0%, rgba(249,115,22,0) 70%)"
                        : "radial-gradient(circle, rgba(239,68,68,0.8) 0%, rgba(239,68,68,0) 70%)",
                    top: `${Math.random() * 100}%`,
                    left: `${Math.random() * 100}%`,
                  }}
                  animate={{
                    y: [0, -(10 + Math.random() * 20)],
                    x: [0, (Math.random() * 10) - 5],
                    opacity: [0, 0.7, 0],
                    scale: [0, 0.5 + Math.random() * 0.5, 0]
                  }}
                  transition={{
                    repeat: Infinity,
                    duration: 2 + Math.random() * 2,
                    delay: Math.random() * 2
                  }}
                />
              ))}
            </div>

            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
            >
              <h3 className="text-2xl font-bold mb-3 text-gray-800 bg-gradient-to-r from-yellow-500 to-orange-500 bg-clip-text text-transparent">Preparing New User Images</h3>
              <p className="text-gray-600 mb-5 max-w-md mx-auto">
                We're initializing our advanced processing engine to create beautiful professional images for newly assigned users.
              </p>

              {/* Status indicators */}
              <div className="flex justify-center space-x-6 mb-4">
                <motion.div
                  className="text-center"
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 0.4, duration: 0.5 }}
                >
                  <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-yellow-100 to-yellow-200 flex items-center justify-center mx-auto mb-2 shadow-md border border-yellow-200">
                    <motion.div
                      className="w-8 h-8 rounded-xl bg-gradient-to-br from-yellow-500 to-orange-400 flex items-center justify-center"
                      animate={{ rotate: 360 }}
                      transition={{ repeat: Infinity, duration: 3, ease: "linear" }}
                    >
                      <FaSpinner className="text-white text-lg" />
                    </motion.div>
                  </div>
                  <p className="text-xs font-medium text-gray-700">Initializing</p>
                </motion.div>

                <motion.div
                  className="text-center"
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 0.5, duration: 0.5 }}
                >
                  <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-orange-100 to-orange-200 flex items-center justify-center mx-auto mb-2 shadow-md border border-orange-200">
                    <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-orange-500 to-red-400 flex items-center justify-center">
                      <MdCameraEnhance className="text-white text-lg" />
                    </div>
                  </div>
                  <p className="text-xs font-medium text-gray-700">Processing</p>
                </motion.div>

                <motion.div
                  className="text-center"
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 0.6, duration: 0.5 }}
                >
                  <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-red-100 to-red-200 flex items-center justify-center mx-auto mb-2 shadow-md border border-red-200">
                    <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-red-500 to-pink-400 flex items-center justify-center">
                      <IoPhonePortraitOutline className="text-white text-lg" />
                    </div>
                  </div>
                  <p className="text-xs font-medium text-gray-700">Preparing</p>
                </motion.div>
              </div>

              {/* Animated dots */}
              <div className="flex justify-center space-x-2 text-yellow-500">
                <motion.span
                  animate={{ opacity: [0.3, 1, 0.3], scale: [0.8, 1, 0.8] }}
                  transition={{ repeat: Infinity, duration: 1.5, delay: 0 }}
                  className="text-xl"
                >●</motion.span>
                <motion.span
                  animate={{ opacity: [0.3, 1, 0.3], scale: [0.8, 1, 0.8] }}
                  transition={{ repeat: Infinity, duration: 1.5, delay: 0.2 }}
                  className="text-xl"
                >●</motion.span>
                <motion.span
                  animate={{ opacity: [0.3, 1, 0.3], scale: [0.8, 1, 0.8] }}
                  transition={{ repeat: Infinity, duration: 1.5, delay: 0.4 }}
                  className="text-xl"
                >●</motion.span>
              </div>
            </motion.div>
          </div>
        );

      case 'processing':
        return (
          <div className="text-center p-5">
            <div className="relative mb-8 max-w-md mx-auto">
              {/* Smartphone container with 3D effect */}
              <motion.div
                initial={{ scale: 0.9, opacity: 0, rotateY: -20 }}
                animate={{ scale: 1, opacity: 1, rotateY: 0 }}
                transition={{ duration: 0.7, type: "spring" }}
                className="relative z-10 mx-auto"
                style={{ perspective: "1000px", transformStyle: "preserve-3d" }}
              >
                {/* Phone frame with metallic effect */}
                <div className="relative w-64 h-[470px] mx-auto rounded-[36px] overflow-hidden"
                  style={{
                    background: "linear-gradient(145deg, #2a2a2a, #3a3a3a)",
                    boxShadow: "0 10px 30px rgba(0,0,0,0.3), inset 0 1px 1px rgba(255,255,255,0.2), inset 0 -1px 1px rgba(0,0,0,0.5)",
                    border: "6px solid #222",
                    transform: "rotateY(0deg) rotateX(5deg)",
                    transformStyle: "preserve-3d"
                  }}>

                  {/* Phone screen with glossy effect */}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-900 via-purple-800 to-indigo-900 overflow-hidden"
                    style={{
                      boxShadow: "inset 0 0 10px rgba(255,255,255,0.3)",
                    }}>

                    {/* Screen content */}
                    <div className="relative w-full h-full flex flex-col items-center justify-center p-4 overflow-hidden">
                      {/* App header */}
                      <div className="absolute top-0 left-0 right-0 bg-black bg-opacity-50 backdrop-blur-sm p-2 flex items-center justify-between">
                        <div className="text-white text-xs font-medium flex items-center">
                          <IoPhonePortraitOutline className="mr-1" />
                          <span>New User Processor</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <div className="w-1.5 h-1.5 rounded-full bg-green-400"></div>
                          <div className="w-1.5 h-1.5 rounded-full bg-yellow-400"></div>
                          <div className="w-1.5 h-1.5 rounded-full bg-red-400"></div>
                        </div>
                      </div>

                      {/* Enhanced Processing Display */}
                      <div className="relative w-full h-[330px] mt-8 overflow-hidden">
                        {/* Professional Processing Interface */}
                        <div className="absolute inset-0 flex flex-col items-center justify-center p-4">
                          {/* Central Processing Hub */}
                          <div className="relative mb-6">
                            {/* Main processing circle */}
                            <motion.div
                              className="w-24 h-24 rounded-full bg-gradient-to-br from-blue-500 via-purple-500 to-indigo-600 flex items-center justify-center relative"
                              animate={{
                                boxShadow: [
                                  "0 0 20px rgba(59,130,246,0.5)",
                                  "0 0 40px rgba(147,51,234,0.7)",
                                  "0 0 20px rgba(59,130,246,0.5)"
                                ]
                              }}
                              transition={{
                                repeat: Infinity,
                                duration: 2,
                                ease: "easeInOut"
                              }}
                            >
                              <motion.div
                                animate={{ rotate: 360 }}
                                transition={{ repeat: Infinity, duration: 3, ease: "linear" }}
                              >
                                <MdCameraEnhance className="text-white text-4xl" />
                              </motion.div>
                            </motion.div>

                            {/* Orbiting progress indicators */}
                            {[...Array(6)].map((_, i) => {
                              const angle = (i * Math.PI * 2) / 6;
                              const radius = 50;

                              return (
                                <motion.div
                                  key={`orbit-${i}`}
                                  className="absolute w-4 h-4 rounded-full bg-gradient-to-r from-blue-400 to-purple-400"
                                  style={{
                                    top: "50%",
                                    left: "50%",
                                    marginTop: "-8px",
                                    marginLeft: "-8px"
                                  }}
                                  animate={{
                                    x: Math.cos(angle) * radius,
                                    y: Math.sin(angle) * radius,
                                    scale: [1, 1.5, 1],
                                    opacity: [0.6, 1, 0.6]
                                  }}
                                  transition={{
                                    repeat: Infinity,
                                    duration: 4,
                                    delay: i * 0.3,
                                    ease: "easeInOut"
                                  }}
                                />
                              );
                            })}
                          </div>

                          {/* Processing Stats */}
                          <div className="w-full max-w-xs space-y-3">
                            {/* Images processed counter */}
                            <div className="bg-black bg-opacity-30 rounded-lg p-3 backdrop-blur-sm">
                              <div className="flex justify-between items-center mb-2">
                                <span className="text-white text-sm font-medium">New Users Processed</span>
                                <span className="text-blue-300 text-sm font-bold">{completedImages}/{totalUsers}</span>
                              </div>

                              {/* Enhanced Progress Bar */}
                              <div className="w-full h-2 bg-gray-800 rounded-full overflow-hidden relative">
                                <motion.div
                                  className="h-full bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-500 relative"
                                  style={{ width: `${progress}%` }}
                                  initial={{ width: "0%" }}
                                  animate={{ width: `${progress}%` }}
                                  transition={{ duration: 0.5, ease: "easeOut" }}
                                >
                                  {/* Animated shine effect */}
                                  <motion.div
                                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30"
                                    animate={{
                                      x: ["-100%", "100%"]
                                    }}
                                    transition={{
                                      repeat: Infinity,
                                      duration: 1.5,
                                      ease: "linear"
                                    }}
                                  />
                                </motion.div>
                              </div>
                            </div>

                            {/* Status indicator */}
                            <div className="bg-black bg-opacity-30 rounded-lg p-3 backdrop-blur-sm">
                              <div className="flex items-center justify-center space-x-2">
                                <motion.div
                                  className="w-3 h-3 rounded-full bg-green-400"
                                  animate={{ opacity: [0.5, 1, 0.5] }}
                                  transition={{ repeat: Infinity, duration: 1.5 }}
                                />
                                <span className="text-white text-sm font-medium">Processing New Users...</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Dynamic Island */}
                  <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-32 h-8 bg-black rounded-full z-10 flex items-center justify-center overflow-hidden">
                    <motion.div
                      className="w-full h-full relative flex items-center justify-center"
                      animate={{
                        width: ["60%", "95%", "60%"],
                        borderRadius: ["9999px", "16px", "9999px"]
                      }}
                      transition={{
                        repeat: Infinity,
                        repeatType: "reverse",
                        duration: 3,
                        ease: "easeInOut",
                        repeatDelay: 2
                      }}
                    >
                      {/* Status indicator */}
                      <div className="absolute left-2 top-1/2 transform -translate-y-1/2 w-2 h-2 rounded-full bg-blue-500"></div>

                      {/* Status text that appears when expanded */}
                      <motion.div
                        className="text-white text-[10px] font-medium flex items-center"
                        animate={{
                          opacity: [0, 1, 0],
                          x: [10, 0, 10]
                        }}
                        transition={{
                          repeat: Infinity,
                          repeatType: "reverse",
                          duration: 3,
                          ease: "easeInOut",
                          repeatDelay: 2
                        }}
                      >
                        <span className="ml-4">Processing {progress}%</span>
                      </motion.div>

                      {/* Right side icons */}
                      <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex space-x-1">
                        <div className="w-1.5 h-1.5 rounded-full bg-white"></div>
                        <div className="w-1.5 h-1.5 rounded-full bg-white"></div>
                      </div>
                    </motion.div>
                  </div>
                </div>

                {/* Phone reflection */}
                <div className="absolute bottom-[-10px] left-1/2 transform -translate-x-1/2 w-56 h-8 bg-black opacity-20 blur-md rounded-full"></div>
              </motion.div>

              {/* Floating particles */}
              {[...Array(15)].map((_, i) => (
                <motion.div
                  key={`float-particle-${i}`}
                  className="absolute w-2 h-2 rounded-full"
                  style={{
                    background: i % 3 === 0
                      ? "radial-gradient(circle, rgba(59,130,246,0.8) 0%, rgba(59,130,246,0) 70%)"
                      : i % 3 === 1
                        ? "radial-gradient(circle, rgba(99,102,241,0.8) 0%, rgba(99,102,241,0) 70%)"
                        : "radial-gradient(circle, rgba(139,92,246,0.8) 0%, rgba(139,92,246,0) 70%)",
                    top: `${Math.random() * 100}%`,
                    left: `${Math.random() * 100}%`,
                  }}
                  animate={{
                    y: [0, -(10 + Math.random() * 20)],
                    x: [0, (Math.random() * 10) - 5],
                    opacity: [0, 0.7, 0],
                    scale: [0, 0.5 + Math.random() * 0.5, 0]
                  }}
                  transition={{
                    repeat: Infinity,
                    duration: 2 + Math.random() * 2,
                    delay: Math.random() * 2
                  }}
                />
              ))}
            </div>

            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
            >
              <h3 className="text-2xl font-bold mb-3 text-gray-800 bg-gradient-to-r from-blue-500 to-indigo-500 bg-clip-text text-transparent">Generating Images for New Users</h3>
              <p className="text-gray-600 mb-5 max-w-md mx-auto">
                Processing {completedImages} of {totalUsers} newly assigned users with our advanced AI engine.
              </p>

              {/* Progress indicator */}
              <div className="bg-gray-100 rounded-lg p-4 max-w-sm mx-auto">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-700">Progress</span>
                  <span className="text-sm font-bold text-blue-600">{progress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <motion.div
                    className="bg-gradient-to-r from-blue-500 to-indigo-500 h-2 rounded-full"
                    initial={{ width: "0%" }}
                    animate={{ width: `${progress}%` }}
                    transition={{ duration: 0.5, ease: "easeOut" }}
                  />
                </div>
              </div>
            </motion.div>
          </div>
        );

      case 'completed':
        return (
          <div className="text-center p-5">
            <div className="relative mb-8 max-w-md mx-auto">
              {/* Smartphone container with 3D effect */}
              <motion.div
                initial={{ scale: 0.9, opacity: 0, rotateY: -20 }}
                animate={{ scale: 1, opacity: 1, rotateY: 0 }}
                transition={{ duration: 0.7, type: "spring" }}
                className="relative z-10 mx-auto"
                style={{ perspective: "1000px", transformStyle: "preserve-3d" }}
              >
                {/* Phone frame with metallic effect */}
                <div className="relative w-64 h-[470px] mx-auto rounded-[36px] overflow-hidden"
                  style={{
                    background: "linear-gradient(145deg, #2a2a2a, #3a3a3a)",
                    boxShadow: "0 10px 30px rgba(0,0,0,0.3), inset 0 1px 1px rgba(255,255,255,0.2), inset 0 -1px 1px rgba(0,0,0,0.5)",
                    border: "6px solid #222",
                    transform: "rotateY(0deg) rotateX(5deg)",
                    transformStyle: "preserve-3d"
                  }}>

                  {/* Phone screen with glossy effect */}
                  <div className="absolute inset-0 bg-gradient-to-br from-green-600 via-emerald-500 to-teal-600 overflow-hidden"
                    style={{
                      boxShadow: "inset 0 0 10px rgba(255,255,255,0.3)",
                    }}>

                    {/* Screen content */}
                    <div className="relative w-full h-full flex flex-col items-center justify-center p-4 overflow-hidden">
                      {/* App header */}
                      <div className="absolute top-0 left-0 right-0 bg-black bg-opacity-50 backdrop-blur-sm p-2 flex items-center justify-between">
                        <div className="text-white text-xs font-medium flex items-center">
                          <IoPhonePortraitOutline className="mr-1" />
                          <span>New User Processor</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <div className="w-1.5 h-1.5 rounded-full bg-green-400"></div>
                          <div className="w-1.5 h-1.5 rounded-full bg-yellow-400"></div>
                          <div className="w-1.5 h-1.5 rounded-full bg-red-400"></div>
                        </div>
                      </div>

                      {/* Success Display */}
                      <div className="relative w-full h-[330px] mt-8 overflow-hidden">
                        <div className="absolute inset-0 flex flex-col items-center justify-center p-4">
                          {/* Success Animation */}
                          <div className="relative mb-6">
                            {/* Success circle */}
                            <motion.div
                              className="w-24 h-24 rounded-full bg-gradient-to-br from-green-500 to-emerald-600 flex items-center justify-center relative"
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{ duration: 0.5, type: "spring" }}
                            >
                              <motion.svg
                                className="w-12 h-12 text-white"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                                initial={{ pathLength: 0 }}
                                animate={{ pathLength: 1 }}
                                transition={{ duration: 0.8, delay: 0.3 }}
                              >
                                <motion.path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={3}
                                  d="M5 13l4 4L19 7"
                                />
                              </motion.svg>
                            </motion.div>

                            {/* Success particles */}
                            {[...Array(8)].map((_, i) => {
                              const angle = (i * Math.PI * 2) / 8;
                              const delay = i * 0.1;

                              return (
                                <motion.div
                                  key={`success-particle-${i}`}
                                  className="absolute w-3 h-3 rounded-full bg-green-400"
                                  style={{
                                    top: "50%",
                                    left: "50%",
                                    marginTop: "-1.5px",
                                    marginLeft: "-1.5px"
                                  }}
                                  initial={{ scale: 0, opacity: 0 }}
                                  animate={{
                                    x: Math.cos(angle) * 60,
                                    y: Math.sin(angle) * 60,
                                    scale: [0, 1, 0],
                                    opacity: [0, 1, 0]
                                  }}
                                  transition={{
                                    duration: 1.5,
                                    delay: delay + 0.5,
                                    ease: "easeOut"
                                  }}
                                />
                              );
                            })}
                          </div>

                          {/* Success Stats */}
                          <div className="w-full max-w-xs space-y-3">
                            <div className="bg-black bg-opacity-30 rounded-lg p-3 backdrop-blur-sm">
                              <div className="flex justify-between items-center mb-2">
                                <span className="text-white text-sm font-medium">New Users Completed</span>
                                <span className="text-green-300 text-sm font-bold">{completedImages}/{totalUsers}</span>
                              </div>
                              <div className="w-full h-2 bg-gray-800 rounded-full overflow-hidden">
                                <div className="h-full bg-gradient-to-r from-green-500 to-emerald-500 w-full rounded-full" />
                              </div>
                            </div>

                            <div className="bg-black bg-opacity-30 rounded-lg p-3 backdrop-blur-sm">
                              <div className="flex items-center justify-center space-x-2">
                                <div className="w-3 h-3 rounded-full bg-green-400" />
                                <span className="text-white text-sm font-medium">All Images Generated!</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Dynamic Island */}
                  <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-32 h-8 bg-black rounded-full z-10 flex items-center justify-center overflow-hidden">
                    <div className="w-full h-full relative flex items-center justify-center">
                      <div className="absolute left-2 top-1/2 transform -translate-y-1/2 w-2 h-2 rounded-full bg-green-500"></div>
                      <span className="text-white text-[10px] font-medium ml-4">Complete</span>
                      <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex space-x-1">
                        <div className="w-1.5 h-1.5 rounded-full bg-white"></div>
                        <div className="w-1.5 h-1.5 rounded-full bg-white"></div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Phone reflection */}
                <div className="absolute bottom-[-10px] left-1/2 transform -translate-x-1/2 w-56 h-8 bg-black opacity-20 blur-md rounded-full"></div>
              </motion.div>

              {/* Success particles */}
              {[...Array(20)].map((_, i) => (
                <motion.div
                  key={`success-float-particle-${i}`}
                  className="absolute w-2 h-2 rounded-full"
                  style={{
                    background: i % 3 === 0
                      ? "radial-gradient(circle, rgba(34,197,94,0.8) 0%, rgba(34,197,94,0) 70%)"
                      : i % 3 === 1
                        ? "radial-gradient(circle, rgba(16,185,129,0.8) 0%, rgba(16,185,129,0) 70%)"
                        : "radial-gradient(circle, rgba(20,184,166,0.8) 0%, rgba(20,184,166,0) 70%)",
                    top: `${Math.random() * 100}%`,
                    left: `${Math.random() * 100}%`,
                  }}
                  initial={{ scale: 0, opacity: 0 }}
                  animate={{
                    y: [0, -(20 + Math.random() * 30)],
                    x: [0, (Math.random() * 20) - 10],
                    opacity: [0, 0.8, 0],
                    scale: [0, 1 + Math.random() * 0.5, 0]
                  }}
                  transition={{
                    duration: 2 + Math.random() * 2,
                    delay: Math.random() * 3,
                    repeat: Infinity,
                    repeatDelay: Math.random() * 5
                  }}
                />
              ))}
            </div>

            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.5 }}
            >
              <h3 className="text-2xl font-bold mb-3 text-gray-800 bg-gradient-to-r from-green-500 to-emerald-500 bg-clip-text text-transparent">New User Images Generated Successfully!</h3>
              <p className="text-gray-600 mb-5 max-w-md mx-auto">
                All {totalUsers} images have been generated and saved for newly assigned users.
              </p>

              {/* Success summary */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 max-w-sm mx-auto">
                <div className="flex items-center justify-between text-sm mb-2">
                  <span className="text-green-700 font-medium">New Users Processed:</span>
                  <span className="text-green-800 font-bold">{completedImages}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-green-700 font-medium">Success Rate:</span>
                  <span className="text-green-800 font-bold">100%</span>
                </div>
              </div>
            </motion.div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog
      visible={visible}
      onHide={currentPhase === 'completed' ? onHide : undefined}
      modal
      closable={false}
      className="image-generation-modal"
      style={{ width: '90vw', maxWidth: '1200px', height: '90vh' }}
      contentClassName="p-0 overflow-hidden"
      showHeader={false}
    >
      <div className="relative w-full h-full bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0">
          {/* Grid Pattern */}
          <div className="absolute inset-0 opacity-20">
            <div className="absolute inset-0" style={{
              backgroundImage: `
                linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
              `,
              backgroundSize: '50px 50px'
            }} />
          </div>

          {/* Floating Orbs */}
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={`orb-${i}`}
              className="absolute rounded-full"
              style={{
                width: `${Math.random() * 100 + 20}px`,
                height: `${Math.random() * 100 + 20}px`,
                background: i % 3 === 0
                  ? "radial-gradient(circle, rgba(59,130,246,0.3) 0%, rgba(59,130,246,0) 70%)"
                  : i % 3 === 1
                    ? "radial-gradient(circle, rgba(147,51,234,0.3) 0%, rgba(147,51,234,0) 70%)"
                    : "radial-gradient(circle, rgba(236,72,153,0.3) 0%, rgba(236,72,153,0) 70%)",
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
              }}
              animate={{
                x: [0, Math.random() * 100 - 50],
                y: [0, Math.random() * 100 - 50],
                scale: [1, 1.2, 1],
                opacity: [0.3, 0.6, 0.3]
              }}
              transition={{
                repeat: Infinity,
                duration: 10 + Math.random() * 10,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>

        {/* Header */}
        <div className="relative z-10 p-8 border-b border-white/10">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              <motion.div
                className="w-16 h-16 rounded-3xl bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 flex items-center justify-center shadow-2xl"
                animate={{
                  rotate: [0, 360],
                  scale: [1, 1.1, 1]
                }}
                transition={{
                  rotate: { duration: 20, repeat: Infinity, ease: "linear" },
                  scale: { duration: 4, repeat: Infinity, ease: "easeInOut" }
                }}
              >
                <MdCameraEnhance className="w-8 h-8 text-white" />
              </motion.div>
              <div>
                <motion.h2
                  className="text-3xl font-bold text-white mb-2"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  New User Image Generation
                </motion.h2>
                <motion.p
                  className="text-lg text-gray-300"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                >
                  Creating professional images for newly assigned users
                </motion.p>
              </div>
            </div>

            {currentPhase === 'completed' && (
              <motion.button
                onClick={onHide}
                className="w-12 h-12 rounded-2xl bg-white/10 hover:bg-white/20 backdrop-blur-sm flex items-center justify-center transition-all duration-300 border border-white/20"
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ delay: 1, type: "spring" }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </motion.button>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="relative z-10 flex-1 p-8">
          {getPhoneContent()}
        </div>
      </div>
    </Dialog>
  );
};

export default BatchImageGenerationModal;
