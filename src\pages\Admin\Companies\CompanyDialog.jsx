import { useEffect, useRef, useState } from 'react'
import { useForm, Controller } from "react-hook-form";
import { isEmpty } from 'lodash';

import { useCreateCompanyMutation, useUpdateCompanyMutation, useUploadMutation } from '@quires';
import { getFormErrorMessage } from '@utils/helper'
import { useGlobalContext } from '@contexts/GlobalContext';

import { InputNumber } from 'primereact/inputnumber';
import { classNames } from 'primereact/utils';
import { FileUpload } from 'primereact/fileupload';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { Dialog } from 'primereact/dialog';
import { Button } from 'primereact/button';

const typeOptions = [
  { label: "Small", value: "small" },
  { label: "Medium", value: "medium" },
  { label: "Large", value: "large" },
];

const statusOptions = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
];

export default function CompanyDialog({ actionType, data }) {
  const { openDialog, dialogHandler, disableBtn, setDisableBtn } = useGlobalContext();
  const { control, formState: { errors }, handleSubmit, reset, setValue } = useForm();

  const uploadImage = useUploadMutation()
  const updateRow = useUpdateCompanyMutation();
  const createRow = useCreateCompanyMutation();

  const [image, setImage] = useState({});
  const fileUploadRef = useRef(null);

  const onCancelUpload = () => {
    setImage({})
    fileUploadRef.current.clear();
  };

  const fileUpload = async () => {
    const formData = new FormData()

    formData.append("file", image);
    formData.append("file_type", "image")
    // formData.append("user_id", localStorage.getItem("user_id"));

    const res = await uploadImage.mutateAsync(formData)
    return res?.file_url
  }

  const createHandler = async (payload) => {
    setDisableBtn(true)
    if (!isEmpty(image)) {
      let fileURL = await fileUpload();
      payload = { ...payload, company_logo: fileURL }
      delete payload?.logo
    }

    if (actionType === "update") {
      await updateRow.mutateAsync(
        {
          data: { ...payload, owner_user_id: 1 },
          id: payload?.id
        }, {
        onSuccess: () => { reset(); setDisableBtn(false) },
        onerror: () => setDisableBtn(false)

      })
    } else
      await createRow.mutateAsync({ ...payload, owner_user_id: 1 }, {
        onSuccess: () => { reset(); setDisableBtn(false) },
        onerror: () => setDisableBtn(false)

      })

  }

  useEffect(() => {
    if (actionType === "update") {
      reset(data)
    } else {
      reset()
    }
  }, [actionType]);

  const onClose = () => {
    dialogHandler("companyDialog")
  }

  return (
    <Dialog visible={openDialog.companyDialog}
      style={{ width: '50vw' }}
      breakpoints={{ '960px': '95vw' }}
      header={`${actionType === "update" ? "Update User" : "Add Company"}`}
      modal className="p-fluid"
      onHide={onClose}
    >
      <form onSubmit={handleSubmit(createHandler)} className="w-full flex flex-col justify-center">
        <div className="col-full flex flex-wrap  justify-start py-4 border-[gray]">
          {/* Name */}
          <div className='w-6/12 mb-3 px-2'>
            <div className="field ">
              <label className="form-label text-sm"> Name </label>
              <span className="p-float-label">
                <Controller name="name" control={control}
                  rules={{ required: 'Name is required.' }}
                  render={({ field, fieldState }) => (
                    <InputText
                      id={field.name}
                      {...field}
                      ref={field.ref}
                      className={`w-100  ${classNames({ 'p-invalid': fieldState.invalid })}`}
                    />
                  )} />
              </span>
              {getFormErrorMessage('name', errors)}
            </div>
          </div>

          {/* Description */}
          <div className='w-6/12 mb-3 px-2'>
            <div className="field ">
              <label className="form-label text-sm"> Description </label>
              <span className="p-float-label">
                <Controller name="description" control={control}
                  rules={{ required: 'Description is required.' }}
                  render={({ field, fieldState }) => (
                    <InputText
                      id={field.name}
                      {...field}
                      ref={field.ref}
                      className={`w-100  ${classNames({ 'p-invalid': fieldState.invalid })}`}
                    />
                  )} />
              </span>
              {getFormErrorMessage('description', errors)}
            </div>
          </div>

          {/* Number of allowed cards */}
          <div className='w-6/12 mb-3 px-2'>
            <div className="field ">
              <label className="form-label text-sm"> Number of allowed cards </label>
              <span className="p-float-label">
                <Controller name="number_of_allowed_cards" control={control}
                  rules={{ required: 'Number of allowed cards is required.' }}
                  render={({ field, fieldState }) => (
                    <InputNumber
                      id={field.name}
                      {...field}
                      mode="decimal"
                      onChange={(e) => field.onChange(e.value)}
                      min={1}
                      ref={field.ref}
                      className={`w-100  ${classNames({ 'p-invalid': fieldState.invalid })}`}
                    />
                  )} />
              </span>
              {getFormErrorMessage('number_of_allowed_cards', errors)}
            </div>
          </div>

          {/* Number of allowed users */}
          <div className='w-6/12 mb-3 px-2'>
            <div className="field ">
              <label className="form-label text-sm"> Number of allowed users </label>
              <span className="p-float-label">
                <Controller name="number_of_allowed_users" control={control}
                  rules={{ required: 'Number of allowed users is required.' }}
                  render={({ field, fieldState }) => (
                    <InputNumber
                      id={field.name}
                      {...field}
                      mode="decimal"
                      onChange={(e) => field.onChange(e.value)}
                      min={1}
                      ref={field.ref}
                      className={`w-100  ${classNames({ 'p-invalid': fieldState.invalid })}`}
                    />
                  )} />
              </span>
              {getFormErrorMessage('number_of_allowed_users', errors)}
            </div>
          </div>

          {/* Type */}
          <div className='w-6/12 mb-3 px-2'>
            <label htmlFor="" className='form-label text-sm'>Type</label>
            <Controller name="type" control={control}
              rules={{ required: actionType === "update" ? false : "type is required!" }}
              render={({ field, fieldState }) => (
                <Dropdown
                  id={field.name} {...field}
                  value={field.value}
                  options={typeOptions}
                  onChange={(e) => { field.onChange(e.value); }}
                  optionLabel="label"
                  optionValue="value"
                  ref={field.ref}
                  placeholder="select..."
                  className={`w-100 ${classNames({ 'p-invalid': fieldState.invalid })}`}
                />
              )
              } />
            {getFormErrorMessage('type', errors)}
          </div>

          {/* Status */}
          <div className='w-6/12 mb-3 px-2'>
            <label htmlFor="" className='form-label text-sm'>Status</label>
            <Controller name="status" control={control}
              rules={{ required: actionType === "update" ? false : "Status is required!" }}
              render={({ field, fieldState }) => (
                <Dropdown
                  id={field.name} {...field}
                  value={field.value}
                  options={statusOptions}
                  onChange={(e) => { field.onChange(e.value); }}
                  optionLabel="label"
                  optionValue="value"
                  ref={field.ref}
                  placeholder="select..."
                  className={`w-100 ${classNames({ 'p-invalid': fieldState.invalid })}`}
                />
              )
              } />
            {getFormErrorMessage('status', errors)}
          </div>

          {/* Company Logo Input */}
          <div className={`w-6/12 mb-3 px-2`} >
            <label htmlFor="logo" className={`orm-label text-sm`} >Company Logo</label>
            <Controller name="logo" control={control}
              rules={{ required: false }}
              render={({ field, fieldState }) => (
                <FileUpload
                  mode="basic"
                  name="logo"
                  accept="image/*"
                  maxFileSize={1000000}
                  customUpload
                  ref={field.ref}
                  onSelect={(e) => { setImage(e.files[0]); setValue("logo", e.files[0]) }}
                  onClick={onCancelUpload}
                  chooseOptions={{
                    className: 'upload_separator',
                    label: 'click to upload image...',
                    style: {
                      background: 'transparent',
                      color: 'gray',
                      width: "100%",
                      border: "2px dashed #D9DEE3",
                      fontWeight: "normal",
                      fontSize: "14px",
                    }
                  }}
                />
              )} />
          </div>
        </div>

        <div className="col-full text-center flex items-end justify-start px-24 py-4">
          <Button
            label="Cancel"
            aria-label="Close"
            type="reset"
            className="gray-btn w-3/12  me-2 text-center"
            disabled={disableBtn}
            data-bs-dismiss="modal"
            onClick={onClose} />

          <Button
            label={actionType === "update" ? "Update" : "Add"}
            aria-label="Add"
            type="submit"
            className="main-btn w-auto  ms-2 text-center"
            loading={disableBtn}
          />

        </div>
      </form>
    </Dialog>)
}

