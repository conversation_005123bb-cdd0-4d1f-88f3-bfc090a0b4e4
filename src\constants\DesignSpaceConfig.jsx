import { FaAlignCenter, FaAlignJustify, FaAlignLeft, FaAlignRight } from "react-icons/fa";

export const textElementConfig = {
    isUnderlined: false,
    isBold: false,
    isItalic: false,
    textTransform: "lowercase",
    fontFamily: "Roboto",
    textAlign: "center",
    fontSize: "16px",
    heading: "h4",
    color: "black",
    width: 150,
    height: 35
};

export const typesOptions = [
    { label: "3.7 inch", dimension: { h: 416, w: 240, id:1 } },
    { label: "4 inch", dimension: { h: 300, w: 400 , id:2} },
    { label: "Profile Card", dimension: { h: 600, w: 400, id:3 } },
    // { label: "4.2 inch", dimension: { h: 119.8, w: 67.8 } },
    // { label: "5.56 inch", dimension: { h: 160.6, w: 90.6 } },
];

export const fontFamilyOptions = [
    { label: "Arial", value: "Arial" },
    { label: "Roboto", value: "Roboto" },
    { label: "Times New Roman", value: "Times New Roman" },
    { label: "Courier New", value: "Courier New" },
];

export const fontSizesOptions = [
    { label: "12px", value: "12px" },
    { label: "14px", value: "14px" },
    { label: "16px", value: "16px" },
    { label: "20px", value: "20px" },
    { label: "24px", value: "24px" },
    { label: "28px", value: "28px" },
    { label: "32px", value: "32px" },
];

export const headingsOptions = [
    { label: "Heading 1", value: "h1" },
    { label: "Heading 2", value: "h2" },
    { label: "Heading 3", value: "h3" },
    { label: "Heading 4", value: "h4" },
    { label: "Heading 5", value: "h5" },
    { label: "Heading 6", value: "h6" },
];

export const alignmentsOptions = [
    { icon: <FaAlignLeft className="mx-auto" />, value: "left" },
    { icon: <FaAlignCenter className="mx-auto" />, value: "center" },
    { icon: <FaAlignRight className="mx-auto" />, value: "right" },
    { icon: <FaAlignJustify className="mx-auto" />, value: "justify" },
]

export const colorsOptions = [
    "red",
    "yellow",
    "black",
    "white"
]
 
export const fieldsOptions = [
    { label: "Name", value: "name" },
    { label: "Type", value: "type" },
    { label: "Position", value: "position" },
    { label: "Department", value: "department" },
    { label: "Custom field 1", value: "custom_field_1" },
    { label: "Custom field 2", value: "custom_field_2" },
    { label: "Custom field 3", value: "custom_field_3" },
    { label: "Custom field 4", value: "custom_field_4" },
    { label: "Custom field 5", value: "custom_field_5" },
    { label: "Custom field 6", value: "custom_field_6" },
    { label: "Custom field 7", value: "custom_field_7" },
    { label: "Custom field 8", value: "custom_field_8" },
    { label: "Custom field 9", value: "custom_field_9" },
    { label: "Custom field 10", value: "custom_field_10" },
 ]