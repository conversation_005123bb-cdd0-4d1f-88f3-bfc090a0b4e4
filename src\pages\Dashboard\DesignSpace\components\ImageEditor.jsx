import React, { useState, useEffect } from 'react';
import { useDesignSpace } from '@contexts/DesignSpaceContext';
import axios from 'axios';

// Icons
import { FiX, FiCheck, FiRotateCw, FiRotateCcw } from 'react-icons/fi';
import {
    MdOutlineBrightness6,
    MdOutlineContrast,
    MdOutlineBlurOn,
    MdOutlineOpacity,
    MdOutlineCrop,
    MdOutlineAutoFixHigh,
    MdOutlineColorLens,
    MdOutlinePhotoFilter,
    MdOutlineTune,
    MdFlip,
    MdOutlineFlip
} from 'react-icons/md';
import { BiCrop, BiReset } from 'react-icons/bi';
import { RiScissorsCutFill } from 'react-icons/ri';
import { Slider } from 'primereact/slider';

const ImageEditor = ({ imageId, onClose }) => {
    const { elements, updateElement } = useDesignSpace();

    const [imageElement, setImageElement] = useState(null);
    const [isProcessing, setIsProcessing] = useState(false);
    const [editSettings, setEditSettings] = useState({
        brightness: 100,
        contrast: 100,
        blur: 0,
        opacity: 100,
        rotation: 0,
        flipX: false,
        flipY: false,
        saturation: 100,
        hue: 0,
        sepia: 0,
        grayscale: 0
    });

    // Find the image element
    useEffect(() => {
        const element = elements.find(el => el.id === imageId);
        if (element && element.type === 'img') {
            setImageElement(element);

            // Initialize edit settings from existing filters if any
            if (element.filters) {
                setEditSettings({
                    ...editSettings,
                    ...element.filters
                });
            }
        }
    }, [imageId, elements]);

    // Apply filters to the image
    const applyFilters = () => {
        if (!imageElement) return;

        const filters = {
            brightness: `brightness(${editSettings.brightness}%)`,
            contrast: `contrast(${editSettings.contrast}%)`,
            blur: `blur(${editSettings.blur}px)`,
            opacity: `opacity(${editSettings.opacity}%)`,
            saturation: `saturate(${editSettings.saturation}%)`,
            hue: `hue-rotate(${editSettings.hue}deg)`,
            sepia: `sepia(${editSettings.sepia}%)`,
            grayscale: `grayscale(${editSettings.grayscale}%)`
        };

        const filterString = Object.values(filters).join(' ');
        const transform = `rotate(${editSettings.rotation}deg) scaleX(${editSettings.flipX ? -1 : 1}) scaleY(${editSettings.flipY ? -1 : 1})`;

        updateElement(imageId, {
            filters: editSettings,
            style: {
                filter: filterString,
                transform: transform
            }
        });
    };

    // Apply filters when settings change
    useEffect(() => {
        applyFilters();
    }, [editSettings]);

    // Handle slider changes
    const handleSliderChange = (property, value) => {
        setEditSettings(prev => ({
            ...prev,
            [property]: value
        }));
    };

    // Reset all filters
    const resetFilters = () => {
        setEditSettings({
            brightness: 100,
            contrast: 100,
            blur: 0,
            opacity: 100,
            rotation: 0,
            flipX: false,
            flipY: false,
            saturation: 100,
            hue: 0,
            sepia: 0,
            grayscale: 0
        });
    };

    // Apply preset filters
    const applyPreset = (preset) => {
        switch(preset) {
            case 'vintage':
                setEditSettings(prev => ({
                    ...prev,
                    sepia: 50,
                    contrast: 120,
                    brightness: 90
                }));
                break;
            case 'blackAndWhite':
                setEditSettings(prev => ({
                    ...prev,
                    grayscale: 100,
                    contrast: 120
                }));
                break;
            case 'warm':
                setEditSettings(prev => ({
                    ...prev,
                    hue: 30,
                    saturation: 120,
                    brightness: 105
                }));
                break;
            case 'cool':
                setEditSettings(prev => ({
                    ...prev,
                    hue: 210,
                    saturation: 90,
                    brightness: 105
                }));
                break;
            case 'sharp':
                setEditSettings(prev => ({
                    ...prev,
                    contrast: 130,
                    brightness: 110
                }));
                break;
            default:
                break;
        }
    };

    // Rotate image
    const rotateImage = (direction) => {
        const newRotation = editSettings.rotation + (direction === 'right' ? 90 : -90);
        setEditSettings(prev => ({
            ...prev,
            rotation: newRotation
        }));
    };

    // Flip image
    const flipImage = (axis) => {
        if (axis === 'horizontal') {
            setEditSettings(prev => ({
                ...prev,
                flipX: !prev.flipX
            }));
        } else {
            setEditSettings(prev => ({
                ...prev,
                flipY: !prev.flipY
            }));
        }
    };

    // Remove background from image
    const removeBackground = async () => {
        if (!imageElement || !imageElement.src) return;

        setIsProcessing(true);

        try {
            // Get the image data
            const imageUrl = imageElement.src;

            // Create a new image with transparent background
            const img = new Image();
            img.crossOrigin = "Anonymous";
            img.src = imageUrl;

            await new Promise((resolve, reject) => {
                img.onload = resolve;
                img.onerror = reject;
            });

            // Create canvas to process the image
            const canvas = document.createElement('canvas');
            canvas.width = img.width;
            canvas.height = img.height;
            const ctx = canvas.getContext('2d');

            // Draw the image on the canvas
            ctx.drawImage(img, 0, 0);

            // Get image data
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;

            // Simple background removal algorithm (this is a basic implementation)
            // For a real application, you would use a more sophisticated API

            // Get the corner pixel color (assumed to be background)
            const r = data[0];
            const g = data[1];
            const b = data[2];

            // Threshold for color similarity
            const threshold = 30;

            // Loop through all pixels
            for (let i = 0; i < data.length; i += 4) {
                const rDiff = Math.abs(data[i] - r);
                const gDiff = Math.abs(data[i + 1] - g);
                const bDiff = Math.abs(data[i + 2] - b);

                // If the pixel is similar to the background color, make it transparent
                if (rDiff < threshold && gDiff < threshold && bDiff < threshold) {
                    data[i + 3] = 0; // Set alpha to 0 (transparent)
                }
            }

            // Put the modified image data back on the canvas
            ctx.putImageData(imageData, 0, 0);

            // Convert canvas to data URL
            const newImageUrl = canvas.toDataURL('image/png');

            // Update the image element with the new image
            updateElement(imageId, {
                src: newImageUrl,
                originalSrc: imageElement.originalSrc || imageElement.src // Keep track of the original image
            });

        } catch (error) {
            console.error('Error removing background:', error);
            // Show error message to user
            alert('Failed to remove background. Please try again.');
        } finally {
            setIsProcessing(false);
        }
    };

    if (!imageElement) return null;

    return (
        <div className="image-editor bg-white shadow-lg rounded-lg p-4 w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium">Image Editor</h3>
                <div className="flex space-x-2">
                    <button
                        className="p-2 rounded-full hover:bg-gray-100"
                        onClick={resetFilters}
                        title="Reset All"
                    >
                        <BiReset />
                    </button>
                    <button
                        className="p-2 rounded-full hover:bg-gray-100"
                        onClick={onClose}
                        title="Close"
                    >
                        <FiX />
                    </button>
                </div>
            </div>

            {/* Background Removal */}
            <div className="mb-4">
                <button
                    className={`w-full p-3 rounded-lg flex items-center justify-center ${
                        isProcessing
                            ? 'bg-purple-100 text-purple-600'
                            : 'bg-purple-600 text-white hover:bg-purple-700'
                    }`}
                    onClick={removeBackground}
                    disabled={isProcessing}
                >
                    <RiScissorsCutFill className="mr-2" size={20} />
                    {isProcessing ? 'Processing...' : 'Remove Background'}
                    {isProcessing && <span className="ml-1 animate-pulse">•••</span>}
                </button>
            </div>

            {/* Transformation Controls */}
            <div className="mb-4">
                <h4 className="text-sm font-medium mb-2">Transform</h4>
                <div className="flex space-x-2">
                    <button
                        className="p-2 rounded hover:bg-gray-100"
                        onClick={() => rotateImage('left')}
                        title="Rotate Left"
                    >
                        <FiRotateCcw />
                    </button>
                    <button
                        className="p-2 rounded hover:bg-gray-100"
                        onClick={() => rotateImage('right')}
                        title="Rotate Right"
                    >
                        <FiRotateCw />
                    </button>
                    <button
                        className="p-2 rounded hover:bg-gray-100"
                        onClick={() => flipImage('horizontal')}
                        title="Flip Horizontal"
                    >
                        <MdOutlineFlip className="rotate-90" />
                    </button>
                    <button
                        className="p-2 rounded hover:bg-gray-100"
                        onClick={() => flipImage('vertical')}
                        title="Flip Vertical"
                    >
                        <MdOutlineFlip />
                    </button>
                </div>
            </div>

            {/* Adjustment Controls */}
            <div className="space-y-4">
                <div>
                    <div className="flex items-center mb-1">
                        <MdOutlineBrightness6 className="mr-2" />
                        <label className="text-sm">Brightness: {editSettings.brightness}%</label>
                    </div>
                    <Slider
                        value={editSettings.brightness}
                        onChange={(e) => handleSliderChange('brightness', e.value)}
                        min={0}
                        max={200}
                    />
                </div>

                <div>
                    <div className="flex items-center mb-1">
                        <MdOutlineContrast className="mr-2" />
                        <label className="text-sm">Contrast: {editSettings.contrast}%</label>
                    </div>
                    <Slider
                        value={editSettings.contrast}
                        onChange={(e) => handleSliderChange('contrast', e.value)}
                        min={0}
                        max={200}
                    />
                </div>

                <div>
                    <div className="flex items-center mb-1">
                        <MdOutlineBlurOn className="mr-2" />
                        <label className="text-sm">Blur: {editSettings.blur}px</label>
                    </div>
                    <Slider
                        value={editSettings.blur}
                        onChange={(e) => handleSliderChange('blur', e.value)}
                        min={0}
                        max={10}
                    />
                </div>

                <div>
                    <div className="flex items-center mb-1">
                        <MdOutlineOpacity className="mr-2" />
                        <label className="text-sm">Opacity: {editSettings.opacity}%</label>
                    </div>
                    <Slider
                        value={editSettings.opacity}
                        onChange={(e) => handleSliderChange('opacity', e.value)}
                        min={0}
                        max={100}
                    />
                </div>

                <div>
                    <div className="flex items-center mb-1">
                        <MdOutlineColorLens className="mr-2" />
                        <label className="text-sm">Saturation: {editSettings.saturation}%</label>
                    </div>
                    <Slider
                        value={editSettings.saturation}
                        onChange={(e) => handleSliderChange('saturation', e.value)}
                        min={0}
                        max={200}
                    />
                </div>

                <div>
                    <div className="flex items-center mb-1">
                        <MdOutlineTune className="mr-2" />
                        <label className="text-sm">Hue Rotate: {editSettings.hue}°</label>
                    </div>
                    <Slider
                        value={editSettings.hue}
                        onChange={(e) => handleSliderChange('hue', e.value)}
                        min={0}
                        max={360}
                    />
                </div>
            </div>

            {/* Filter Presets */}
            <div className="mt-4">
                <h4 className="text-sm font-medium mb-2">Filter Presets</h4>
                <div className="grid grid-cols-3 gap-2">
                    <button
                        className="p-2 text-xs rounded bg-gray-100 hover:bg-gray-200"
                        onClick={() => applyPreset('vintage')}
                    >
                        Vintage
                    </button>
                    <button
                        className="p-2 text-xs rounded bg-gray-100 hover:bg-gray-200"
                        onClick={() => applyPreset('blackAndWhite')}
                    >
                        B&W
                    </button>
                    <button
                        className="p-2 text-xs rounded bg-gray-100 hover:bg-gray-200"
                        onClick={() => applyPreset('warm')}
                    >
                        Warm
                    </button>
                    <button
                        className="p-2 text-xs rounded bg-gray-100 hover:bg-gray-200"
                        onClick={() => applyPreset('cool')}
                    >
                        Cool
                    </button>
                    <button
                        className="p-2 text-xs rounded bg-gray-100 hover:bg-gray-200"
                        onClick={() => applyPreset('sharp')}
                    >
                        Sharp
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ImageEditor;
