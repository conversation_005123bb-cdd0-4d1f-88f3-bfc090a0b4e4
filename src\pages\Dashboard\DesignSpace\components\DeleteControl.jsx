import { useState } from 'react'
import { IoTrashOutline } from 'react-icons/io5';
import { Dialog } from 'primereact/dialog';
import { Button } from 'primereact/button';

import { useDesignSpace } from '@contexts/DesignSpaceContext';

function DeleteControl() {
    const { setElements, setSelectedIds, setCanvasBackgroundWithStyle } = useDesignSpace();
    const [showConfirmDialog, setShowConfirmDialog] = useState(false);

    // Clear all elements from the design area
    const clearAllElements = () => {
        setElements([]);
        setSelectedIds([]);
        setCanvasBackgroundWithStyle('transparent', null);

        const targetDiv = document.getElementById('design-space-content');
        if (targetDiv) {
            targetDiv.style.removeProperty('background-color');
            targetDiv.style.removeProperty('background-image');
            targetDiv.style.removeProperty('background-size');
            targetDiv.style.removeProperty('background-position');
            targetDiv.style.removeProperty('background-repeat');
        }
        setShowConfirmDialog(false);
    };

    return (
        <>
            <button
                className="p-2 mx-1 relative group flex items-center bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white rounded-md shadow-sm transition-all duration-300 ease-in-out"
                onClick={() => setShowConfirmDialog(true)}
                title="Clear Canvas"
            >
                <IoTrashOutline size={20} className="mr-1" />
                <span className="text-sm font-medium">Clear Canvas</span>
            </button>

            {/* Confirmation Dialog */}
            <Dialog
                visible={showConfirmDialog}
                onHide={() => setShowConfirmDialog(false)}
                header="Clear Canvas"
                modal
                className="rounded-lg overflow-hidden"
                headerClassName="bg-gradient-to-r from-red-500 to-red-600 text-white"
                contentClassName="p-0"
                style={{ width: '400px' }}
                footer={
                    <div className="flex justify-end gap-2 p-3 bg-gray-50 border-t border-gray-200">
                        <Button
                            label="Cancel"
                            icon="pi pi-times"
                            className="p-button-text"
                            onClick={() => setShowConfirmDialog(false)}
                        />
                        <Button
                            label="Clear All"
                            icon="pi pi-trash"
                            className="p-button-danger"
                            onClick={clearAllElements}
                        />
                    </div>
                }
            >
                <div className="flex flex-col items-center p-6 bg-white">
                    <div className="text-6xl text-red-500 mb-4 bg-red-50 p-4 rounded-full">
                        <IoTrashOutline />
                    </div>
                    <h3 className="text-xl font-bold text-gray-800 mb-2">Clear All Elements</h3>
                    <p className="text-center text-gray-600">
                        Are you sure you want to clear all elements from the canvas? This action cannot be undone.
                    </p>
                </div>
            </Dialog>
        </>
    )
}

export default DeleteControl