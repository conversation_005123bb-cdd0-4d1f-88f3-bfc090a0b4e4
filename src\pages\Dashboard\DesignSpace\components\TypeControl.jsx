import { useEffect, useMemo, useState, useCallback } from 'react';
import { useQueryClient } from 'react-query';
import PropTypes from 'prop-types';

import { Dropdown } from 'primereact/dropdown';

import { useDesignSpace } from '@contexts/DesignSpaceContext';
import { useGetCardsTypes } from "@quires/template";
import { isEmpty } from 'lodash';

// Default card types if API fails
const DEFAULT_CARD_TYPES = [
    {
        id: 'default-business-card',
        name: 'Business Card',
        setting: {
            id: 'default-business-card',
            width: 350,
            height: 200
        }
    },
    {
        id: 'default-instagram',
        name: 'Instagram Post',
        setting: {
            id: 'default-instagram',
            width: 1080,
            height: 1080
        }
    },
    {
        id: 'default-id-badge',
        name: 'ID Badge',
        setting: {
            id: 'default-id-badge',
            width: 300,
            height: 450
        }
    }
];

function TypeControl({ hideLabel = false }) {
    const queryClient = useQueryClient();
    const { data: cardsTypesData, isLoading } = useGetCardsTypes();

    const { cardType, setCardType } = useDesignSpace();
    const [typesOptions, setTypesOptions] = useState(DEFAULT_CARD_TYPES);

    // Get data from query client or from direct hook
    useEffect(() => {
        try {
            const cachedData = queryClient.getQueryData('getCardsTypes');
            if (cachedData && Array.isArray(cachedData) && cachedData.length > 0) {
                setTypesOptions(cachedData);
            } else if (cardsTypesData && Array.isArray(cardsTypesData) && cardsTypesData.length > 0) {
                setTypesOptions(cardsTypesData);
            } else {
                console.log('Using default card types');
                setTypesOptions(DEFAULT_CARD_TYPES);
            }
        } catch (error) {
            console.error('Error loading card types:', error);
            setTypesOptions(DEFAULT_CARD_TYPES);
        }
    }, [queryClient, cardsTypesData]);

    // Create options from typesOptions with safe fallbacks
    const options = useMemo(() => {
        try {
            if (!typesOptions || !Array.isArray(typesOptions) || typesOptions.length === 0) {
                return DEFAULT_CARD_TYPES;
            }

            return typesOptions.map(item => ({
                name: item.name || 'Unnamed Card',
                setting: {
                    id: item.id || `card-${item.name?.replace(/\s+/g, '-').toLowerCase() || 'unnamed'}`,
                    width: (item.setting?.width) || 300,
                    height: (item.setting?.height) || 200
                }
            }));
        } catch (error) {
            console.error('Error creating options:', error);
            return DEFAULT_CARD_TYPES;
        }
    }, [typesOptions]);

    // Set default card type if none is selected - only runs once when options are loaded
    useEffect(() => {
        try {
            // Only set card type if it's empty and we have options
            if (isEmpty(cardType) && options && options.length > 0) {
                setCardType({ ...options[0]?.setting });
            }
        } catch (error) {
            console.error('Error setting default card type:', error);
            if (DEFAULT_CARD_TYPES.length > 0) {
                setCardType({ ...DEFAULT_CARD_TYPES[0].setting });
            }
        }
        // No cleanup function to avoid resetting card type
        // This effect should only run when options change
    }, [options]); // Remove cardType and setCardType dependencies
    // Loading and error states
    const isDataReady = options && options.length > 0;

    // Memoize dropdown options to prevent re-renders
    const dropdownOptions = useMemo(() => {
        return isDataReady ? options : DEFAULT_CARD_TYPES;
    }, [isDataReady, options]);

    // Handle dropdown change safely - memoized to prevent re-renders
    const handleDropdownChange = useCallback((e) => {
        try {
            if (e && e.value) {
                setCardType(e.value);
            }
        } catch (error) {
            console.error('Error changing card type:', error);
        }
    }, [setCardType]);

    // Memoize the entire dropdown component to prevent re-renders
    const dropdownComponent = useMemo(() => {
        if (isLoading) {
            return <div className="rounded-[6px] me-3 text-sm bg-gray-100 p-2 text-gray-500">Loading...</div>;
        }

        return (
            <Dropdown
                className='rounded-[6px] me-3 text-[black] text-sm'
                optionLabel="name"
                optionValue="setting"
                value={cardType}
                options={dropdownOptions}
                onChange={handleDropdownChange}
                placeholder="Select a Type"
                panelClassName="text-sm"
            />
        );
    }, [isLoading, cardType, dropdownOptions, handleDropdownChange]);

    return (
        <div className="flex flex-col">
            {!hideLabel && <label className="mr-1 text-sm">Card Type</label>}
            {dropdownComponent}
        </div>
    )
}

TypeControl.propTypes = {
    hideLabel: PropTypes.bool,
};

export default TypeControl