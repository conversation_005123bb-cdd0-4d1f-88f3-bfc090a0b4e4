import { useDesignSpace } from "@contexts/DesignSpaceContext";

const icons = [
  "https://file-tech-test.livaatverse.com/uploads/file---1733395522.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395672.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395711.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395786.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396147.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396169.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395845.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395869.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395897.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395925.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395957.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396014.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396030.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396048.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396065.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396086.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396110.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396128.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396344.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396361.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396381.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396398.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395975.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395993.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396212.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396261.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396229.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396415.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733395813.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396432.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396279.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396294.svg",
  "https://file-tech-test.livaatverse.com/uploads/file---1733396320.svg"
]

function IconsSettings() {
  const { addElement } = useDesignSpace()
  return (
    <div className="flex flex-wrap justify-start mx-auto">
      {
        icons.map((icon, index) => {
          return (
            <img
             loading="lazy"
              className="m-3"
              width={"30px"}
              key={index}
              src={icon}
              onClick={() => { addElement("icon", icon) }} />)
        })
      }
    </div>
  )
}

export default IconsSettings