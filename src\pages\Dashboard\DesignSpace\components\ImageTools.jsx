import React from 'react';
import { Button } from 'primereact/button';
import { Slider } from 'primereact/slider';
import { useDesignSpace } from '@contexts/DesignSpaceContext';

// This component implements the image editing tools for crop, filters, adjust, and effects
const ImageTools = ({ 
    imageEditMode, 
    selectedImage, 
    generatedImage, 
    isProcessing,
    cropRatio, 
    setCropRatio,
    cropRotation, 
    setCropRotation,
    brightness, 
    setBrightness,
    contrast, 
    setContrast,
    saturation, 
    setSaturation,
    hue, 
    setHue,
    selectedFilter, 
    setSelectedFilter,
    effectType, 
    setEffectType,
    effectIntensity, 
    setEffectIntensity,
    imageFilters,
    imageEffects,
    cropRatioOptions,
    updateSelectedImage,
    setResultMessage
}) => {
    return (
        <>
            {/* Crop Image Tab */}
            {imageEditMode === 'crop' && (
                <div>
                    <div className="mb-3">
                        <label className="block text-sm font-medium mb-2">Select Crop Ratio</label>
                        <div className="grid grid-cols-3 gap-2">
                            {cropRatioOptions.map((option) => (
                                <div
                                    key={option.value}
                                    className={`p-2 border rounded cursor-pointer text-center ${cropRatio === option.value ? 'border-green-500 bg-green-50' : 'border-gray-200'}`}
                                    onClick={() => setCropRatio(option.value)}
                                >
                                    {option.label}
                                </div>
                            ))}
                        </div>
                    </div>

                    <div className="mb-3">
                        <label className="block text-sm font-medium mb-1">Rotation: {cropRotation}°</label>
                        <Slider
                            value={cropRotation}
                            onChange={(e) => setCropRotation(e.value)}
                            min={-180}
                            max={180}
                            disabled={isProcessing && (!generatedImage && !selectedImage)}
                        />
                    </div>

                    <div className="flex justify-between mb-4">
                        <div className="flex gap-2">
                            <Button
                                icon="pi pi-refresh"
                                className="p-button-outlined p-button-sm flex-1"
                                tooltip="Rotate Left 90°"
                                onClick={() => {
                                    if (selectedImage) {
                                        updateSelectedImage({
                                            transform: 'rotate(-90deg)'
                                        });
                                    }
                                }}
                                disabled={isProcessing && (!generatedImage && !selectedImage)}
                            />
                            <Button
                                icon="pi pi-refresh"
                                className="p-button-outlined p-button-sm flex-1"
                                tooltip="Rotate Right 90°"
                                onClick={() => {
                                    if (selectedImage) {
                                        updateSelectedImage({
                                            transform: 'rotate(90deg)'
                                        });
                                    }
                                }}
                                disabled={isProcessing && (!generatedImage && !selectedImage)}
                            />
                        </div>
                        <Button
                            label={isProcessing ? "Applying..." : "Apply Crop"}
                            icon="pi pi-crop"
                            loading={isProcessing}
                            onClick={(e) => {
                                e.stopPropagation();
                                // Apply crop to the selected image
                                if (selectedImage) {
                                    // Apply the crop and rotation
                                    updateSelectedImage({
                                        transform: `rotate(${cropRotation}deg)`,
                                        cropRatio: cropRatio
                                    });
                                    
                                    setResultMessage({
                                        severity: 'success',
                                        summary: 'Success',
                                        detail: 'Crop applied successfully'
                                    });
                                } else {
                                    setResultMessage({
                                        severity: 'warn',
                                        summary: 'Warning',
                                        detail: 'Please select an image first'
                                    });
                                }
                            }}
                            disabled={(!generatedImage && !selectedImage) || isProcessing}
                            className="p-button-sm"
                        />
                    </div>
                </div>
            )}

            {/* Filters Image Tab */}
            {imageEditMode === 'filters' && (
                <div>
                    <div className="mb-3">
                        <label className="block text-sm font-medium mb-2">Select Filter</label>
                        <div className="grid grid-cols-2 gap-2">
                            {Object.entries(imageFilters).map(([key, filter]) => (
                                <div
                                    key={key}
                                    className={`p-2 border rounded cursor-pointer text-center ${selectedFilter === key ? 'border-green-500 bg-green-50' : 'border-gray-200'}`}
                                    onClick={() => setSelectedFilter(key)}
                                >
                                    {filter.label}
                                </div>
                            ))}
                        </div>
                    </div>

                    <div className="flex justify-between mb-4">
                        <Button
                            label="Reset"
                            icon="pi pi-refresh"
                            className="p-button-outlined p-button-sm"
                            onClick={() => setSelectedFilter('none')}
                            disabled={isProcessing && (!generatedImage && !selectedImage)}
                        />
                        <Button
                            label={isProcessing ? "Applying..." : "Apply Filter"}
                            icon="pi pi-image"
                            loading={isProcessing}
                            onClick={(e) => {
                                e.stopPropagation();
                                // Apply the filter to the selected image
                                if (selectedImage) {
                                    // Get the filter
                                    const filterValue = selectedFilter !== 'none' ? imageFilters[selectedFilter].filter : 'none';
                                    
                                    // Update the selected image with the filter
                                    updateSelectedImage({
                                        filter: filterValue
                                    });
                                    
                                    setResultMessage({
                                        severity: 'success',
                                        summary: 'Success',
                                        detail: 'Filter applied successfully'
                                    });
                                } else {
                                    setResultMessage({
                                        severity: 'warn',
                                        summary: 'Warning',
                                        detail: 'Please select an image first'
                                    });
                                }
                            }}
                            disabled={(!generatedImage && !selectedImage) || isProcessing}
                            className="p-button-sm"
                        />
                    </div>
                </div>
            )}

            {/* Adjust Image Tab */}
            {imageEditMode === 'adjust' && (
                <div>
                    <div className="mb-3">
                        <label className="block text-sm font-medium mb-1">Brightness: {brightness}%</label>
                        <Slider
                            value={brightness}
                            onChange={(e) => setBrightness(e.value)}
                            min={0}
                            max={200}
                            disabled={isProcessing && (!generatedImage && !selectedImage)}
                        />
                    </div>
                    
                    <div className="mb-3">
                        <label className="block text-sm font-medium mb-1">Contrast: {contrast}%</label>
                        <Slider
                            value={contrast}
                            onChange={(e) => setContrast(e.value)}
                            min={0}
                            max={200}
                            disabled={isProcessing && (!generatedImage && !selectedImage)}
                        />
                    </div>
                    
                    <div className="mb-3">
                        <label className="block text-sm font-medium mb-1">Saturation: {saturation}%</label>
                        <Slider
                            value={saturation}
                            onChange={(e) => setSaturation(e.value)}
                            min={0}
                            max={200}
                            disabled={isProcessing && (!generatedImage && !selectedImage)}
                        />
                    </div>
                    
                    <div className="mb-3">
                        <label className="block text-sm font-medium mb-1">Hue Rotation: {hue}°</label>
                        <Slider
                            value={hue}
                            onChange={(e) => setHue(e.value)}
                            min={0}
                            max={360}
                            disabled={isProcessing && (!generatedImage && !selectedImage)}
                        />
                    </div>

                    <div className="flex justify-between mb-4">
                        <Button
                            label="Reset"
                            icon="pi pi-refresh"
                            className="p-button-outlined p-button-sm"
                            onClick={() => {
                                setBrightness(100);
                                setContrast(100);
                                setSaturation(100);
                                setHue(0);
                            }}
                            disabled={isProcessing && (!generatedImage && !selectedImage)}
                        />
                        <Button
                            label={isProcessing ? "Applying..." : "Apply Adjustments"}
                            icon="pi pi-image"
                            loading={isProcessing}
                            onClick={(e) => {
                                e.stopPropagation();
                                // Apply adjustments to the selected image
                                if (selectedImage) {
                                    // Create the filter string
                                    const filterValue = `brightness(${brightness/100}) contrast(${contrast/100}) saturate(${saturation/100}) hue-rotate(${hue}deg)`;
                                    
                                    // Update the selected image with the filter
                                    updateSelectedImage({
                                        filter: filterValue
                                    });
                                    
                                    setResultMessage({
                                        severity: 'success',
                                        summary: 'Success',
                                        detail: 'Adjustments applied successfully'
                                    });
                                } else {
                                    setResultMessage({
                                        severity: 'warn',
                                        summary: 'Warning',
                                        detail: 'Please select an image first'
                                    });
                                }
                            }}
                            disabled={(!generatedImage && !selectedImage) || isProcessing}
                            className="p-button-sm"
                        />
                    </div>
                </div>
            )}

            {/* Effects Image Tab */}
            {imageEditMode === 'effects' && (
                <div>
                    <div className="mb-3">
                        <label className="block text-sm font-medium mb-2">Select Effect</label>
                        <div className="grid grid-cols-2 gap-2">
                            {Object.entries(imageEffects).map(([key, effect]) => (
                                <div
                                    key={key}
                                    className={`p-2 border rounded cursor-pointer text-center ${effectType === key ? 'border-green-500 bg-green-50' : 'border-gray-200'}`}
                                    onClick={() => setEffectType(key)}
                                >
                                    {effect.label}
                                </div>
                            ))}
                        </div>
                    </div>

                    <div className="mb-3">
                        <label className="block text-sm font-medium mb-1">Effect Intensity: {effectIntensity}%</label>
                        <Slider
                            value={effectIntensity}
                            onChange={(e) => setEffectIntensity(e.value)}
                            min={0}
                            max={100}
                            disabled={isProcessing && (!generatedImage && !selectedImage)}
                        />
                    </div>

                    <div className="flex justify-between mb-4">
                        <Button
                            label="Reset"
                            icon="pi pi-refresh"
                            className="p-button-outlined p-button-sm"
                            onClick={() => {
                                setEffectType('none');
                                setEffectIntensity(50);
                            }}
                            disabled={isProcessing && (!generatedImage && !selectedImage)}
                        />
                        <Button
                            label={isProcessing ? "Applying..." : "Apply Effect"}
                            icon="pi pi-image"
                            loading={isProcessing}
                            onClick={(e) => {
                                e.stopPropagation();
                                // Apply the effect to the selected image
                                if (selectedImage) {
                                    // Get the effect filter
                                    let effectFilter = 'none';
                                    if (effectType !== 'none') {
                                        effectFilter = imageEffects[effectType].filter;
                                        // Adjust intensity if needed
                                        if (effectType === 'shadow' || effectType === 'glow') {
                                            const intensity = effectIntensity / 50;
                                            effectFilter = effectFilter.replace('5px', `${Math.round(intensity * 5)}px`);
                                        }
                                    }
                                    
                                    // Update the selected image with the effect
                                    updateSelectedImage({
                                        filter: effectFilter,
                                        className: effectType !== 'none' && imageEffects[effectType].className ? 
                                            imageEffects[effectType].className : ''
                                    });
                                    
                                    setResultMessage({
                                        severity: 'success',
                                        summary: 'Success',
                                        detail: 'Effect applied successfully'
                                    });
                                } else {
                                    setResultMessage({
                                        severity: 'warn',
                                        summary: 'Warning',
                                        detail: 'Please select an image first'
                                    });
                                }
                            }}
                            disabled={(!generatedImage && !selectedImage) || isProcessing}
                            className="p-button-sm"
                        />
                    </div>
                </div>
            )}
        </>
    );
};

export default ImageTools;
