import React from "react";
import { Dialog } from 'primereact/dialog';
import { But<PERSON> } from 'primereact/button';

const ErrorUpgradePackage = ({ isModalOpen2, setIsModalOpen2, message }) => {
  return (
    <Dialog
      visible={isModalOpen2}
      onHide={() => setIsModalOpen2(false)}
      header="Error in Package Upgrade"
      style={{ width: '50vw' }}
      modal
      className="p-fluid"
    >
      <div style={{ marginBottom: '20px', fontSize: '16px', color: '#555' }}>
        <p>{message}</p>
      </div>
      <Button
        label="Close"
        onClick={() => setIsModalOpen2(false)}
        style={{ marginTop: '20px' }} 
        className="p-button-danger"
      />
    </Dialog>
  );
};

export default ErrorUpgradePackage;