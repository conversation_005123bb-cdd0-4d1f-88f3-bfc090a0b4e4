// Upload Templates for DesignSpace
// This file contains professional templates for uploaded content

// Image Upload Templates
export const imageUploadTemplates = [
  {
    id: 'iu-001',
    name: 'Image with Frame',
    width: 1080,
    height: 1080,
    thumbnail: 'https://placehold.co/1080x1080/f8fafc/333333?text=Image+Frame',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1080,
        height: 1080,
        backgroundColor: '#f8fafc',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 90,
        y: 90,
        width: 900,
        height: 900,
        backgroundColor: '#e2e8f0',
        borderColor: '#ffffff',
        borderWidth: 20,
        shadow: true,
      },
      {
        id: 'el_3',
        type: 'text',
        x: 90,
        y: 1000,
        width: 900,
        height: 40,
        value: 'YOUR UPLOADED IMAGE',
        fontSize: 24,
        fontWeight: 'bold',
        color: '#333333',
        textAlign: 'center',
      },
    ]
  },
  {
    id: 'iu-002',
    name: 'Image with Caption',
    width: 1080,
    height: 1200,
    thumbnail: 'https://placehold.co/1080x1200/f8fafc/333333?text=Image+Caption',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1080,
        height: 1200,
        backgroundColor: '#ffffff',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 40,
        y: 40,
        width: 1000,
        height: 800,
        backgroundColor: '#e2e8f0',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 40,
        y: 880,
        width: 1000,
        height: 80,
        value: 'IMAGE TITLE',
        fontSize: 48,
        fontWeight: 'bold',
        color: '#333333',
        textAlign: 'center',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 40,
        y: 960,
        width: 1000,
        height: 120,
        value: 'Add your caption or description here. Describe what makes this image special or provide context about when and where it was taken.',
        fontSize: 20,
        fontWeight: 'normal',
        color: '#666666',
        textAlign: 'center',
      },
      {
        id: 'el_5',
        type: 'text',
        x: 40,
        y: 1100,
        width: 1000,
        height: 40,
        value: 'Photo by: Your Name',
        fontSize: 16,
        fontWeight: 'normal',
        color: '#999999',
        textAlign: 'center',
      },
    ]
  },
  {
    id: 'iu-003',
    name: 'Image Collage',
    width: 1080,
    height: 1080,
    thumbnail: 'https://placehold.co/1080x1080/f8fafc/333333?text=Image+Collage',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1080,
        height: 1080,
        backgroundColor: '#ffffff',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 20,
        y: 20,
        width: 520,
        height: 520,
        backgroundColor: '#e2e8f0',
      },
      {
        id: 'el_3',
        type: 'shape',
        shapeType: 'rectangle',
        x: 560,
        y: 20,
        width: 500,
        height: 500,
        backgroundColor: '#e2e8f0',
      },
      {
        id: 'el_4',
        type: 'shape',
        shapeType: 'rectangle',
        x: 20,
        y: 560,
        width: 500,
        height: 500,
        backgroundColor: '#e2e8f0',
      },
      {
        id: 'el_5',
        type: 'shape',
        shapeType: 'rectangle',
        x: 540,
        y: 540,
        width: 520,
        height: 520,
        backgroundColor: '#e2e8f0',
      },
    ]
  }
];

// Document Upload Templates
export const documentUploadTemplates = [
  {
    id: 'du-001',
    name: 'Document Cover',
    width: 800,
    height: 1100,
    thumbnail: 'https://placehold.co/800x1100/3b82f6/ffffff?text=Document+Cover',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 800,
        height: 1100,
        backgroundColor: '#3b82f6',
        style: { background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)' },
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 50,
        y: 50,
        width: 700,
        height: 1000,
        backgroundColor: 'transparent',
        borderColor: '#ffffff',
        borderWidth: 2,
      },
      {
        id: 'el_3',
        type: 'text',
        x: 100,
        y: 200,
        width: 600,
        height: 200,
        value: 'DOCUMENT TITLE',
        fontSize: 60,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 100,
        y: 400,
        width: 600,
        height: 100,
        value: 'SUBTITLE OR DESCRIPTION',
        fontSize: 30,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_5',
        type: 'shape',
        shapeType: 'rectangle',
        x: 300,
        y: 550,
        width: 200,
        height: 2,
        backgroundColor: '#ffffff',
      },
      {
        id: 'el_6',
        type: 'text',
        x: 100,
        y: 600,
        width: 600,
        height: 100,
        value: 'PREPARED BY:\nYOUR NAME OR COMPANY',
        fontSize: 24,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_7',
        type: 'text',
        x: 100,
        y: 900,
        width: 600,
        height: 50,
        value: 'DATE: JANUARY 2023',
        fontSize: 20,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'center',
      },
    ]
  }
];

// Export all upload templates
export const allUploadTemplates = [
  ...imageUploadTemplates,
  ...documentUploadTemplates
];
