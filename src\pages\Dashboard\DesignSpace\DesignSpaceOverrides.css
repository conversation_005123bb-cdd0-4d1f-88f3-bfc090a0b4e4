/* Override styles to remove transparency from design space */

.design-space {
  position: relative;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
  transition: transform 0.3s ease, box-shadow 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border-radius: 8px;
  transform-style: preserve-3d;
  perspective: 1200px;
  backface-visibility: hidden;
  border: 1px solid rgba(0, 0, 0, 0.2) !important;
  z-index: 10 !important;
  transform-origin: center center !important;
  will-change: transform;
 
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  filter: none !important;
}

/* Design space content - ensure uniform background */
#design-space-content {
  
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  filter: none !important;
  background-image: none !important;
  background-attachment: fixed !important;
  background-size: auto !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
}

/* Remove any gradient or blur effects from design space container */
.design-space-container {
  position: relative;
  z-index: 5;
  background: transparent !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  filter: none !important;
}

/* Hide design elements from the design area */
.design-elements-container {
  z-index: 1;
  pointer-events: none;
}

/* Smart positioning for element controls */
.draggable-element.top-edge .element-controls {
  top: auto !important;
  bottom: -55px !important;
}

.draggable-element.top-edge .element-controls::after {
  bottom: auto !important;
  top: -6px !important;
}

.draggable-element.right-edge .element-controls {
  right: auto !important;
  left: -10px !important;
}

.draggable-element.right-edge .element-controls::after {
  right: auto !important;
  left: 15px !important;
}

/* Remove any background effects from the main design space area */
.design-space > div {
  opacity: 1 !important;
 
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  filter: none !important;
}

/* Ensure zoom container allows overflow for zoomed content */
.design-space-container {
  overflow: auto !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  min-height: 100% !important;
  padding: 50px !important;
  background: transparent !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  filter: none !important;
}

/* Keep shadow but make it more solid */

/* Remove hover effects that change transparency */

/* Extra force: Remove any scale/transform from libraries (framer-motion, Tailwind, etc) on hover when zoom is not 100% */
.design-space.no-hover-zoom:hover, .design-space.no-hover-zoom.motion-hover, .design-space.no-hover-zoom:hover, .design-space.no-hover-zoom:active {
  transform: none !important;
  scale: 1 !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
  transition: none !important;
  filter: none !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  
}

/* Make corner marks more visible */
.design-space .corner-mark {
  border-color: rgba(0, 0, 0, 0.4) !important;
}

/* Remove forced white background */
.design-space > div {
  opacity: 1 !important;
}

/* Zoom functionality styles */
.design-space[data-design-space="true"] {
  transform-origin: center center !important;
  will-change: transform !important;
  backface-visibility: hidden !important;
  -webkit-backface-visibility: hidden !important;
}

/* Smooth zoom transitions */
.design-space.zooming {
  transition: transform 0.3s ease !important;
}

/* ===== TOOLBAR STYLING - SEPARATED FROM DESIGN SPACE ===== */

/* Animation for toolbar appearance */
@keyframes toolbarSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(0.9) translateY(0);
  }
}

/* Element controls - completely separate from design space styling */
.element-controls {
  background: #23272f !important; /* لون داكن ثابت بدون شفافية */
  opacity: 1 !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  filter: none !important;
  background-blend-mode: normal !important;
  mix-blend-mode: normal !important;
  transform: scale(0.7);
  gap: 2px;
  font-size: 10px;
  padding: 3px 6px;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  border-radius: 10px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1), inset 0 -1px 0 rgba(0, 0, 0, 0.2) !important;
  z-index: 1000 !important;
  position: absolute !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  animation: toolbarSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.element-controls:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5), 0 6px 20px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.15), inset 0 -1px 0 rgba(0, 0, 0, 0.3) !important;
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 50%, #34495e 100%) !important;
}

.element-control-btn {
  width: 24px !important;
  height: 24px !important;
  min-width: 0 !important;
  min-height: 0 !important;
  font-size: 10px !important;
  padding: 0 !important;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1) !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.08) 100%) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 6px !important;
  color: #ffffff !important;
  position: relative !important;
  overflow: hidden !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

.element-control-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.element-control-btn:hover::before {
  left: 100%;
}

.element-control-btn:hover {
  transform: translateY(-2px) scale(1.05) !important;
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.8) 0%, rgba(6, 182, 212, 0.6) 100%) !important;
  color: #ffffff !important;
  box-shadow: 0 6px 20px rgba(6, 182, 212, 0.4), 0 2px 8px rgba(6, 182, 212, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(6, 182, 212, 0.8) !important;
}

.element-control-btn:active {
  transform: translateY(0) scale(0.98) !important;
  transition: all 0.1s ease !important;
}

.element-controls .element-control-btn:hover, 
.element-controls .element-control-btn:focus {
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.9) 0%, rgba(6, 182, 212, 0.7) 100%) !important;
  box-shadow: 0 8px 25px rgba(6, 182, 212, 0.5), 0 4px 12px rgba(6, 182, 212, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
  border-color: rgba(6, 182, 212, 1) !important;
}

/* Enhanced button icons */
.element-control-btn svg {
  width: 14px !important;
  height: 14px !important;
  transition: all 0.25s ease !important;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3)) !important;
}

.element-control-btn:hover svg {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.4)) !important;
  transform: scale(1.1) !important;
}

/* Special styling for different button types */
.element-control-btn.rotate-btn:hover {
  background: linear-gradient(135deg, rgba(139, 61, 255, 0.8) 0%, rgba(139, 61, 255, 0.6) 100%) !important;
  box-shadow: 0 6px 20px rgba(139, 61, 255, 0.4), 0 2px 8px rgba(139, 61, 255, 0.3) !important;
  border-color: rgba(139, 61, 255, 0.8) !important;
}

.element-control-btn.delete-btn:hover {
  background: linear-gradient(135deg, rgba(255, 92, 92, 0.8) 0%, rgba(255, 92, 92, 0.6) 100%) !important;
  box-shadow: 0 6px 20px rgba(255, 92, 92, 0.4), 0 2px 8px rgba(255, 92, 92, 0.3) !important;
  border-color: rgba(255, 92, 92, 0.8) !important;
}

.element-control-btn.duplicate-btn:hover {
  background: linear-gradient(135deg, rgba(0, 196, 140, 0.8) 0%, rgba(0, 196, 140, 0.6) 100%) !important;
  box-shadow: 0 6px 20px rgba(0, 196, 140, 0.4), 0 2px 8px rgba(0, 196, 140, 0.3) !important;
  border-color: rgba(0, 196, 140, 0.8) !important;
}

/* Glow effect for active buttons */
.element-control-btn.active-mode {
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.9) 0%, rgba(6, 182, 212, 0.7) 100%) !important;
  box-shadow: 0 0 20px rgba(6, 182, 212, 0.6), 0 4px 12px rgba(6, 182, 212, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
  border-color: rgba(6, 182, 212, 1) !important;
}

/* Ensure toolbar doesn't inherit design space styles */
.draggable-element .element-controls,
.draggable-element .element-controls *,
.element-controls,
.element-controls * {
  background: initial !important;
  backdrop-filter: initial !important;
  -webkit-backdrop-filter: initial !important;
  filter: initial !important;
}

/* Force toolbar to maintain dark background matching triangle pointer */
.draggable-element .element-controls,
.element-controls {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%) !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  filter: none !important;
  background-image: none !important;
  background-attachment: fixed !important;
  background-size: auto !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
}

/* Override any white background that might be applied */
.element-controls,
.element-controls *,
.draggable-element .element-controls,
.draggable-element .element-controls * {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%) !important;
}

/* Ensure toolbar elements don't inherit any background from parent elements */
.draggable-element .element-controls *,
.element-controls * {
  background: initial !important;
  backdrop-filter: initial !important;
  -webkit-backdrop-filter: initial !important;
  filter: initial !important;
}

/* But force the main toolbar to keep its dark background */
.element-controls {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%) !important;
}

/* Arrow position for default (top) - HIDDEN */
.element-controls::after {
  display: none !important;
}

/* Arrow position for top edge (when toolbar is at bottom) - HIDDEN */
.element-controls.top-edge::after {
  display: none !important;
}

/* Arrow position for right edge - HIDDEN */
.element-controls.right-edge::after {
  display: none !important;
}

/* Arrow position for left edge - HIDDEN */
.element-controls.left-edge::after {
  display: none !important;
}

/* Arrow position for both top and right edges - HIDDEN */
.element-controls.top-edge.right-edge::after {
  display: none !important;
}

/* Arrow position for both top and left edges - HIDDEN */
.element-controls.top-edge.left-edge::after {
  display: none !important;
}

/* Fix transparency of ColorPicker dropdown menu */
.color-picker-btn + .absolute,
.color-picker-dropdown {
  background: #fff;
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
  filter: none;
  box-shadow: 0 8px 32px rgba(0,0,0,0.18), 0 2px 8px rgba(0,0,0,0.10);
  opacity: 1;
}

/* Radical fix for color boxes in ColorPicker - allows inline style to appear */
.color-picker-dropdown button[style][title]:not([type]),
.color-picker-dropdown .grid button[style]:not([type]) {
  background: unset !important;
  background-color: unset !important;
  box-shadow: 0 1px 4px rgba(0,0,0,0.10);
  border-radius: 4px;
  border: 1px solid #eee;
  opacity: 1 !important;
  min-width: 0;
  min-height: 0;
  padding: 0;
}

/* Exclude gradient preview from any override */
/* .color-picker-dropdown .gradient-preview, .color-picker-dropdown .gradient-preview * {
  background: unset !important;
  background-color: unset !important;
  background-image: unset !important;
  filter: none !important;
  isolation: auto !important;
  mix-blend-mode: normal !important;
  background-blend-mode: normal !important;
} */

/* Exclude the add new gradient color button from gradient background */
/* .color-picker-dropdown .add-gradient-color-btn {
  background: unset !important;
  background-color: #fff !important;
  color: #888 !important;
} */

#gradient-preview-override {
  background: var(--gradient-preview-bg, initial) !important;
  background-image: var(--gradient-preview-bg, initial) !important;
  background-color: unset !important;
  filter: none !important;
  isolation: auto !important;
  mix-blend-mode: normal !important;
  background-blend-mode: normal !important;
}

.color-picker-dropdown button,
.color-picker-dropdown button.w-full.py-2.px-4.bg-gradient-to-r.from-purple-600.to-blue-600 {
  background: linear-gradient(90deg, #7c3aed 0%, #2563eb 100%) !important;
  color: #fff !important;
  border-radius: 1rem !important;
  font-weight: bold !important;
  font-size: 1rem !important;
  box-shadow: 0 2px 8px rgba(44,62,80,0.10) !important;
  border: none !important;
}

.color-picker-dropdown button.w-full.py-2.px-4.bg-gradient-to-r.from-purple-600.to-blue-600:hover {
  background: linear-gradient(90deg, #6d28d9 0%, #1d4ed8 100%) !important;
  color: #fff !important;
}

/* Highest specificity for the Apply Gradient button */
.color-picker-dropdown .color-picker-apply-gradient-btn.color-picker-apply-gradient-btn.color-picker-apply-gradient-btn,
.color-picker-apply-gradient-btn.color-picker-apply-gradient-btn.color-picker-apply-gradient-btn {
  background: linear-gradient(90deg, #7c3aed 0%, #06b6d4 100%) !important;
  background-color: #7c3aed !important;
  color: #fff !important;
  box-shadow: 0 2px 8px rgba(44,62,80,0.10) !important;
  border-radius: 1rem !important;
  font-weight: bold !important;
  font-size: 1rem !important;
  border: none !important;
  opacity: 1 !important;
  filter: none !important;
  isolation: auto !important;
  mix-blend-mode: normal !important;
  background-blend-mode: normal !important;
  z-index: 99999 !important;
  position: relative !important;
}

/* ===== Force color swatch buttons to show their real color even inside element-controls ===== */
.element-controls .color-picker-dropdown button[style*="background-color"] {
  background: none !important;
  background-color: unset !important;
  box-shadow: none !important;
  border: 1.5px solid #d1d5db !important;
}

.element-controls .color-picker-dropdown button[style*="background-color"]::after {
  content: '';
  display: block;
  width: 100%;
  height: 100%;
  border-radius: 3px;
  background: inherit !important;
  background-color: inherit !important;
}

/* Crop handles for image cropping (منتصف الجوانب) */
.crop-handle {
  width: 10px;
  height: 10px;
  background: #fff;
  border: 1.5px solid #00b4ff;
  border-radius: 50%;
  box-shadow: 0 1px 4px rgba(0,180,255,0.15), 0 0 0 1.5px #fff;
  cursor: pointer;
  z-index: 1002;
  transition: box-shadow 0.18s, transform 0.13s;
  display: block;
}
.crop-handle:hover {
  box-shadow: 0 2px 8px rgba(0,180,255,0.22), 0 0 0 2.5px #00b4ff;
  transform: scale(1.18);
  background: #e6f7ff;
}

/* تمييز crop-handle-top/bottom/left/right إذا أردت لاحقًا */
.crop-handle-top, .crop-handle-bottom, .crop-handle-left, .crop-handle-right {
  /* يمكن تخصيص لون أو ظل إضافي هنا */
}

/* Crop button styles */
.element-control-btn.crop-btn {
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
    transition: all 0.2s ease;
}

.element-control-btn.crop-btn:hover {
    background: linear-gradient(135deg, #7c3aed, #9333ea);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
}

.element-control-btn.crop-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(139, 92, 246, 0.3);
}

/* Slider styles for crop settings */
.slider {
    -webkit-appearance: none;
    appearance: none;
    background: transparent;
    cursor: pointer;
}

.slider::-webkit-slider-track {
    background: #e5e7eb;
    height: 8px;
    border-radius: 4px;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
    height: 20px;
    width: 20px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
    background: linear-gradient(135deg, #7c3aed, #9333ea);
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.slider::-moz-range-track {
    background: #e5e7eb;
    height: 8px;
    border-radius: 4px;
    border: none;
}

.slider::-moz-range-thumb {
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
    height: 20px;
    width: 20px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: all 0.2s ease;
}

.slider::-moz-range-thumb:hover {
    background: linear-gradient(135deg, #7c3aed, #9333ea);
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Crop Image Modal Styles */
.crop-image-modal {
    background: rgba(0, 0, 0, 0.75) !important;
    backdrop-filter: blur(4px) !important;
    -webkit-backdrop-filter: blur(4px) !important;
}

.crop-image-modal .bg-white {
    background-color: #ffffff !important;
    background-image: none !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
}

.crop-image-modal .bg-gray-50 {
    background-color: #f9fafb !important;
    background-image: none !important;
}

.crop-image-modal .bg-gray-900 {
    background-color: #111827 !important;
    background-image: none !important;
}

.crop-image-modal .bg-purple-100 {
    background-color: #f3e8ff !important;
    background-image: none !important;
}

.crop-image-modal .bg-purple-50 {
    background-color: #faf5ff !important;
    background-image: none !important;
}

.crop-image-modal .bg-white\/20 {
    background-color: rgba(255, 255, 255, 0.2) !important;
    background-image: none !important;
}

.crop-image-modal .bg-white\/30 {
    background-color: rgba(255, 255, 255, 0.3) !important;
    background-image: none !important;
}

/* Force white background for sidebar */
.crop-image-modal .w-80 {
    background-color: #ffffff !important;
    background-image: none !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
}

/* Force proper colors for all elements in crop modal */
.crop-image-modal * {
    background-image: none !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
}

/* Ensure proper text colors */
.crop-image-modal .text-gray-800 {
    color: #1f2937 !important;
}

.crop-image-modal .text-gray-700 {
    color: #374151 !important;
}

.crop-image-modal .text-gray-600 {
    color: #4b5563 !important;
}

.crop-image-modal .text-gray-500 {
    color: #6b7280 !important;
}

.crop-image-modal .text-purple-600 {
    color: #9333ea !important;
}

.crop-image-modal .text-purple-700 {
    color: #7c3aed !important;
}

/* Ensure proper border colors */
.crop-image-modal .border-gray-200 {
    border-color: #e5e7eb !important;
}

.crop-image-modal .border-gray-300 {
    border-color: #d1d5db !important;
}

.crop-image-modal .border-purple-500 {
    border-color: #8b5cf6 !important;
}

/* Ensure proper hover states */
.crop-image-modal .hover\:bg-gray-50:hover {
    background-color: #f9fafb !important;
}

.crop-image-modal .hover\:bg-gray-100:hover {
    background-color: #f3f4f6 !important;
}

.crop-image-modal .hover\:border-gray-300:hover {
    border-color: #d1d5db !important;
}

.crop-image-modal .hover\:text-gray-800:hover {
    color: #1f2937 !important;
}

/* Ensure proper button states */
.crop-image-modal .bg-purple-600 {
    background-color: #9333ea !important;
}

.crop-image-modal .hover\:bg-purple-700:hover {
    background-color: #7c3aed !important;
}

.crop-image-modal .bg-purple-400 {
    background-color: #a78bfa !important;
}

/* Ensure proper slider styling */
.crop-image-modal .slider {
    background: transparent !important;
}

.crop-image-modal .slider::-webkit-slider-track {
    background: #e5e7eb !important;
}

.crop-image-modal .slider::-webkit-slider-thumb {
    background: linear-gradient(135deg, #8b5cf6, #a855f7) !important;
    border: 2px solid white !important;
}

/* Force proper z-index for modal */
.crop-image-modal {
    z-index: 9999 !important;
}

/* Ensure modal content is properly positioned */
.crop-image-modal .max-w-6xl {
    max-width: 72rem !important;
}

.crop-image-modal .max-h-\[90vh\] {
    max-height: 90vh !important;
}

/* Ensure proper overflow handling */
.crop-image-modal .overflow-hidden {
    overflow: hidden !important;
}

.crop-image-modal .overflow-y-auto {
    overflow-y: auto !important;
}

/* Force proper flex layout */
.crop-image-modal .flex {
    display: flex !important;
}

.crop-image-modal .flex-1 {
    flex: 1 1 0% !important;
}

.crop-image-modal .flex-col {
    flex-direction: column !important;
}

/* Ensure proper spacing */
.crop-image-modal .space-y-6 > * + * {
    margin-top: 1.5rem !important;
}

.crop-image-modal .space-y-2 > * + * {
    margin-top: 0.5rem !important;
}

.crop-image-modal .space-y-1 > * + * {
    margin-top: 0.25rem !important;
}

.crop-image-modal .space-x-2 > * + * {
    margin-left: 0.5rem !important;
}

.crop-image-modal .space-x-3 > * + * {
    margin-left: 0.75rem !important;
}

/* Ensure proper grid layout */
.crop-image-modal .grid {
    display: grid !important;
}

.crop-image-modal .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
}

.crop-image-modal .gap-2 {
    gap: 0.5rem !important;
}

/* Ensure proper padding and margins */
.crop-image-modal .p-4 {
    padding: 1rem !important;
}

.crop-image-modal .p-3 {
    padding: 0.75rem !important;
}

.crop-image-modal .p-2 {
    padding: 0.5rem !important;
}

.crop-image-modal .px-4 {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
}

.crop-image-modal .py-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
}

.crop-image-modal .py-3 {
    padding-top: 0.75rem !important;
    padding-bottom: 0.75rem !important;
}

/* Ensure proper border radius */
.crop-image-modal .rounded-lg {
    border-radius: 0.5rem !important;
}

.crop-image-modal .rounded-xl {
    border-radius: 0.75rem !important;
}

/* Ensure proper shadows */
.crop-image-modal .shadow-2xl {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
}

/* Ensure proper transitions */
.crop-image-modal .transition-colors {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke !important;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
    transition-duration: 150ms !important;
}

.crop-image-modal .transition-all {
    transition-property: all !important;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
    transition-duration: 150ms !important;
}

/* Ensure proper text sizes */
.crop-image-modal .text-lg {
    font-size: 1.125rem !important;
    line-height: 1.75rem !important;
}

.crop-image-modal .text-sm {
    font-size: 0.875rem !important;
    line-height: 1.25rem !important;
}

.crop-image-modal .text-xs {
    font-size: 0.75rem !important;
    line-height: 1rem !important;
}

/* Ensure proper font weights */
.crop-image-modal .font-semibold {
    font-weight: 600 !important;
}

.crop-image-modal .font-medium {
    font-weight: 500 !important;
}

/* Ensure proper flex utilities */
.crop-image-modal .flex-1 {
    flex: 1 1 0% !important;
}

.crop-image-modal .items-center {
    align-items: center !important;
}

.crop-image-modal .justify-center {
    justify-content: center !important;
}

.crop-image-modal .justify-between {
    justify-content: space-between !important;
}

/* Ensure proper positioning */
.crop-image-modal .relative {
    position: relative !important;
}

.crop-image-modal .absolute {
    position: absolute !important;
}

.crop-image-modal .inset-0 {
    top: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
}

.crop-image-modal .top-4 {
    top: 1rem !important;
}

.crop-image-modal .left-4 {
    left: 1rem !important;
}

/* Ensure proper width and height */
.crop-image-modal .w-full {
    width: 100% !important;
}

.crop-image-modal .w-80 {
    width: 20rem !important;
}

.crop-image-modal .h-full {
    height: 100% !important;
}

.crop-image-modal .h-2 {
    height: 0.5rem !important;
}

/* Ensure proper cursor states */
.crop-image-modal .cursor-pointer {
    cursor: pointer !important;
}

.crop-image-modal .cursor-not-allowed {
    cursor: not-allowed !important;
}

/* Ensure proper disabled states */
.crop-image-modal .disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed !important;
}

/* Ensure proper animation states */
.crop-image-modal .animate-spin {
    animation: spin 1s linear infinite !important;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Ensure proper border styles */
.crop-image-modal .border-b {
    border-bottom-width: 1px !important;
}

.crop-image-modal .border-l {
    border-left-width: 1px !important;
}

.crop-image-modal .border-t {
    border-top-width: 1px !important;
}

/* Ensure proper gradient backgrounds */
.crop-image-modal .bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops)) !important;
}

.crop-image-modal .from-gray-50 {
    --tw-gradient-from: #f9fafb !important;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(249, 250, 251, 0)) !important;
}

.crop-image-modal .to-white {
    --tw-gradient-to: #ffffff !important;
}

/* Ensure proper responsive design */
@media (max-width: 768px) {
    .crop-image-modal .max-w-6xl {
        max-width: 100% !important;
        margin: 1rem !important;
    }
    
    .crop-image-modal .w-80 {
        width: 100% !important;
    }
    
    .crop-image-modal .flex {
        flex-direction: column !important;
    }
}

/* Text Frame Resize Handles - Circular Style */
.text-frame-handle-left,
.text-frame-handle-right {
    width: 14px !important;
    height: 14px !important;
    min-width: 14px;
    min-height: 14px;
    max-width: 14px;
    max-height: 14px;
    border-radius: 50%;
    background: #fff;
    border: 1.5px solid #bbb;
    box-shadow: 0 1px 4px rgba(0,0,0,0.08);
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1002;
    cursor: ew-resize;
    transition: box-shadow 0.18s, border-color 0.18s;
    display: flex;
    align-items: center;
    justify-content: center;
}
.text-frame-handle-left {
    left: -7px;
}
.text-frame-handle-right {
    right: -7px;
}
.text-frame-handle-left:hover,
.text-frame-handle-right:hover {
    border-color: #2563eb;
    box-shadow: 0 2px 8px rgba(59,130,246,0.18);
}
.text-frame-handle-left:active,
.text-frame-handle-right:active {
    border-color: #1d4ed8;
    box-shadow: 0 0 0 2px #2563eb33;
    background: #f0f6ff;
}

/* Top and Bottom handles - horizontal lines */
.text-frame-handle-top,
.text-frame-handle-bottom {
    position: relative;
}

.text-frame-handle-top::after,
.text-frame-handle-bottom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 2px;
    background-color: rgba(59, 130, 246, 0.8);
    border-radius: 1px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.text-frame-handle-top:hover::after,
.text-frame-handle-bottom:hover::after {
    opacity: 1;
}

/* Active state for text frame handles */
.text-frame-handle.active {
    background-color: rgba(59, 130, 246, 0.4) !important;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 1);
}

.text-frame-handle.active::after {
    opacity: 1;
    background-color: rgba(59, 130, 246, 1);
}

/* Enhanced visual feedback for text frame handles */
.text-frame-handle-left:hover,
.text-frame-handle-right:hover {
    background: linear-gradient(90deg, rgba(59, 130, 246, 0.2), rgba(59, 130, 246, 0.4)) !important;
}

.text-frame-handle-top:hover,
.text-frame-handle-bottom:hover {
    background: linear-gradient(180deg, rgba(59, 130, 246, 0.2), rgba(59, 130, 246, 0.4)) !important;
}

/* Professional cursor indicators */
.text-frame-handle-left,
.text-frame-handle-right {
    cursor: ew-resize !important;
}

.text-frame-handle-top,
.text-frame-handle-bottom {
    cursor: ns-resize !important;
}

/* Smooth transitions for all states */
.text-frame-handle {
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Focus state for accessibility */
.text-frame-handle:focus {
    outline: 2px solid rgba(59, 130, 246, 0.8);
    outline-offset: 1px;
}

/* منع أي تأثير تظليل أو شفافية أو blend mode على النصوص فوق الصور */
.design-space .draggable-element .text,
.design-space .draggable-element .text * {
  mix-blend-mode: normal !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  opacity: 1 !important;
  background: none !important;
  background-color: transparent !important;
  filter: none !important;
}

/* إلغاء تظليل النص عند التحديد أو الضغط داخل منطقة التصميم */
.design-space ::selection {
  background: transparent !important;
  color: inherit !important;
}

.design-space *::-moz-selection {
  background: transparent !important;
  color: inherit !important;
}

/* السماح بتظليل النص فقط عندما يكون النص قابل للتعديل (contentEditable) */
.design-space [contenteditable="true"]::selection {
  background: #b4d5ff !important; /* لون أزرق فاتح افتراضي أو أي لون تفضله */
  color: inherit !important;
}
.design-space [contenteditable="true"] *::selection {
  background: #b4d5ff !important;
  color: inherit !important;
}