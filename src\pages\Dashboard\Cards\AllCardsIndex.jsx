import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useFetchCards } from './path-to-your-hooks-file'; 
import { useDeleteCardMutation, useUpdateCardMutation } from './path-to-your-hooks-file'; 
import { DataTable } from 'primereact/datatable';
import { Tooltip } from 'primereact/tooltip';
import { Column } from 'primereact/column';
import { TfiTrash } from "react-icons/tfi";
import { FaRegEye } from 'react-icons/fa';
import { FiEdit } from "react-icons/fi";


const statusStyles = {
    active: "bg-[#22C55E]",
    inactive: "bg-[#dc2626]",
    pending: "bg-[#D97706]",
};


function CardsDataTable() {
    const { data, isLoading } = useFetchCards(); 
    console.log('📌 API Response cards:', data); 
 
    const { mutate: deleteCard } = useDeleteCardMutation();
    const { mutate: updateCard } = useUpdateCardMutation();
    const navigate = useNavigate();
    const [lazyParams, setLazyParams] = useState({ first: 0, rows: 10 });

    const deleteCardHandler = (id) => {

        deleteCard(id);
    }

    const actionBodyTemplate = (rowData) => {
        return (
            <div className="flex items-center">

                <Tooltip target={`.preview-button-${rowData.id}`} showDelay={100} className="fs-8" />
                <button className={`btn btn-sm btn-icon preview-button-${rowData.id} me-4`}>
                    <FaRegEye size={20} />
                </button>


                <Tooltip target={`.update-button-${rowData.id}`} showDelay={100} className="fs-8" />
                <button
                    className={`btn btn-sm btn-icon update-button-${rowData.id} me-4`}
                    onClick={() => navigate(`/cards/edit/${rowData.id}`)}>
                    <FiEdit size={20} />
                </button>


                <Tooltip target={`.delete-button-${rowData.id}`} showDelay={100} className="fs-8" />
                <button
                    className={`btn btn-sm btn-icon delete-button-${rowData.id}`}
                    onClick={() => deleteCardHandler(rowData.id)}>
                    <TfiTrash size={20} />
                </button>
            </div>
        );
    }

    const statusBodyTemplate = (rowData) => {
        return (
            <span className={`text-white rounded-[6px] font-bold text-sm py-2 px-3 capitalize ${statusStyles[rowData.status || "inactive"]}`}>
                {rowData.status || "inactive"}
            </span>
        );
    }

    return (
        <div className="w-full mt-8">
            <div className='table-responsive text-nowrap'>
                <DataTable
                    lazy
                    filterDisplay="row"
                    responsiveLayout="stack"
                    breakpoint="960px"
                    dataKey="id"
                    paginator
                    className="table w-full border"
                    value={data}
                    first={lazyParams.first}
                    rows={lazyParams.rows}
                    rowsPerPageOptions={[5, 25, 50, 100]}
                    totalRecords={data ? data.length : 0}
                    loading={isLoading}
                    onPage={(e) => setLazyParams(e)}
                    onSort={(e) => setLazyParams({ ...lazyParams, sortField: e.sortField, sortOrder: e.sortOrder })}
                    onFilter={(e) => setLazyParams({ ...lazyParams, filters: e.filters })}
                    paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                    currentPageReportTemplate="Showing {first} to {last} of {totalRecords} cards"
                    scrollable
                    scrollHeight="calc(100vh - 400px)"
                >
                    <Column field="title" header="Title" className='text-center' filter sortable />
                    <Column field="description" header="Description" className='text-center' filter sortable />
                    <Column body={statusBodyTemplate} field="status" header="Status" className='text-center' filter sortable />
                    <Column body={actionBodyTemplate} exportable={false} style={{ minWidth: '8rem' }} />
                </DataTable>
            </div>
        </div>
    );
}

export default CardsDataTable;
