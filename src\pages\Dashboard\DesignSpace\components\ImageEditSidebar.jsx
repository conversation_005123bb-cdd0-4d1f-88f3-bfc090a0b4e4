import { useState, useEffect } from 'react';
import { useDesignSpace } from '@contexts/DesignSpaceContext';
import { Slider } from 'primereact/slider';
import { Accordion, AccordionTab } from 'primereact/accordion';
import { Button } from 'primereact/button';

// Icons
import { FiRotateCw, FiRotateCcw } from 'react-icons/fi';
import {
    MdOutlineBrightness6,
    MdOutlineContrast,
    MdOutlineBlurOn,
    MdOutlineOpacity,
    MdOutlineColorLens,
    MdOutlinePhotoFilter,
    MdOutlineTune,
    MdOutlineFlip
} from 'react-icons/md';

const ImageEditSidebar = () => {
    const { selectedIds, elements, updateElement } = useDesignSpace();

    const [selectedImage, setSelectedImage] = useState(null);
    const [activeIndex, setActiveIndex] = useState([0]); // Default open first tab
    const [editSettings, setEditSettings] = useState({
        brightness: 100,
        contrast: 100,
        blur: 0,
        opacity: 100,
        rotation: 0,
        flipX: false,
        flipY: false,
        saturation: 100,
        hue: 0,
        sepia: 0,
        grayscale: 0
    });

    // Find the selected image
    useEffect(() => {
        if (selectedIds.length === 1) {
            const element = elements.find(el => el.id === selectedIds[0]);
            if (element && element.type === 'img') {
                setSelectedImage(element);

                // Initialize edit settings from existing filters if any
                if (element.filters) {
                    setEditSettings({
                        ...editSettings,
                        ...element.filters
                    });
                }
            } else {
                setSelectedImage(null);
            }
        } else {
            setSelectedImage(null);
        }
    }, [selectedIds, elements]);

    // Apply filters to the image
    const applyFilters = () => {
        if (!selectedImage) return;

        // Apply filters immediately without showing processing indicator
        const filters = {
            brightness: `brightness(${editSettings.brightness}%)`,
            contrast: `contrast(${editSettings.contrast}%)`,
            blur: `blur(${editSettings.blur}px)`,
            opacity: `opacity(${editSettings.opacity}%)`,
            saturation: `saturate(${editSettings.saturation}%)`,
            hue: `hue-rotate(${editSettings.hue}deg)`,
            sepia: `sepia(${editSettings.sepia}%)`,
            grayscale: `grayscale(${editSettings.grayscale}%)`
        };

        const filterString = Object.values(filters).join(' ');
        const transform = `rotate(${editSettings.rotation}deg) scaleX(${editSettings.flipX ? -1 : 1}) scaleY(${editSettings.flipY ? -1 : 1})`;

        // Update element with filters but apply styles only to the image content, not the toolbar
        updateElement(selectedImage.id, {
            filters: editSettings,
            style: {
                filter: filterString,
                transform: transform
            }
        });

        // Apply styles directly to the image element to avoid affecting the toolbar
        const imageElement = document.querySelector(`[data-element-id="${selectedImage.id}"] img`);
        if (imageElement) {
            imageElement.style.filter = filterString;
            imageElement.style.transform = transform;
        }
    };

    // Apply filters when settings change with debounce
    useEffect(() => {
        const debounceTimer = setTimeout(() => {
            applyFilters();
        }, 100); // Small debounce to prevent too many updates

        return () => clearTimeout(debounceTimer);
    }, [editSettings]);

    // Handle slider changes
    const handleSliderChange = (property, value) => {
        setEditSettings(prev => ({
            ...prev,
            [property]: value
        }));
    };

    // Reset all filters
    const resetFilters = () => {
        setEditSettings({
            brightness: 100,
            contrast: 100,
            blur: 0,
            opacity: 100,
            rotation: 0,
            flipX: false,
            flipY: false,
            saturation: 100,
            hue: 0,
            sepia: 0,
            grayscale: 0
        });
    };

    // Apply preset filters
    const applyPreset = (preset) => {
        switch(preset) {
            case 'vintage':
                setEditSettings(prev => ({
                    ...prev,
                    sepia: 50,
                    contrast: 120,
                    brightness: 90,
                    saturation: 85
                }));
                break;
            case 'blackAndWhite':
                setEditSettings(prev => ({
                    ...prev,
                    grayscale: 100,
                    contrast: 120,
                    brightness: 110,
                    saturation: 0
                }));
                break;
            case 'warm':
                setEditSettings(prev => ({
                    ...prev,
                    hue: 30,
                    saturation: 120,
                    brightness: 105,
                    sepia: 20
                }));
                break;
            case 'cool':
                setEditSettings(prev => ({
                    ...prev,
                    hue: 210,
                    saturation: 90,
                    brightness: 105,
                    contrast: 110
                }));
                break;
            case 'sharp':
                setEditSettings(prev => ({
                    ...prev,
                    contrast: 130,
                    brightness: 110,
                    saturation: 110
                }));
                break;
            case 'dramatic':
                setEditSettings(prev => ({
                    ...prev,
                    contrast: 150,
                    brightness: 90,
                    saturation: 120,
                    hue: 0
                }));
                break;
            case 'faded':
                setEditSettings(prev => ({
                    ...prev,
                    contrast: 90,
                    brightness: 110,
                    saturation: 70,
                    opacity: 90
                }));
                break;
            case 'retro':
                setEditSettings(prev => ({
                    ...prev,
                    sepia: 30,
                    hue: 20,
                    saturation: 90,
                    contrast: 110
                }));
                break;
            case 'cinema':
                setEditSettings(prev => ({
                    ...prev,
                    contrast: 120,
                    saturation: 85,
                    brightness: 95,
                    sepia: 10
                }));
                break;
            default:
                break;
        }
    };

    // Rotate image
    const rotateImage = (direction) => {
        const newRotation = editSettings.rotation + (direction === 'right' ? 90 : -90);
        setEditSettings(prev => ({
            ...prev,
            rotation: newRotation
        }));
    };

    // Flip image
    const flipImage = (axis) => {
        if (axis === 'horizontal') {
            setEditSettings(prev => ({
                ...prev,
                flipX: !prev.flipX
            }));
        } else {
            setEditSettings(prev => ({
                ...prev,
                flipY: !prev.flipY
            }));
        }
    };

    if (!selectedImage) {
        return (
            <div className="image-edit-sidebar bg-white h-full flex flex-col">
                <div className="flex justify-between items-center p-4 border-b border-gray-200 bg-white z-10 flex-shrink-0">
                    <h3 className="text-lg font-medium">Image Editor</h3>
                </div>
                <div className="flex-1 flex items-center justify-center">
                    <div className="text-center text-gray-500">
                        <MdOutlinePhotoFilter size={48} className="mx-auto mb-4 text-gray-300" />
                        <p>Select an image to edit</p>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="image-edit-sidebar bg-white h-full flex flex-col">
            <div className="flex justify-between items-center p-4 border-b border-gray-200 bg-white z-10 flex-shrink-0">
                <h3 className="text-lg font-medium">Image Editor</h3>
                <div className="flex items-center">
                    <Button
                        icon="pi pi-undo"
                        className="p-button-rounded p-button-text p-button-sm mr-2"
                        onClick={resetFilters}
                        tooltip="Reset All"
                        tooltipOptions={{ position: 'bottom' }}
                    />
                </div>
            </div>

            <div className="flex-1 overflow-y-auto p-4">
                <Accordion multiple activeIndex={activeIndex} onTabChange={(e) => setActiveIndex(e.index)}>
                <AccordionTab header="Transform">
                    <div className="mb-4">
                        <div className="flex space-x-2 mb-3">
                            <button
                                className="p-2 rounded hover:bg-gray-100 flex-1 flex items-center justify-center"
                                onClick={() => rotateImage('left')}
                                title="Rotate Left"
                            >
                                <FiRotateCcw className="mr-2" />
                                <span>Rotate Left</span>
                            </button>
                            <button
                                className="p-2 rounded hover:bg-gray-100 flex-1 flex items-center justify-center"
                                onClick={() => rotateImage('right')}
                                title="Rotate Right"
                            >
                                <FiRotateCw className="mr-2" />
                                <span>Rotate Right</span>
                            </button>
                        </div>
                        <div className="flex space-x-2">
                            <button
                                className="p-2 rounded hover:bg-gray-100 flex-1 flex items-center justify-center"
                                onClick={() => flipImage('horizontal')}
                                title="Flip Horizontal"
                            >
                                <MdOutlineFlip className="mr-2 rotate-90" />
                                <span>Flip H</span>
                            </button>
                            <button
                                className="p-2 rounded hover:bg-gray-100 flex-1 flex items-center justify-center"
                                onClick={() => flipImage('vertical')}
                                title="Flip Vertical"
                            >
                                <MdOutlineFlip className="mr-2" />
                                <span>Flip V</span>
                            </button>
                        </div>
                    </div>
                </AccordionTab>

                <AccordionTab header="Adjustments">
                    <div className="space-y-4">
                        <div>
                            <div className="flex justify-between items-center mb-1">
                                <div className="flex items-center">
                                    <MdOutlineBrightness6 className="mr-2 text-blue-600" />
                                    <label className="text-sm">Brightness</label>
                                </div>
                                <div className="bg-gray-100 px-2 py-1 rounded text-xs font-medium">
                                    {editSettings.brightness}%
                                </div>
                            </div>
                            <Slider
                                value={editSettings.brightness}
                                onChange={(e) => handleSliderChange('brightness', e.value)}
                                min={0}
                                max={200}
                                className="mt-2"
                            />
                            <div className="flex justify-between text-xs text-gray-500 mt-1">
                                <span>0%</span>
                                <span>100%</span>
                                <span>200%</span>
                            </div>
                        </div>

                        <div>
                            <div className="flex justify-between items-center mb-1">
                                <div className="flex items-center">
                                    <MdOutlineContrast className="mr-2 text-purple-600" />
                                    <label className="text-sm">Contrast</label>
                                </div>
                                <div className="bg-gray-100 px-2 py-1 rounded text-xs font-medium">
                                    {editSettings.contrast}%
                                </div>
                            </div>
                            <Slider
                                value={editSettings.contrast}
                                onChange={(e) => handleSliderChange('contrast', e.value)}
                                min={0}
                                max={200}
                                className="mt-2"
                            />
                            <div className="flex justify-between text-xs text-gray-500 mt-1">
                                <span>0%</span>
                                <span>100%</span>
                                <span>200%</span>
                            </div>
                        </div>

                        <div>
                            <div className="flex justify-between items-center mb-1">
                                <div className="flex items-center">
                                    <MdOutlineBlurOn className="mr-2 text-indigo-600" />
                                    <label className="text-sm">Blur</label>
                                </div>
                                <div className="bg-gray-100 px-2 py-1 rounded text-xs font-medium">
                                    {editSettings.blur}px
                                </div>
                            </div>
                            <Slider
                                value={editSettings.blur}
                                onChange={(e) => handleSliderChange('blur', e.value)}
                                min={0}
                                max={10}
                                className="mt-2"
                            />
                            <div className="flex justify-between text-xs text-gray-500 mt-1">
                                <span>0px</span>
                                <span>5px</span>
                                <span>10px</span>
                            </div>
                        </div>

                        <div>
                            <div className="flex justify-between items-center mb-1">
                                <div className="flex items-center">
                                    <MdOutlineOpacity className="mr-2 text-gray-600" />
                                    <label className="text-sm">Opacity</label>
                                </div>
                                <div className="bg-gray-100 px-2 py-1 rounded text-xs font-medium">
                                    {editSettings.opacity}%
                                </div>
                            </div>
                            <Slider
                                value={editSettings.opacity}
                                onChange={(e) => handleSliderChange('opacity', e.value)}
                                min={0}
                                max={100}
                                className="mt-2"
                            />
                            <div className="flex justify-between text-xs text-gray-500 mt-1">
                                <span>0%</span>
                                <span>50%</span>
                                <span>100%</span>
                            </div>
                        </div>

                        <div>
                            <div className="flex justify-between items-center mb-1">
                                <div className="flex items-center">
                                    <MdOutlineColorLens className="mr-2 text-pink-600" />
                                    <label className="text-sm">Saturation</label>
                                </div>
                                <div className="bg-gray-100 px-2 py-1 rounded text-xs font-medium">
                                    {editSettings.saturation}%
                                </div>
                            </div>
                            <Slider
                                value={editSettings.saturation}
                                onChange={(e) => handleSliderChange('saturation', e.value)}
                                min={0}
                                max={200}
                                className="mt-2"
                            />
                            <div className="flex justify-between text-xs text-gray-500 mt-1">
                                <span>0%</span>
                                <span>100%</span>
                                <span>200%</span>
                            </div>
                        </div>

                        <div>
                            <div className="flex justify-between items-center mb-1">
                                <div className="flex items-center">
                                    <MdOutlineTune className="mr-2 text-green-600" />
                                    <label className="text-sm">Hue Rotate</label>
                                </div>
                                <div className="bg-gray-100 px-2 py-1 rounded text-xs font-medium">
                                    {editSettings.hue}°
                                </div>
                            </div>
                            <Slider
                                value={editSettings.hue}
                                onChange={(e) => handleSliderChange('hue', e.value)}
                                min={0}
                                max={360}
                                className="mt-2"
                            />
                            <div className="flex justify-between text-xs text-gray-500 mt-1">
                                <span>0°</span>
                                <span>180°</span>
                                <span>360°</span>
                            </div>
                        </div>
                    </div>
                </AccordionTab>

                <AccordionTab header="Filters">
                    <div className="grid grid-cols-3 gap-2">
                        <div
                            className="p-2 text-center rounded cursor-pointer overflow-hidden flex flex-col items-center"
                            onClick={() => applyPreset('vintage')}
                        >
                            <div className="w-full h-12 mb-1 rounded bg-amber-100 border border-gray-200 flex items-center justify-center text-amber-800 font-bold">V</div>
                            <span className="text-xs">Vintage</span>
                        </div>
                        <div
                            className="p-2 text-center rounded cursor-pointer overflow-hidden flex flex-col items-center"
                            onClick={() => applyPreset('blackAndWhite')}
                        >
                            <div className="w-full h-12 mb-1 rounded bg-gray-800 border border-gray-200 flex items-center justify-center text-white font-bold">B&W</div>
                            <span className="text-xs">B&W</span>
                        </div>
                        <div
                            className="p-2 text-center rounded cursor-pointer overflow-hidden flex flex-col items-center"
                            onClick={() => applyPreset('warm')}
                        >
                            <div className="w-full h-12 mb-1 rounded bg-orange-100 border border-gray-200 flex items-center justify-center text-orange-600 font-bold">W</div>
                            <span className="text-xs">Warm</span>
                        </div>
                        <div
                            className="p-2 text-center rounded cursor-pointer overflow-hidden flex flex-col items-center"
                            onClick={() => applyPreset('cool')}
                        >
                            <div className="w-full h-12 mb-1 rounded bg-blue-100 border border-gray-200 flex items-center justify-center text-blue-600 font-bold">C</div>
                            <span className="text-xs">Cool</span>
                        </div>
                        <div
                            className="p-2 text-center rounded cursor-pointer overflow-hidden flex flex-col items-center"
                            onClick={() => applyPreset('sharp')}
                        >
                            <div className="w-full h-12 mb-1 rounded bg-gray-100 border border-gray-200 flex items-center justify-center text-gray-800 font-bold">S</div>
                            <span className="text-xs">Sharp</span>
                        </div>
                        <div
                            className="p-2 text-center rounded cursor-pointer overflow-hidden flex flex-col items-center"
                            onClick={() => applyPreset('dramatic')}
                        >
                            <div className="w-full h-12 mb-1 rounded bg-purple-100 border border-gray-200 flex items-center justify-center text-purple-800 font-bold">D</div>
                            <span className="text-xs">Dramatic</span>
                        </div>
                        <div
                            className="p-2 text-center rounded cursor-pointer overflow-hidden flex flex-col items-center"
                            onClick={() => applyPreset('faded')}
                        >
                            <div className="w-full h-12 mb-1 rounded bg-gray-200 border border-gray-200 flex items-center justify-center text-gray-500 font-bold">F</div>
                            <span className="text-xs">Faded</span>
                        </div>
                        <div
                            className="p-2 text-center rounded cursor-pointer overflow-hidden flex flex-col items-center"
                            onClick={() => applyPreset('retro')}
                        >
                            <div className="w-full h-12 mb-1 rounded bg-yellow-100 border border-gray-200 flex items-center justify-center text-yellow-800 font-bold">R</div>
                            <span className="text-xs">Retro</span>
                        </div>
                        <div
                            className="p-2 text-center rounded cursor-pointer overflow-hidden flex flex-col items-center"
                            onClick={() => applyPreset('cinema')}
                        >
                            <div className="w-full h-12 mb-1 rounded bg-indigo-100 border border-gray-200 flex items-center justify-center text-indigo-800 font-bold">C</div>
                            <span className="text-xs">Cinema</span>
                        </div>
                        <div
                            className="p-2 text-center rounded cursor-pointer overflow-hidden flex flex-col items-center"
                            onClick={resetFilters}
                        >
                            <div className="w-full h-12 mb-1 rounded bg-white border border-gray-200 flex items-center justify-center text-gray-800 font-bold">N</div>
                            <span className="text-xs">Normal</span>
                        </div>
                    </div>
                </AccordionTab>
            </Accordion>
            </div>
        </div>
    );
};

export default ImageEditSidebar;
