import React, { useState } from 'react';
import { Dialog } from 'primereact/dialog';
import { But<PERSON> } from 'primereact/button';
import { Accordion, AccordionTab } from 'primereact/accordion';
import { FiHelpCircle } from 'react-icons/fi';

const HelpGuide = () => {
    const [visible, setVisible] = useState(false);

    return (
        <>
            <button
                className="p-2 rounded-full bg-purple-600 text-white fixed bottom-4 right-4 shadow-lg hover:bg-purple-700 z-50 help-guide-button hidden"
                onClick={() => setVisible(true)}
                title="Help"
            >
                <FiHelpCircle size={24} />
            </button>

            <Dialog
                header="Design Space Help Guide"
                visible={visible}
                style={{ width: '90%', maxWidth: '800px' }}
                onHide={() => setVisible(false)}
                draggable={false}
                resizable={false}
            >
                <div className="help-guide p-3">
                    <h2 className="text-xl font-bold mb-4">Welcome to the Design Space!</h2>
                    <p className="mb-4">This guide will help you understand how to use all the features available in our design tool.</p>

                    <Accordion multiple>
                        <AccordionTab header="Getting Started">
                            <div className="p-2">
                                <h3 className="text-lg font-semibold mb-2">Basic Navigation</h3>
                                <ul className="list-disc pl-6 space-y-2">
                                    <li>The left sidebar contains all the design elements and tools you can use.</li>
                                    <li>The top toolbar provides access to file operations, editing tools, and view controls.</li>
                                    <li>The main canvas in the center is where you'll create your design.</li>
                                </ul>

                                <h3 className="text-lg font-semibold mt-4 mb-2">Starting a New Design</h3>
                                <ol className="list-decimal pl-6 space-y-2">
                                    <li>Click on the "Templates" tab in the left sidebar.</li>
                                    <li>Browse through available templates or search for a specific type.</li>
                                    <li>Click on a template to apply it to your canvas.</li>
                                    <li>Alternatively, you can start from scratch by adding elements one by one.</li>
                                </ol>
                            </div>
                        </AccordionTab>

                        <AccordionTab header="Working with Templates">
                            <div className="p-2">
                                <h3 className="text-lg font-semibold mb-2">Using Templates</h3>
                                <ul className="list-disc pl-6 space-y-2">
                                    <li>Templates are pre-designed layouts that you can customize.</li>
                                    <li>Browse templates by category: Social Media, Presentations, or Print Products.</li>
                                    <li>Click on a template to apply it to your canvas.</li>
                                    <li>All elements in the template can be edited, moved, or deleted.</li>
                                </ul>
                            </div>
                        </AccordionTab>

                        <AccordionTab header="Adding and Editing Elements">
                            <div className="p-2">
                                <h3 className="text-lg font-semibold mb-2">Adding Elements</h3>
                                <ul className="list-disc pl-6 space-y-2">
                                    <li>Use the sidebar tabs to add different types of elements:</li>
                                    <li><strong>Text:</strong> Add headings, paragraphs, or labels.</li>
                                    <li><strong>Images:</strong> Upload your own images or use from the library.</li>
                                    <li><strong>Shapes:</strong> Add geometric shapes, lines, or icons.</li>
                                    <li><strong>Background:</strong> Change the canvas background color or image.</li>
                                </ul>

                                <h3 className="text-lg font-semibold mt-4 mb-2">Editing Elements</h3>
                                <ul className="list-disc pl-6 space-y-2">
                                    <li>Click on any element to select it.</li>
                                    <li>Use the handles around the element to resize it.</li>
                                    <li>Drag the element to reposition it on the canvas.</li>
                                    <li>When an element is selected, the sidebar will show editing options specific to that element type.</li>
                                </ul>
                            </div>
                        </AccordionTab>

                        <AccordionTab header="Advanced Image Editing">
                            <div className="p-2">
                                <h3 className="text-lg font-semibold mb-2">Image Editing Features</h3>
                                <ul className="list-disc pl-6 space-y-2">
                                    <li>Select an image and click "Advanced Edit" to open the image editor.</li>
                                    <li>Adjust brightness, contrast, saturation, and other properties.</li>
                                    <li>Apply filters like Vintage, Black & White, Warm, or Cool.</li>
                                    <li>Rotate or flip the image as needed.</li>
                                    <li>Adjust opacity to create overlay effects.</li>
                                </ul>
                            </div>
                        </AccordionTab>

                        <AccordionTab header="Using the Toolbar">
                            <div className="p-2">
                                <h3 className="text-lg font-semibold mb-2">Toolbar Functions</h3>
                                <ul className="list-disc pl-6 space-y-2">
                                    <li><strong>Undo/Redo:</strong> Reverse or reapply your recent actions.</li>
                                    <li><strong>Zoom:</strong> Zoom in or out of the canvas for detailed work.</li>
                                    <li><strong>Lock/Unlock:</strong> Lock elements to prevent accidental changes.</li>
                                    <li><strong>Group/Ungroup:</strong> Combine multiple elements to move or edit them together.</li>
                                    <li><strong>Layers:</strong> Bring elements forward or send them backward in the stacking order.</li>
                                </ul>
                            </div>
                        </AccordionTab>

                        <AccordionTab header="Saving and Exporting">
                            <div className="p-2">
                                <h3 className="text-lg font-semibold mb-2">Saving Your Work</h3>
                                <ul className="list-disc pl-6 space-y-2">
                                    <li>Click the "Save Design" button at the top of the page to save your work.</li>
                                    <li>Your design will be saved to your account and can be accessed later.</li>
                                </ul>

                                <h3 className="text-lg font-semibold mt-4 mb-2">Exporting Your Design</h3>
                                <ul className="list-disc pl-6 space-y-2">
                                    <li>Use the File menu to export your design in different formats.</li>
                                    <li>Choose the appropriate format based on your needs:</li>
                                    <li>- PNG: Best for web graphics with transparency</li>
                                    <li>- JPG: Best for photographs and complex images</li>
                                    <li>- PDF: Best for print materials</li>
                                </ul>
                            </div>
                        </AccordionTab>
                    </Accordion>

                    <div className="mt-6 text-center">
                        <Button label="Close Guide" icon="pi pi-times" onClick={() => setVisible(false)} className="p-button-secondary" />
                    </div>
                </div>
            </Dialog>
        </>
    );
};

export default HelpGuide;
