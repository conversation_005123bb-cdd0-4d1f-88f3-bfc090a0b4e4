import { useEffect, useState } from "react";
import Container from "@components/Container";
import axiosInstance from "../../../config/Axios";
import { useGlobalContext } from "@contexts/GlobalContext";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { Dropdown } from "primereact/dropdown";
import { FaSearch, FaEye, FaDownload, FaTimes } from "react-icons/fa";
import { Dialog } from "primereact/dialog";
import { Button } from "primereact/button";

export default function BillingHistory() {
  const [purchaseHistory, setPurchaseHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    package_name: '',
    status: '',
  });

  // Mobile responsiveness states
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  // Invoice modal states
  const [invoiceModalVisible, setInvoiceModalVisible] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState(null);

  const userId = localStorage.getItem("user_id");
  const { showToast } = useGlobalContext();

  const statusOptions = [
    { label: 'All Statuses', value: '' },
    { label: 'Active', value: 'active' },
    { label: 'Inactive', value: 'inactive' },
  ];

  useEffect(() => {
    const fetchPurchaseHistory = async () => {
      try {
        setLoading(true);

        if (!userId) {
          showToast("error", "Error", "User ID not found");
          return;
        }

        const { data } = await axiosInstance.get(
          `/packages/${userId}/packages_history`,
          {
            params: {
              package_name: filters.package_name,
              status: filters.status,
            }
          }
        );

        setPurchaseHistory(data.history_packages || []);
      } catch (error) {
        console.error("Error fetching purchase history:", error);
        showToast("error", "Error", "Failed to fetch purchase history");
      } finally {
        setLoading(false);
      }
    };

    fetchPurchaseHistory();
  }, [userId, filters, showToast]);

  // Mobile detection useEffect
  useEffect(() => {
    const handleResize = () => {
      const mobileView = window.innerWidth < 768;
      setIsMobile(mobileView);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const statusBodyTemplate = (rowData) => {
    const statusClass = rowData.status === "active"
      ? "bg-[#22C55E]"
      : "bg-[#DC2626]";

    return (
      <span
        className={`text-white rounded-[6px] font-bold text-sm py-2 px-3 capitalize ${statusClass} min-w-[100px] inline-block text-center`}
      >
        {rowData.status}
      </span>
    );
  };

  const priceBodyTemplate = (rowData) => {
    return (
      <span
        className="bg-transparent text-[#00CC32] border-2 border-[#00CC32] rounded-[6px] font-bold text-sm py-2 px-3 inline-block text-center min-w-[100px]"
      >
        ${rowData.total_price}
      </span>
    );
  };

  const actionBodyTemplate = (rowData) => {
    return (
      <div className="flex items-center justify-center">
        <button
          onClick={() => handleViewInvoice(rowData)}
          className="p-2 text-blue-500 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-all duration-200 flex items-center justify-center"
          title="View Invoice"
        >
          <FaEye className="w-5 h-5" />
        </button>
      </div>
    );
  };

  const handleViewInvoice = (invoiceData) => {
    setSelectedInvoice(invoiceData);
    setInvoiceModalVisible(true);
  };

  const handleDownloadAndPrint = () => {
    if (!selectedInvoice) return;
    
    // Create a new window for PDF generation and printing
    const printWindow = window.open('', '_blank');
    const invoiceContent = generateInvoicePDF(selectedInvoice);
    
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Invoice - ${selectedInvoice.package_name}</title>
          <meta charset="utf-8">
          <style>
            @media print {
              * { -webkit-print-color-adjust: exact !important; color-adjust: exact !important; }
              @page {
                margin: 0;
                size: A4;
              }
              body {
                margin: 0;
                padding: 0;
                background: white;
              }
            }
          </style>
        </head>
        <body>
          ${invoiceContent}
        </body>
      </html>
    `);
    printWindow.document.close();
    
    // Wait for content to load then trigger print dialog
    printWindow.onload = function() {
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 500);
    };
    
    showToast("success", "Invoice Ready", "Print dialog opened. Choose 'Save as PDF' to download or 'Print' to print.");
  };

  const generateInvoicePDF = (invoice) => {
    const currentDate = new Date().toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
    const invoiceNumber = `INV-${Date.now()}`;
    
    return `
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: white;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }
        .invoice-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
            transform: scale(0.9);
            transform-origin: top center;
            min-height: 100vh;
        }
        .invoice-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        .company-logo {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .company-logo svg {
            width: 40px;
            height: 40px;
            filter: brightness(0) invert(1);
        }
        .header-text {
            text-align: left;
        }
        .invoice-header h1 {
            font-size: 2.5em;
            margin-bottom: 5px;
            font-weight: 300;
            letter-spacing: 2px;
        }
        .invoice-header p {
            font-size: 1.1em;
            opacity: 0.9;
            font-weight: 300;
        }
        .header-right {
            text-align: right;
        }
        .invoice-number {
            font-size: 1.2em;
            font-weight: 600;
            margin-bottom: 5px;
            opacity: 0.9;
        }
        .invoice-date {
            font-size: 0.9em;
            opacity: 0.8;
        }
        .invoice-details {
            padding: 30px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            border-bottom: 2px solid #f1f3f4;
        }
        .invoice-info, .customer-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
        }
        .invoice-info h3, .customer-info h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
        }
        .info-label {
            font-weight: 600;
            color: #555;
        }
        .info-value {
            color: #333;
        }
        .invoice-items {
            padding: 30px;
        }
        .invoice-items h3 {
            font-size: 1.2em;
            margin-bottom: 15px;
            color: #667eea;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .items-table th {
            background: #667eea;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }
        .items-table td {
            padding: 15px;
            border-bottom: 1px solid #eee;
        }
        .items-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        .total-section {
            padding: 30px;
            background: #f8f9fa;
            text-align: right;
        }
        .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        .total-amount {
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
            border-top: 2px solid #667eea;
            padding-top: 10px;
            margin-top: 10px;
        }
        .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.9em;
        }
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        .footer {
            padding: 20px 30px;
            background: #333;
            color: white;
            text-align: center;
            font-size: 0.9em;
        }
        
        @media print {
            * { 
                -webkit-print-color-adjust: exact !important; 
                color-adjust: exact !important; 
            }
            @page {
                margin: 0 !important;
                size: A4 !important;
                padding: 0 !important;
            }
            html, body { 
                background: white !important; 
                margin: 0 !important;
                padding: 0 !important;
                height: 100% !important;
                min-height: 100% !important;
                overflow: hidden !important;
            }
            .invoice-container { 
                box-shadow: none !important; 
                margin: 0 !important; 
                transform: none !important;
                max-width: 100% !important;
                border-radius: 0 !important;
                background: white !important;
                height: 100% !important;
                min-height: 100% !important;
                display: flex !important;
                flex-direction: column !important;
            }
            .invoice-header { 
                padding: 30px !important; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                color: white !important;
                flex-shrink: 0 !important;
            }
            .invoice-header h1 { 
                font-size: 2.5em !important; 
                color: white !important;
            }
            .invoice-header p { 
                color: white !important; 
            }
            .invoice-details { 
                padding: 30px !important; 
                gap: 30px !important; 
                flex-shrink: 0 !important;
            }
            .invoice-info, .customer-info { 
                padding: 20px !important; 
                background: #f8f9fa !important;
            }
            .invoice-info h3, .customer-info h3 { 
                color: #667eea !important; 
            }
            .invoice-items { 
                padding: 30px !important; 
                flex: 1 !important;
            }
            .invoice-items h3 { 
                color: #667eea !important; 
            }
            .items-table th { 
                background: #667eea !important; 
                color: white !important; 
            }
            .total-section { 
                padding: 30px !important; 
                background: #f8f9fa !important;
                flex-shrink: 0 !important;
            }
            .total-amount { 
                color: #667eea !important; 
                border-top: 2px solid #667eea !important;
            }
            .footer { 
                padding: 20px 30px !important; 
                background: #333 !important;
                color: white !important;
                flex-shrink: 0 !important;
            }
            .status-badge.status-active { 
                background: #d4edda !important; 
                color: #155724 !important; 
            }
            .status-badge.status-inactive { 
                background: #f8d7da !important; 
                color: #721c24 !important; 
            }
        }
    </style>
    
    <div class="invoice-container">
        <div class="invoice-header">
            <div class="header-left">
                <div class="company-logo">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                        <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                        <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                    </svg>
                </div>
                <div class="header-text">
                    <h1>INVOICE</h1>
                    <p>Package Purchase</p>
                </div>
            </div>
            <div class="header-right">
                <div class="invoice-number">${invoiceNumber}</div>
                <div class="invoice-date">${currentDate}</div>
            </div>
        </div>
        
        <div class="invoice-details">
            <div class="invoice-info">
                <h3>Invoice Information</h3>
                <div class="info-row">
                    <span class="info-label">Invoice Number:</span>
                    <span class="info-value">${invoiceNumber}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Issue Date:</span>
                    <span class="info-value">${currentDate}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Purchase Date:</span>
                    <span class="info-value">${invoice.purchased_at}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Status:</span>
                    <span class="info-value">
                        <span class="status-badge status-${invoice.status}">${invoice.status}</span>
                    </span>
                </div>
            </div>
            
            <div class="customer-info">
                <h3>Package Details</h3>
                <div class="info-row">
                    <span class="info-label">Package Name:</span>
                    <span class="info-value">${invoice.package_name}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Card Limit:</span>
                    <span class="info-value">${invoice.card_limit} cards</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Expiry Date:</span>
                    <span class="info-value">${invoice.expiry_date}</span>
                </div>
            </div>
        </div>
        
        <div class="invoice-items">
            <h3>Purchase Details</h3>
            <table class="items-table">
                <thead>
                    <tr>
                        <th>Description</th>
                        <th>Details</th>
                        <th>Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>${invoice.package_name} Package</td>
                        <td>Card Limit: ${invoice.card_limit} | Valid until: ${invoice.expiry_date}</td>
                        <td>$${invoice.total_price}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="total-section">
            <div class="total-row">
                <span>Subtotal:</span>
                <span>$${invoice.total_price}</span>
            </div>
            <div class="total-row">
                <span>Tax (0%):</span>
                <span>$0.00</span>
            </div>
            <div class="total-row total-amount">
                <span>Total Amount:</span>
                <span>$${invoice.total_price}</span>
            </div>
        </div>
        
        <div class="footer">
            <p>Thank you for your purchase! This is a computer-generated invoice.</p>
        </div>
    </div>
    `;
  };

  const onFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Mobile list view component
  const MobileListView = () => {
    if (loading) {
      return (
        <div className="space-y-2">
          {[...Array(5)].map((_, index) => (
            <div key={index} className="bg-white border rounded-lg p-4 shadow-sm animate-pulse">
              <div className="flex items-center justify-between">
                <div className="flex items-center flex-1">
                  <div className="w-12 h-12 bg-gray-300 rounded-lg mr-3"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-300 rounded w-1/2 mb-1"></div>
                    <div className="h-3 bg-gray-300 rounded w-1/3"></div>
                  </div>
                </div>
                <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
              </div>
            </div>
          ))}
        </div>
      );
    }

    if (purchaseHistory.length === 0) {
      return (
        <div className="text-center py-8">
          <p className="text-gray-500">No purchase history found</p>
        </div>
      );
    }

    return (
      <div className="space-y-3">
        {purchaseHistory.map((item, index) => (
          <div key={item.id || index} className="bg-white border rounded-lg p-4 shadow-sm">
            <div className="flex items-center justify-between">
              <div className="flex items-center flex-1">
                {/* Package Icon */}
                <div className="w-15 h-15 bg-green-100 rounded-lg mr-3 flex items-center justify-center">
                  <svg className="w-6 h-6 text-green-500" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4a2 2 0 0 0 1-1.73z" stroke="currentColor" strokeWidth="2" strokeLinejoin="round"/>
                    <path d="M3.27 6.96L12 12.01l8.73-5.05M12 22V12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>

                {/* Package Info */}
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium text-gray-900 truncate">{item.package_name}</h3>
                  <p className="text-sm text-gray-500">
                    <span
                      className="bg-transparent text-[#00CC32] border border-[#00CC32] rounded px-2 py-1 text-xs font-bold"
                    >
                      ${item.total_price}
                    </span>
                  </p>
                  <div className="flex items-center mt-1 space-x-2">
                    <span className="text-xs text-gray-400">Purchased: {item.purchased_at}</span>
                  </div>
                  <div className="flex items-center mt-1 space-x-2">
                    <span className="text-xs text-gray-400">Expires: {item.expiry_date}</span>
                    <span className="text-xs text-gray-400">• Cards: {item.card_limit}</span>
                  </div>
                  <div className="mt-2">
                    <span
                      className={`text-xs px-2 py-1 rounded-full font-bold ${
                        item.status === 'active' ? 'bg-green-100 text-green-800' :
                        'bg-red-100 text-red-800'
                      }`}
                    >
                      {item.status}
                    </span>
                  </div>
                </div>
              </div>
              
              {/* Mobile Action Button */}
              <button
                onClick={() => handleViewInvoice(item)}
                className="p-2 text-blue-500 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-all duration-200"
                title="View Invoice"
              >
                <FaEye className="w-5 h-5" />
              </button>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <Container className="flex flex-col h-full">
      <div className="w-full mb-5">
        <h1 className="text-2xl font-bold">Billing & Purchase History</h1>
      </div>

      {/* Search and Filter Section - Mobile responsive */}
      <div className={`w-full mb-4 ${isMobile ? 'space-y-3' : 'flex justify-between items-center'}`}>
        {/* Search Input */}
        <div className={`relative ${isMobile ? 'w-full' : 'flex-grow max-w-[700px]'}`}>
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaSearch className="h-4 w-4 text-gray-400" aria-hidden="true" />
          </div>
          <input
            type="text"
            placeholder="Search is disabled until further notice."
            className="w-full pl-10 pr-4 py-2 border rounded-md shadow-md
                      focus:outline-none focus:ring-2 focus:ring-blue-300
                      focus:border-blue-300 transition-all duration-200"
            value={filters.package_name}
            onChange={(e) => onFilterChange('package_name', e.target.value)}
            disabled
          />
        </div>

        {/* Status Filter */}
        <div className={`flex items-center ${isMobile ? 'w-full' : 'ml-4'}`}>
          <span className="mr-2 text-gray-700">Status:</span>
          <Dropdown
            options={statusOptions}
            value={filters.status}
            onChange={(e) => onFilterChange('status', e.value)}
            placeholder="All Statuses"
            className={`shadow-md ${isMobile ? 'flex-1' : 'w-40'}`}
          />
        </div>
      </div>

      {/* Conditional rendering for mobile vs desktop */}
      <div className="w-full flex-grow overflow-hidden">
        {isMobile ? (
          // Mobile view
          <div className="h-full overflow-y-auto">
            <MobileListView />
          </div>
        ) : (
          // Desktop view
          <DataTable
            value={purchaseHistory}
            loading={loading}
            className="table w-full border"
            paginator
            rows={10}
            rowsPerPageOptions={[5, 10, 25, 50]}
            emptyMessage="No purchase history found"
            header={null}
            scrollable
            scrollHeight="100%"
            paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
            currentPageReportTemplate="Showing {first} to {last} of {totalRecords} records"
          >
          <Column
            field="package_name"
            header="Package Name"
            sortable
          />
          <Column
            field="total_price"
            header="Total Price"
            body={priceBodyTemplate}
            sortable
          />
          <Column
            field="purchased_at"
            header="Purchase Date"
            sortable
          />
          <Column
            field="expiry_date"
            header="Expiry Date"
            sortable
          />
          <Column
            field="card_limit"
            header="Allowed Cards Count"
            sortable
          />
          <Column
            field="status"
            header="Status"
            body={statusBodyTemplate}
            sortable
          />
          <Column
            header="Actions"
            body={actionBodyTemplate}
            style={{ width: '100px' }}
          />
          </DataTable>
        )}
      </div>

      {/* Invoice Modal */}
      <Dialog
        visible={invoiceModalVisible}
        onHide={() => setInvoiceModalVisible(false)}
        header={
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-100 to-indigo-200 rounded-lg flex items-center justify-center mr-3">
                <FaEye className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-800">Invoice Preview</h2>
                <p className="text-sm text-gray-500">View, print and download your invoice</p>
              </div>
            </div>
          </div>
        }
        style={{ width: '70vw', maxWidth: '800px' }}
        modal
        className="invoice-modal"
        footer={
          <div className="flex justify-end space-x-4">
            <Button
              label="Download & Print"
              icon={<FaDownload className="mr-3" />}
              onClick={handleDownloadAndPrint}
              className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 hover:from-indigo-700 hover:via-purple-700 hover:to-pink-700 text-white border-0 shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 font-semibold text-sm px-8 py-3 rounded-xl"
            />
            <Button
              label="Close"
              icon={<FaTimes className="mr-3" />}
              onClick={() => setInvoiceModalVisible(false)}
              className="bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white border-0 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 font-semibold text-sm px-6 py-3 rounded-xl"
            />
          </div>
        }
      >
        {selectedInvoice && (
          <div className="p-4 rounded-lg h-[65vh] overflow-y-auto flex items-start justify-center pt-4">
            <div dangerouslySetInnerHTML={{ __html: generateInvoicePDF(selectedInvoice) }} />
          </div>
        )}
      </Dialog>

      <style>{`
        .invoice-modal .p-dialog-content {
          padding: 0;
          max-height: 85vh;
          overflow: hidden;
          position: relative;
          z-index: 1;
        }
        
        .invoice-modal .p-dialog-header {
          background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
          border-bottom: 2px solid #e2e8f0;
          padding: 1.5rem;
          position: relative;
          z-index: 10;
        }
        
        .invoice-modal .p-dialog-header .p-dialog-title {
          color: #1e293b;
        }
        
        .invoice-modal .p-dialog-header .p-dialog-header-icon {
          color: #64748b;
          background: #f1f5f9;
          border-radius: 8px;
          padding: 8px;
          transition: all 0.2s;
        }
        
        .invoice-modal .p-dialog-header .p-dialog-header-icon:hover {
          background: #e2e8f0;
          color: #475569;
        }
        
        .invoice-modal .p-dialog-footer {
          background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
          border-top: 2px solid #e2e8f0;
          padding: 1.5rem;
          position: relative;
          z-index: 10;
        }
        
        .invoice-modal .p-dialog {
          border-radius: 16px;
          box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
      `}</style>
    </Container>
  );
}
