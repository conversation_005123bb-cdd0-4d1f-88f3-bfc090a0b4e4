import { useRef, useState, useEffect } from 'react'
import { Image } from 'primereact/image';
import { Menu } from 'primereact/menu';
import { MdKeyboardArrowDown } from "react-icons/md";
import { RiBillLine } from "react-icons/ri";
import { LuLogOut } from "react-icons/lu";
import { FiSettings } from 'react-icons/fi';
import { useLogoutMutation } from '@quires/auth';
import { useGlobalContext } from '@contexts/GlobalContext';
import { useNavigate } from 'react-router-dom';

// Default user image
const DEFAULT_USER_IMAGE = 'https://storage.inknull.com/uploads/user-image-14-591-1751789627.png';

function Banner() {
    const { userType } = useGlobalContext();
    const navigate = useNavigate();
    const [userImage, setUserImage] = useState('');

    const logout = useLogoutMutation()

    // جلب صورة المستخدم مباشرة من localStorage
    useEffect(() => {
        const userImageFromStorage = localStorage.getItem('user_image');
        if (userImageFromStorage) {
            setUserImage(userImageFromStorage);
        }
    }, []);

    // الاستماع لتغييرات صورة المستخدم في localStorage
    useEffect(() => {
        const handleStorageChange = () => {
            const userImageFromStorage = localStorage.getItem('user_image');
            if (userImageFromStorage) {
                setUserImage(userImageFromStorage);
            }
        };

        window.addEventListener('storage', handleStorageChange);
        
        // الاستماع للتغييرات في نفس التبويب
        const originalSetItem = localStorage.setItem;
        localStorage.setItem = function(key, value) {
            if (key === 'user_image') {
                setUserImage(value);
            }
            originalSetItem.apply(this, arguments);
        };

        return () => {
            window.removeEventListener('storage', handleStorageChange);
            localStorage.setItem = originalSetItem;
        };
    }, []);

    const menu = useRef(null);
    const items = [
        {
            label: 'Options',
            items: [
                {
                    label: 'Settings',
                    icon: <FiSettings className='me-2' />,
                    command: () => {navigate('/manager/settings');

                    }
                },
                {
                    label: 'Billing',
                    icon: <RiBillLine className='me-2' />,
                    command: () => {
                        const prefix = userType === 'admin' ? '/admin' : '/manager';  //Keep it incase I need to give admin a setting page
                        navigate(`${prefix}/billing`);
                    }
                },
                {
                    label: 'Logout',
                    icon: <LuLogOut className='me-2' />,
                    command: async () => {
                        await logout.mutateAsync()
                    }
                }
            ]
        }
    ];

    return (
        <nav className='w-full flex  bg-[white]'>                       {/*If you wish to return it to normal navbar functionallity add these (justify-end p-5) to the class*/}
            <Menu model={items} popup ref={menu} id="popup_menu" />

            <div className='flex items-start'>
                <div className="w-10 h-10 rounded-full overflow-hidden">
                    <Image 
                        src={userImage ? (userImage.startsWith('http') ? userImage : userImage) : DEFAULT_USER_IMAGE} 
                        alt="profile image" 
                        imageClassName="w-full h-full object-cover" 
                        width="40" 
                        height="40"
                        onError={(e) => {
                            e.target.src = DEFAULT_USER_IMAGE;
                        }}
                    />
                </div>
                <button
                    onClick={(event) => menu.current.toggle(event)}
                    aria-controls="popup_menu"
                    aria-haspopup
                    className='flex flex-col mx-2'>
                    <div className='text-md font-bold flex items-center'>
                        <h5 className='me-2 capitalize'>{localStorage.getItem("user_name") || "User name"} </h5>
                        <MdKeyboardArrowDown size={18} />
                    </div>
                    <small className='ms-1 capitalize'>{userType}</small>
                </button>

            </div>
        </nav>
    )
}

export default Banner