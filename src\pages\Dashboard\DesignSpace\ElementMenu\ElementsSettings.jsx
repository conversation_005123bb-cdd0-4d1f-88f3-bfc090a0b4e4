import { useState } from 'react';
import { useDesignSpace } from '@contexts/DesignSpaceContext';
import { v4 as uuidv4 } from 'uuid';
import {
  IoShapesOutline,
  IoColorPaletteOutline
} from 'react-icons/io5';
import {
  BiRectangle,
  BiCircle,
  BiSquare,
  BiPyramid,
  BiDiamond
} from 'react-icons/bi';
import {
  BsHexagon,
  BsStars
} from 'react-icons/bs';
import {
  FaRegCircle,
  FaRegSquare,
  FaRegStar,
  FaHeart,
  FaRegHeart,
  FaShapes
} from 'react-icons/fa';
import {
  MdOutlineHexagon,
  MdOutlineAutoGraph,
  MdOutlineTimeline,
  MdOutlineWavingHand
} from 'react-icons/md';
import {
  RiShapeLine,
  RiRectangleLine,
  RiArrowRightLine,
  RiArrowLeftLine,
  RiArrowUpLine,
  RiArrowDownLine,
  RiArrowRightCircleLine
} from 'react-icons/ri';
import {
  TbSquareRounded,
  TbArrowBigRight,
  TbArrowBigLeft,
  TbArrowWaveRightUp
} from 'react-icons/tb';

// Element categories and items
const elementCategories = [
  {
    id: 'basic-shapes',
    name: 'Basic Shapes',
    icon: <IoShapesOutline size={20} />,
    items: [
      { id: 'rectangle', name: 'Rectangle', icon: <BiRectangle size={32} />, type: 'shape', shapeType: 'rectangle' },
      { id: 'square', name: 'Square', icon: <BiSquare size={32} />, type: 'shape', shapeType: 'square' },
      { id: 'circle', name: 'Circle', icon: <BiCircle size={32} />, type: 'shape', shapeType: 'circle' },
      { id: 'triangle', name: 'Triangle', icon: <BiPyramid size={32} />, type: 'shape', shapeType: 'triangle' },
      { id: 'star', name: 'Star', icon: <FaRegStar size={32} />, type: 'shape', shapeType: 'star' },
      { id: 'heart', name: 'Heart', icon: <FaRegHeart size={32} />, type: 'shape', shapeType: 'heart' },
      { id: 'rounded-square', name: 'Rounded Square', icon: <TbSquareRounded size={32} />, type: 'shape', shapeType: 'rounded-square' },
      { id: 'oval', name: 'Oval', icon: <BiCircle size={32} />, type: 'shape', shapeType: 'oval' },
    ]
  },
  {
    id: 'complex-shapes',
    name: 'Complex Shapes',
    icon: <FaShapes size={20} />,
    items: [
      { id: 'pentagon', name: 'Pentagon', icon: <BsHexagon size={32} />, type: 'shape', shapeType: 'pentagon' },
      { id: 'hexagon', name: 'Hexagon', icon: <BsHexagon size={32} />, type: 'shape', shapeType: 'hexagon' },
      { id: 'octagon', name: 'Octagon', icon: <BsHexagon size={32} />, type: 'shape', shapeType: 'octagon' },
      { id: 'diamond', name: 'Diamond', icon: <BiDiamond size={32} />, type: 'shape', shapeType: 'diamond' },
      { id: 'trapezoid', name: 'Trapezoid', icon: <BiRectangle size={32} />, type: 'shape', shapeType: 'trapezoid' },
      { id: 'parallelogram', name: 'Parallelogram', icon: <BiRectangle size={32} />, type: 'shape', shapeType: 'parallelogram' },
    ]
  },
  {
    id: 'lines',
    name: 'Lines & Arrows',
    icon: <MdOutlineTimeline size={20} />,
    items: [
      { id: 'line', name: 'Line', icon: <MdOutlineTimeline size={32} />, type: 'line', lineType: 'straight' },
      { id: 'arrow-right', name: 'Arrow Right', icon: <RiArrowRightLine size={32} />, type: 'line', lineType: 'arrow-right' },
      { id: 'arrow-left', name: 'Arrow Left', icon: <RiArrowLeftLine size={32} />, type: 'line', lineType: 'arrow-left' },
      { id: 'arrow-up', name: 'Arrow Up', icon: <RiArrowUpLine size={32} />, type: 'line', lineType: 'arrow-up' },
      { id: 'arrow-down', name: 'Arrow Down', icon: <RiArrowDownLine size={32} />, type: 'line', lineType: 'arrow-down' },
      { id: 'arrow-big-right', name: 'Big Arrow Right', icon: <TbArrowBigRight size={32} />, type: 'line', lineType: 'arrow-big-right' },
      { id: 'arrow-big-left', name: 'Big Arrow Left', icon: <TbArrowBigLeft size={32} />, type: 'line', lineType: 'arrow-big-left' },
      { id: 'arrow-circle-right', name: 'Circle Arrow Right', icon: <RiArrowRightCircleLine size={32} />, type: 'line', lineType: 'arrow-circle-right' },
      { id: 'arrow-wave', name: 'Wave Arrow', icon: <TbArrowWaveRightUp size={32} />, type: 'line', lineType: 'arrow-wave' },
      { id: 'curved-line', name: 'Curved Line', icon: <MdOutlineAutoGraph size={32} />, type: 'line', lineType: 'curved' },
    ]
  },
  {
    id: 'frames',
    name: 'Frames',
    icon: <RiShapeLine size={20} />,
    items: [
      { id: 'frame-rectangle', name: 'Rectangle Frame', icon: <RiRectangleLine size={32} />, type: 'frame', frameType: 'rectangle' },
      { id: 'frame-square', name: 'Square Frame', icon: <FaRegSquare size={32} />, type: 'frame', frameType: 'square' },
      { id: 'frame-circle', name: 'Circle Frame', icon: <FaRegCircle size={32} />, type: 'frame', frameType: 'circle' },
      { id: 'frame-heart', name: 'Heart Frame', icon: <FaHeart size={32} />, type: 'frame', frameType: 'heart' },
      { id: 'frame-star', name: 'Star Frame', icon: <BsStars size={32} />, type: 'frame', frameType: 'star' },
      { id: 'frame-hexagon', name: 'Hexagon Frame', icon: <MdOutlineHexagon size={32} />, type: 'frame', frameType: 'hexagon' },
    ]
  },
  {
    id: 'stickers-emoji',
    name: 'Emoji Stickers',
    icon: <MdOutlineWavingHand size={20} />,
    items: [
      { id: 'sticker-smile', name: 'Smile', icon: '😊', type: 'sticker', stickerType: 'smile' },
      { id: 'sticker-laugh', name: 'Laugh', icon: '😂', type: 'sticker', stickerType: 'laugh' },
      { id: 'sticker-love', name: 'Love', icon: '😍', type: 'sticker', stickerType: 'love' },
      { id: 'sticker-cool', name: 'Cool', icon: '😎', type: 'sticker', stickerType: 'cool' },
      { id: 'sticker-wink', name: 'Wink', icon: '😉', type: 'sticker', stickerType: 'wink' },
      { id: 'sticker-think', name: 'Think', icon: '🤔', type: 'sticker', stickerType: 'think' },
      { id: 'sticker-wow', name: 'Wow', icon: '😮', type: 'sticker', stickerType: 'wow' },
      { id: 'sticker-sad', name: 'Sad', icon: '😢', type: 'sticker', stickerType: 'sad' },
      { id: 'sticker-angry', name: 'Angry', icon: '😡', type: 'sticker', stickerType: 'angry' },
    ]
  },
  {
    id: 'stickers-symbols',
    name: 'Symbol Stickers',
    icon: <BsStars size={20} />,
    items: [
      { id: 'sticker-star', name: 'Star', icon: '⭐', type: 'sticker', stickerType: 'star' },
      { id: 'sticker-heart', name: 'Heart', icon: '❤️', type: 'sticker', stickerType: 'heart' },
      { id: 'sticker-fire', name: 'Fire', icon: '🔥', type: 'sticker', stickerType: 'fire' },
      { id: 'sticker-check', name: 'Check', icon: '✅', type: 'sticker', stickerType: 'check' },
      { id: 'sticker-cross', name: 'Cross', icon: '❌', type: 'sticker', stickerType: 'cross' },
      { id: 'sticker-warning', name: 'Warning', icon: '⚠️', type: 'sticker', stickerType: 'warning' },
      { id: 'sticker-lightning', name: 'Lightning', icon: '⚡', type: 'sticker', stickerType: 'lightning' },
      { id: 'sticker-music', name: 'Music', icon: '🎵', type: 'sticker', stickerType: 'music' },
      { id: 'sticker-trophy', name: 'Trophy', icon: '🏆', type: 'sticker', stickerType: 'trophy' },
    ]
  },
  {
    id: 'stickers-objects',
    name: 'Object Stickers',
    icon: <FaShapes size={20} />,
    items: [
      { id: 'sticker-gift', name: 'Gift', icon: '🎁', type: 'sticker', stickerType: 'gift' },
      { id: 'sticker-balloon', name: 'Balloon', icon: '🎈', type: 'sticker', stickerType: 'balloon' },
      { id: 'sticker-camera', name: 'Camera', icon: '📷', type: 'sticker', stickerType: 'camera' },
      { id: 'sticker-phone', name: 'Phone', icon: '📱', type: 'sticker', stickerType: 'phone' },
      { id: 'sticker-computer', name: 'Computer', icon: '💻', type: 'sticker', stickerType: 'computer' },
      { id: 'sticker-bulb', name: 'Light Bulb', icon: '💡', type: 'sticker', stickerType: 'bulb' },
      { id: 'sticker-money', name: 'Money', icon: '💰', type: 'sticker', stickerType: 'money' },
      { id: 'sticker-rocket', name: 'Rocket', icon: '🚀', type: 'sticker', stickerType: 'rocket' },
      { id: 'sticker-clock', name: 'Clock', icon: '🕒', type: 'sticker', stickerType: 'clock' },
    ]
  },
  {
    id: 'gradients-blue',
    name: 'Blue Gradients',
    icon: <IoColorPaletteOutline size={20} />,
    items: [
      { id: 'gradient-blue-1', name: 'Blue Gradient 1', color: 'linear-gradient(45deg, #1e3c72, #2a5298)', type: 'gradient', gradientType: 'blue' },
      { id: 'gradient-blue-2', name: 'Blue Gradient 2', color: 'linear-gradient(45deg, #2980B9, #6DD5FA)', type: 'gradient', gradientType: 'blue' },
      { id: 'gradient-blue-3', name: 'Blue Gradient 3', color: 'linear-gradient(45deg, #0082c8, #667db6)', type: 'gradient', gradientType: 'blue' },
      { id: 'gradient-blue-4', name: 'Blue Gradient 4', color: 'linear-gradient(45deg, #396afc, #2948ff)', type: 'gradient', gradientType: 'blue' },
      { id: 'gradient-blue-5', name: 'Blue Gradient 5', color: 'linear-gradient(45deg, #4e54c8, #8f94fb)', type: 'gradient', gradientType: 'blue' },
      { id: 'gradient-blue-6', name: 'Blue Gradient 6', color: 'linear-gradient(45deg, #0575E6, #021B79)', type: 'gradient', gradientType: 'blue' },
    ]
  },
  {
    id: 'gradients-warm',
    name: 'Warm Gradients',
    icon: <IoColorPaletteOutline size={20} />,
    items: [
      { id: 'gradient-orange', name: 'Orange Gradient', color: 'linear-gradient(45deg, #f12711, #f5af19)', type: 'gradient', gradientType: 'orange' },
      { id: 'gradient-pink', name: 'Pink Gradient', color: 'linear-gradient(45deg, #ee0979, #ff6a00)', type: 'gradient', gradientType: 'pink' },
      { id: 'gradient-red', name: 'Red Gradient', color: 'linear-gradient(45deg, #ED213A, #93291E)', type: 'gradient', gradientType: 'red' },
      { id: 'gradient-sunset', name: 'Sunset Gradient', color: 'linear-gradient(45deg, #FF512F, #F09819)', type: 'gradient', gradientType: 'sunset' },
      { id: 'gradient-peach', name: 'Peach Gradient', color: 'linear-gradient(45deg, #FDC830, #F37335)', type: 'gradient', gradientType: 'peach' },
      { id: 'gradient-rose', name: 'Rose Gradient', color: 'linear-gradient(45deg, #FF9A9E, #FECFEF)', type: 'gradient', gradientType: 'rose' },
    ]
  },
  {
    id: 'gradients-cool',
    name: 'Cool Gradients',
    icon: <IoColorPaletteOutline size={20} />,
    items: [
      { id: 'gradient-purple', name: 'Purple Gradient', color: 'linear-gradient(45deg, #6a3093, #a044ff)', type: 'gradient', gradientType: 'purple' },
      { id: 'gradient-green', name: 'Green Gradient', color: 'linear-gradient(45deg, #11998e, #38ef7d)', type: 'gradient', gradientType: 'green' },
      { id: 'gradient-teal', name: 'Teal Gradient', color: 'linear-gradient(45deg, #11998e, #38ef7d)', type: 'gradient', gradientType: 'teal' },
      { id: 'gradient-mint', name: 'Mint Gradient', color: 'linear-gradient(45deg, #00b09b, #96c93d)', type: 'gradient', gradientType: 'mint' },
      { id: 'gradient-aqua', name: 'Aqua Gradient', color: 'linear-gradient(45deg, #1A2980, #26D0CE)', type: 'gradient', gradientType: 'aqua' },
      { id: 'gradient-violet', name: 'Violet Gradient', color: 'linear-gradient(45deg, #654ea3, #eaafc8)', type: 'gradient', gradientType: 'violet' },
    ]
  }
];

function ElementsSettings() {
  const { addElement } = useDesignSpace();
  const [activeCategory, setActiveCategory] = useState('basic-shapes');
  const [searchTerm, setSearchTerm] = useState('');
  const [shapeColor, setShapeColor] = useState('#4338ca');

  // Get the active category data
  const activeCategoryData = elementCategories.find(cat => cat.id === activeCategory);

  // Filter items based on search term
  const filteredItems = activeCategoryData?.items.filter(item =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle adding an element to the canvas
  const handleAddElement = (item, color = '#4338ca') => {
    // Create a new element based on the item type
    let elementData = {
      id: uuidv4(),
      type: item.type,
      width: 100,
      height: 100,
      x: 100,
      y: 100,
      style: {}
    };

    // Add specific properties based on element type
    switch(item.type) {
      case 'shape':
        elementData.shapeType = item.shapeType;
        elementData.backgroundColor = color; // Use selected color
        break;
      case 'line':
        elementData.lineType = item.lineType;
        elementData.strokeColor = color; // Use selected color
        elementData.strokeWidth = 2;
        break;
      case 'frame':
        elementData.frameType = item.frameType;
        elementData.borderColor = color; // Use selected color
        elementData.borderWidth = 2;
        break;
      case 'sticker':
        elementData.stickerType = item.stickerType;
        elementData.value = item.id; // Use the id as the value for now
        break;
      case 'gradient':
        elementData.gradientType = item.gradientType;
        elementData.style.background = item.color;
        break;
      default:
        break;
    }

    // Add the element to the canvas - zIndex سيتم تعيينه تلقائياً في addElement
    addElement(item.type, "", elementData);
  };

  return (
    <div className="canva-elements">
      <div className="flex items-center justify-between mb-5">
        <h3 className="text-xl font-bold text-gray-800 flex items-center">
          <span className="text-purple-600 mr-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
            </svg>
          </span>
          Professional Elements
        </h3>
        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
          {filteredItems?.length || 0} items
        </span>
      </div>

      {/* Search - Professional Design */}
      <div className="mb-5">
        <div className="relative">
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-purple-500">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <input
            type="text"
            placeholder="Search professional elements..."
            className="w-full p-3 pl-10 pr-10 border border-gray-200 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm bg-white"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          {searchTerm && (
            <button
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              onClick={() => setSearchTerm('')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Categories - Professional design with cards */}
      <div className="mb-6">
        <h4 className="text-md font-medium mb-4 text-gray-700">Categories</h4>
        <div className="grid grid-cols-5 gap-3 overflow-x-auto hide-scrollbar pb-2">
          {elementCategories.map(category => {
            let icon = '';
            let bgGradient = '';
            switch(category.id) {
              case 'basic-shapes':
                icon = '■';
                bgGradient = 'from-blue-500 to-indigo-600';
                break;
              case 'complex-shapes':
                icon = '⬡';
                bgGradient = 'from-indigo-500 to-purple-600';
                break;
              case 'lines':
                icon = '→';
                bgGradient = 'from-purple-500 to-pink-600';
                break;
              case 'frames':
                icon = '□';
                bgGradient = 'from-pink-500 to-rose-600';
                break;
              case 'stickers-emoji':
                icon = '😊';
                bgGradient = 'from-amber-400 to-orange-500';
                break;
              case 'stickers-symbols':
                icon = '⭐';
                bgGradient = 'from-yellow-400 to-amber-500';
                break;
              case 'stickers-objects':
                icon = '🎁';
                bgGradient = 'from-orange-400 to-red-500';
                break;
              case 'gradients-blue':
                icon = '🎨';
                bgGradient = 'from-sky-400 to-blue-500';
                break;
              case 'gradients-warm':
                icon = '🎨';
                bgGradient = 'from-red-400 to-rose-500';
                break;
              case 'gradients-cool':
                icon = '🎨';
                bgGradient = 'from-teal-400 to-emerald-500';
                break;
              default:
                icon = '•';
                bgGradient = 'from-gray-400 to-gray-500';
            }
            return (
              <div
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`relative overflow-hidden rounded-xl cursor-pointer transition-all duration-300 ${
                  activeCategory === category.id
                    ? 'ring-2 ring-offset-2 ring-purple-500 scale-105 shadow-lg'
                    : 'hover:shadow-md hover:scale-105'
                }`}
              >
                <div className={`absolute inset-0 bg-gradient-to-br ${bgGradient} ${activeCategory === category.id ? 'opacity-100' : 'opacity-80'}`}></div>
                <div className="relative p-3 flex flex-col items-center justify-center h-20">
                  <span className="text-2xl mb-1 text-white">{icon}</span>
                  <span className="text-xs font-medium text-white text-center truncate w-full">
                    {category.name}
                  </span>
                </div>
                {activeCategory === category.id && (
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-white"></div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Custom CSS for hiding scrollbar is added to the global CSS */}

      {/* Color Picker for Shapes */}
      <div className="grid grid-cols-3 gap-3">
        {filteredItems?.map(item => (
          <div
            key={item.id}
            className="bg-gray-50 rounded-lg p-3 aspect-square flex flex-col items-center justify-center cursor-pointer hover:bg-gray-100 hover:shadow-sm transition-all duration-200 border border-gray-100"
            onClick={() => handleAddElement(item, shapeColor)}
          >
            {item.type === 'gradient' ? (
              <div
                className="w-14 h-14 rounded-md mb-2 shadow-sm"
                style={{ background: item.color }}
              ></div>
            ) : (
              <div className="mb-2 text-2xl" style={{ color: item.type === 'shape' ? shapeColor : '#4338ca' }}>
                {item.icon}
              </div>
            )}
            <span className="text-xs font-medium text-center text-gray-700">{item.name}</span>
          </div>
        ))}
        {filteredItems?.length === 0 && (
          <div className="col-span-3 py-10 text-center text-gray-500">
            <div className="text-3xl mb-2">🔍</div>
            <div className="font-medium">No elements found</div>
            <div className="text-xs mt-1">Try a different search term or category</div>
          </div>
        )}
      </div>

      {/* {(activeCategory === 'basic-shapes' || activeCategory === 'complex-shapes' || activeCategory === 'lines' || activeCategory === 'frames') && (
        <div className="mb-4">
          <h4 className="text-md font-medium mb-2">Element Color</h4>
          <div className="flex flex-wrap gap-2">
            <div className="w-full mb-2">
              <h5 className="text-xs text-gray-500 mb-1">Basic Colors</h5>
              <div className="flex flex-wrap gap-2">
                {[
                  '#4338ca', '#ef4444', '#f97316', '#eab308', '#22c55e', '#06b6d4', '#8b5cf6', '#ec4899', '#000000', '#ffffff'
                ].map(color => (
                  <button
                    key={color}
                    className={`w-8 h-8 rounded-full border-2 ${shapeColor === color ? 'border-gray-800' : 'border-gray-200'}`}
                    style={{ backgroundColor: color }}
                    onClick={() => setShapeColor(color)}
                    aria-label={`Select color ${color}`}
                  />
                ))}
              </div>
            </div>
            <div className="w-full mb-2">
              <h5 className="text-xs text-gray-500 mb-1">Red Shades</h5>
              <div className="flex flex-wrap gap-2">
                {[
                  '#fee2e2', '#fecaca', '#fca5a5', '#f87171', '#ef4444', '#dc2626', '#b91c1c', '#991b1b', '#7f1d1d', '#450a0a'
                ].map(color => (
                  <button
                    key={color}
                    className={`w-8 h-8 rounded-full border-2 ${shapeColor === color ? 'border-gray-800' : 'border-gray-200'}`}
                    style={{ backgroundColor: color }}
                    onClick={() => setShapeColor(color)}
                    aria-label={`Select color ${color}`}
                  />
                ))}
              </div>
            </div>
            <div className="w-full mb-2">
              <h5 className="text-xs text-gray-500 mb-1">Orange Shades</h5>
              <div className="flex flex-wrap gap-2">
                {[
                  '#ffedd5', '#fed7aa', '#fdba74', '#fb923c', '#f97316', '#ea580c', '#c2410c', '#9a3412', '#7c2d12', '#431407'
                ].map(color => (
                  <button
                    key={color}
                    className={`w-8 h-8 rounded-full border-2 ${shapeColor === color ? 'border-gray-800' : 'border-gray-200'}`}
                    style={{ backgroundColor: color }}
                    onClick={() => setShapeColor(color)}
                    aria-label={`Select color ${color}`}
                  />
                ))}
              </div>
            </div>
            <div className="w-full mb-2">
              <h5 className="text-xs text-gray-500 mb-1">Yellow Shades</h5>
              <div className="flex flex-wrap gap-2">
                {[
                  '#fef9c3', '#fef08a', '#fde047', '#facc15', '#eab308', '#ca8a04', '#a16207', '#854d0e', '#713f12', '#422006'
                ].map(color => (
                  <button
                    key={color}
                    className={`w-8 h-8 rounded-full border-2 ${shapeColor === color ? 'border-gray-800' : 'border-gray-200'}`}
                    style={{ backgroundColor: color }}
                    onClick={() => setShapeColor(color)}
                    aria-label={`Select color ${color}`}
                  />
                ))}
              </div>
            </div>
            <div className="w-full mb-2">
              <h5 className="text-xs text-gray-500 mb-1">Green Shades</h5>
              <div className="flex flex-wrap gap-2">
                {[
                  '#dcfce7', '#bbf7d0', '#86efac', '#4ade80', '#22c55e', '#16a34a', '#15803d', '#166534', '#14532d', '#052e16'
                ].map(color => (
                  <button
                    key={color}
                    className={`w-8 h-8 rounded-full border-2 ${shapeColor === color ? 'border-gray-800' : 'border-gray-200'}`}
                    style={{ backgroundColor: color }}
                    onClick={() => setShapeColor(color)}
                    aria-label={`Select color ${color}`}
                  />
                ))}
              </div>
            </div>
            <div className="w-full mb-2">
              <h5 className="text-xs text-gray-500 mb-1">Blue Shades</h5>
              <div className="flex flex-wrap gap-2">
                {[
                  '#dbeafe', '#bfdbfe', '#93c5fd', '#60a5fa', '#3b82f6', '#2563eb', '#1d4ed8', '#1e40af', '#1e3a8a', '#172554'
                ].map(color => (
                  <button
                    key={color}
                    className={`w-8 h-8 rounded-full border-2 ${shapeColor === color ? 'border-gray-800' : 'border-gray-200'}`}
                    style={{ backgroundColor: color }}
                    onClick={() => setShapeColor(color)}
                    aria-label={`Select color ${color}`}
                  />
                ))}
              </div>
            </div>
            <div className="w-full mb-2">
              <h5 className="text-xs text-gray-500 mb-1">Purple Shades</h5>
              <div className="flex flex-wrap gap-2">
                {[
                  '#ede9fe', '#ddd6fe', '#c4b5fd', '#a78bfa', '#8b5cf6', '#7c3aed', '#6d28d9', '#5b21b6', '#4c1d95', '#2e1065'
                ].map(color => (
                  <button
                    key={color}
                    className={`w-8 h-8 rounded-full border-2 ${shapeColor === color ? 'border-gray-800' : 'border-gray-200'}`}
                    style={{ backgroundColor: color }}
                    onClick={() => setShapeColor(color)}
                    aria-label={`Select color ${color}`}
                  />
                ))}
              </div>
            </div>
            <div className="w-full mb-2">
              <h5 className="text-xs text-gray-500 mb-1">Pink Shades</h5>
              <div className="flex flex-wrap gap-2">
                {[
                  '#fce7f3', '#fbcfe8', '#f9a8d4', '#f472b6', '#ec4899', '#db2777', '#be185d', '#9d174d', '#831843', '#500724'
                ].map(color => (
                  <button
                    key={color}
                    className={`w-8 h-8 rounded-full border-2 ${shapeColor === color ? 'border-gray-800' : 'border-gray-200'}`}
                    style={{ backgroundColor: color }}
                    onClick={() => setShapeColor(color)}
                    aria-label={`Select color ${color}`}
                  />
                ))}
              </div>
            </div>
            <div className="w-full mb-2">
              <h5 className="text-xs text-gray-500 mb-1">Gray Shades</h5>
              <div className="flex flex-wrap gap-2">
                {[
                  '#f9fafb', '#f3f4f6', '#e5e7eb', '#d1d5db', '#9ca3af', '#6b7280', '#4b5563', '#374151', '#1f2937', '#111827'
                ].map(color => (
                  <button
                    key={color}
                    className={`w-8 h-8 rounded-full border-2 ${shapeColor === color ? 'border-gray-800' : 'border-gray-200'}`}
                    style={{ backgroundColor: color }}
                    onClick={() => setShapeColor(color)}
                    aria-label={`Select color ${color}`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      )} */}
    </div>
  );
}

export default ElementsSettings;
