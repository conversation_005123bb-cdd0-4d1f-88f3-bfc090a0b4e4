import _ from "lodash";
import { useMutation, useQuery } from "react-query"

import { useDataTableContext } from "@contexts/DataTableContext"
import { groupsTableConfig } from "@constants/tableConfig";
import { useGlobalContext } from "@contexts/GlobalContext"
import { handleErrors } from "@utils/helper";
import axiosInstance from '../config/Axios';

//--------------create group-------------- //
const createGroup = async (payload) => {
    const { data } = await axiosInstance.post("/groups", payload);

    return data;
}

export const useCreateGroupMutation = () => {
    const { showToast, dialogHandler } = useGlobalContext();

    return useMutation(createGroup, {
        onSuccess: async (data) => {
            dialogHandler("createGroup")
            showToast("success", data?.message)
        },
        onError: (error) => handleErrors(showToast, error)
    })
}

//--------------update group-------------- //
const updateGroup = async (payload) => {
    const { data } = await axiosInstance.post(`/groups/${payload.id}`, payload);

    return data;
}

export const useUpdateGroupMutation = () => {
    const { showToast, dialogHandler } = useGlobalContext();
    const { setLazyParams } = useDataTableContext();

    return useMutation(updateGroup, {
        onSuccess: async (data) => {
            dialogHandler("updateGroup")
            showToast("success", data?.message);
            setLazyParams(prev => ({ ...prev, ...groupsTableConfig }))
        },
        onError: (error) => handleErrors(showToast, error)
    })
}

//--------------delete group-------------- //
const deleteGroup = async (payload) => {
    const { data } = await axiosInstance.delete(`/groups/${payload.id}`);

    return data;
}

export const useDeleteGroupMutation = () => {
    const { showToast } = useGlobalContext();
    const { setLazyParams } = useDataTableContext();

    return useMutation(deleteGroup, {
        onSuccess: async () => {
            showToast("success", "Group deleted successfully!");
            setLazyParams(prev => ({ ...prev, ...groupsTableConfig }))
        },
        onError: (error) => handleErrors(showToast, error)
    })
}

//--------------return group's members-------------- //
const getGroupMembers = async (id) => {
    const { data } = await axiosInstance.get(`/groups/get-users-ids?group_id=${id}`);

    return data;
}

export const useGetGroupMember = (id) => {
    const { showToast } = useGlobalContext();
 
    return useMutation(getGroupMembers, {
        onError: (error) => handleErrors(showToast, error)
    })
}