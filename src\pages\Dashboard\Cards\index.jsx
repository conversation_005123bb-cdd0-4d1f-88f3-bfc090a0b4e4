import { useGetCardsTypes } from '@quires';
import Container from '@components/Container';
import React, { useState, useEffect, useRef } from 'react';
import CreateTypeForm from '../Cards/CreateTypeForm';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { Toast } from 'primereact/toast';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';

function CardsIndex() {
  const { data, isLoading, refetch } = useGetCardsTypes();
  const [isCreateTypeModalOpen, setIsCreateTypeModalOpen] = useState(false);
  const [editData, setEditData] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [localData, setLocalData] = useState([]);
  const toast = useRef(null);
  const backendUrl = import.meta.env.VITE_BACKEND_URL;
  const token = localStorage.getItem("token");

  useEffect(() => {
    if (data) {
      setLocalData(data);
    }
  }, [data]);

  const openCreateTypeModal = () => {
    setIsEditMode(false);
    setIsCreateTypeModalOpen(true);
  };

  const handleEdit = (rowData) => {
    setEditData(rowData);
    setIsEditMode(true);
    setIsCreateTypeModalOpen(true);
  };

  const resetEditMode = () => {
    setIsEditMode(false);
    setEditData(null);
  };

  const handleDelete = (id) => {
    confirmDialog({
      message: 'Are you sure you want to delete this card type?',
      header: 'Confirmation',
      icon: 'pi pi-exclamation-triangle',
      accept: () => deleteCardType(id),
      reject: () => {}
    });
  };

  const deleteCardType = async (id) => {
    try {
      const response = await fetch(`${backendUrl}/card-types/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: 'Card type deleted successfully',
          life: 3000
        });
        setLocalData(localData.filter(item => item.id !== id));
      } else {
        const errorData = await response.json();
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: errorData.message || 'Failed to delete card type',
          life: 3000
        });
      }
    } catch (error) {
      console.error('Error:', error);
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'An error occurred while deleting card type',
        life: 3000
      });
    }
  };

  const actionBodyTemplate = (rowData) => {
    return (
      <div className="flex gap-1 justify-center">
        <Button
        icon="pi pi-pencil"
        className="p-button-rounded p-button-warning p-button-text"
        onClick={() => handleEdit(rowData)}
        tooltip="Edit"
        tooltipOptions={{ position: 'top' }}
        />

        <Button
          icon="pi pi-trash"
          className="p-button-rounded p-button-danger p-button-text"
          onClick={() => handleDelete(rowData.id)}
          tooltip="Delete"
          tooltipOptions={{ position: 'top' }}
        />

              {/* <Tooltip target=".edit-icon" content="Edit" position="top" />
              <Link to={`/edit-card/${rowData.id}`} className="edit-icon">
                <FiEdit className="text-yellow-500" size={20} />
              </Link> */}
      </div>
    );
  };

  return (
    <Container>
      <Toast ref={toast} />
      <ConfirmDialog />
      <div className="w-full flex justify-center mb-5">
        <div className='w-full'>
          <h1 className='text-xl font-bold'>Card Types</h1>
        </div>
        <div className="w-8/12 flex justify-end">
          <button
            className="main-btn text-md shadow-md"
            onClick={openCreateTypeModal}
          >
            Create New Type
          </button>
        </div>
      </div>

      <div className='table-responsive text-nowrap'>
        <CreateTypeForm
          isModalOpen={isCreateTypeModalOpen}
          setIsModalOpen={setIsCreateTypeModalOpen}
          fetchCardTypes={refetch}
          editData={editData}
          isEditMode={isEditMode}
          resetEditMode={resetEditMode}
        />

        <DataTable
          lazy
          responsiveLayout="scroll"
          className="table w-full border"
          value={localData}
          loading={isLoading}
        >
          <Column field="name" header="Name" className='text-center' />
          <Column field="type_of_connection" header="Type Of Connection" className='text-center' />
          <Column field="number_of_colors" header="Number Of Colors" className='text-center' />
          <Column field="setting.height" header="Height (px)" className='text-center' />
          <Column field="setting.width" header="Width (px)" className='text-center' />
          <Column 
            body={actionBodyTemplate} 
            header="Actions" 
            className='text-center' 
            style={{ width: '120px' }}
          />
        </DataTable>
      </div>
    </Container>
  );
}

export default CardsIndex;