import { useState } from 'react';
import { Dropdown } from 'primereact/dropdown';

import { useDesignSpace } from '@contexts/DesignSpaceContext';
import { fieldsOptions } from '@constants/DesignSpaceConfig';

function FieldsDropdown() {
    const { addElement, getActiveTextStyle } = useDesignSpace();
    const [selectedField, setSelectedField] = useState("");

    const onChangeHandler = (val) => {
        const activeStyle = getActiveTextStyle && getActiveTextStyle();
        let customProps = {};
        if (activeStyle) {
            const pureStyle = JSON.parse(JSON.stringify(activeStyle));
            delete pureStyle.value;
            delete pureStyle.id;
            customProps = { ...pureStyle };
        }
        addElement("text", val, customProps);
        setSelectedField("");
    }
 
    return (
        <>
            <Dropdown
                defaultValue={fieldsOptions[0].dimension}
                className='rounded-[6px] me-3 text-[black] w-full'
                optionLabel="label"
                optionValue="value"
                value={selectedField}
                options={fieldsOptions}
                onChange={(e) => { setSelectedField(e.value); onChangeHandler(e.value); }}
                placeholder="select field ..." />
        </>
    )
}

export default FieldsDropdown