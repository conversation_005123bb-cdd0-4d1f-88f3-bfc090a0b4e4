// Video Templates for DesignSpace
// This file contains professional video templates and layouts

// Video Thumbnail Templates
export const videoThumbnailTemplates = [
  {
    id: 'vt-001',
    name: 'YouTube Thumbnail',
    width: 1280,
    height: 720,
    thumbnail: 'https://placehold.co/1280x720/ff0000/ffffff?text=YouTube+Thumbnail',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1280,
        height: 720,
        backgroundColor: '#ff0000',
        style: { background: 'linear-gradient(135deg, #ff0000, #cc0000)' },
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 40,
        y: 40,
        width: 1200,
        height: 640,
        backgroundColor: 'rgba(0,0,0,0.3)',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 100,
        y: 200,
        width: 1080,
        height: 200,
        value: 'ENGAGING VIDEO TITLE',
        fontSize: 80,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
        textShadow: '2px 2px 4px rgba(0,0,0,0.5)',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 100,
        y: 400,
        width: 1080,
        height: 100,
        value: 'SUBTITLE OR DESCRIPTION',
        fontSize: 40,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'center',
        textShadow: '2px 2px 4px rgba(0,0,0,0.5)',
      },
    ]
  },
  {
    id: 'vt-002',
    name: 'Gaming Video Thumbnail',
    width: 1280,
    height: 720,
    thumbnail: 'https://placehold.co/1280x720/6d28d9/ffffff?text=Gaming+Thumbnail',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1280,
        height: 720,
        backgroundColor: '#6d28d9',
        style: { background: 'linear-gradient(135deg, #6d28d9, #4c1d95)' },
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 640,
        y: 100,
        width: 600,
        height: 520,
        backgroundColor: '#e2e8f0',
        borderColor: '#ffffff',
        borderWidth: 10,
      },
      {
        id: 'el_3',
        type: 'text',
        x: 40,
        y: 150,
        width: 560,
        height: 300,
        value: 'EPIC GAMING MOMENTS',
        fontSize: 70,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'left',
        textShadow: '2px 2px 4px rgba(0,0,0,0.5)',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 40,
        y: 450,
        width: 560,
        height: 100,
        value: 'EPISODE 42',
        fontSize: 50,
        fontWeight: 'bold',
        color: '#f59e0b',
        textAlign: 'left',
        textShadow: '2px 2px 4px rgba(0,0,0,0.5)',
      },
    ]
  },
  {
    id: 'vt-003',
    name: 'Tutorial Video Thumbnail',
    width: 1280,
    height: 720,
    thumbnail: 'https://placehold.co/1280x720/0ea5e9/ffffff?text=Tutorial+Thumbnail',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1280,
        height: 720,
        backgroundColor: '#0ea5e9',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 40,
        y: 40,
        width: 1200,
        height: 640,
        backgroundColor: '#ffffff',
      },
      {
        id: 'el_3',
        type: 'shape',
        shapeType: 'rectangle',
        x: 40,
        y: 40,
        width: 1200,
        height: 120,
        backgroundColor: '#0ea5e9',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 100,
        y: 80,
        width: 1080,
        height: 40,
        value: 'STEP-BY-STEP TUTORIAL',
        fontSize: 40,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_5',
        type: 'shape',
        shapeType: 'rectangle',
        x: 100,
        y: 200,
        width: 500,
        height: 400,
        backgroundColor: '#e2e8f0',
      },
      {
        id: 'el_6',
        type: 'text',
        x: 640,
        y: 250,
        width: 540,
        height: 300,
        value: 'HOW TO CREATE\nAMAZING\nDESIGNS',
        fontSize: 60,
        fontWeight: 'bold',
        color: '#0f172a',
        textAlign: 'left',
      },
    ]
  }
];

// Video Intro Templates
export const videoIntroTemplates = [
  {
    id: 'vi-001',
    name: 'Channel Intro Slide',
    width: 1920,
    height: 1080,
    thumbnail: 'https://placehold.co/1920x1080/0f172a/ffffff?text=Channel+Intro',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1920,
        height: 1080,
        backgroundColor: '#0f172a',
      },
      {
        id: 'el_2',
        type: 'text',
        x: 200,
        y: 400,
        width: 1520,
        height: 200,
        value: 'CHANNEL NAME',
        fontSize: 120,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
        letterSpacing: '10px',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 200,
        y: 600,
        width: 1520,
        height: 80,
        value: 'YOUR CHANNEL TAGLINE HERE',
        fontSize: 40,
        fontWeight: 'normal',
        color: '#94a3b8',
        textAlign: 'center',
        letterSpacing: '5px',
      },
    ]
  }
];

// Export all video templates
export const allVideoTemplates = [
  ...videoThumbnailTemplates,
  ...videoIntroTemplates
];
