import { useState, useRef, useEffect } from 'react';
import { InputText } from 'primereact/inputtext';
import { Password } from 'primereact/password';
import { But<PERSON> } from 'primereact/button';
import { Toast } from 'primereact/toast';
import { Image } from 'primereact/image';
import axiosInstance from "../../../config/Axios";

// API URL for image storage
const API_URL = import.meta.env.VITE_REACT_APP_API_URL || 'http://localhost:8000';

// Default user image
const DEFAULT_USER_IMAGE = 'https://storage.inknull.com/uploads/user-image-14-591-1751789627.png';

function SettingsIndex() {
    const toast = useRef(null);
    const fileInputRef = useRef(null);
    const [isEditingProfile, setIsEditingProfile] = useState(false);
    const [isEditingPassword, setIsEditingPassword] = useState(false);
    const [originalName, setOriginalName] = useState('');
    const [originalEmail, setOriginalEmail] = useState('');
    const [loading, setLoading] = useState(false);
    const [imageLoading, setImageLoading] = useState(false);
    const [errors, setErrors] = useState({});
    const [userImage, setUserImage] = useState('');

    // Form states
    const [name, setName] = useState('');
    const [email, setEmail] = useState('');
    const [currentPassword, setCurrentPassword] = useState('');
    const [newPassword, setNewPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');

    // Fetch user data on component mount
    useEffect(() => {
        fetchUserData();
    }, []);

    const fetchUserData = async () => {
        try {
            const userId = localStorage.getItem('user_id');
            const response = await axiosInstance.get(`/users/${userId}`);
            const userData = response.data.data || response.data;

            setName(userData.name || '');
            setEmail(userData.email || '');
            setOriginalName(userData.name || '');
            setOriginalEmail(userData.email || '');
            
            // Handle image path - if it's already a full URL, use it as is
            if (userData.image) {
                setUserImage(userData.image);
                // حفظ صورة المستخدم في localStorage
                localStorage.setItem('user_image', userData.image);
            } else {
                setUserImage('');
                // إزالة صورة المستخدم من localStorage
                localStorage.removeItem('user_image');
            }
        } catch (error) {
            console.error('Error fetching user data:', error);
            showMessage('error', 'Error', 'Failed to load user data');
        }
    };

    const showMessage = (severity, summary, detail) => {
        toast.current?.show({ severity, summary, detail, life: 3000 });
    };

    const handleImageUpload = async (event) => {
        const file = event.target.files[0];
        if (!file) return;

        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            showMessage('error', 'Invalid File Type', 'Please select a valid image file (JPEG, PNG, GIF)');
            return;
        }

        // Validate file size (max 5MB)
        const maxSize = 5 * 1024 * 1024; // 5MB
        if (file.size > maxSize) {
            showMessage('error', 'File Too Large', 'Image size should be less than 5MB');
            return;
        }

        setImageLoading(true);
        setErrors({});

        try {
            const userId = localStorage.getItem('user_id');
            const formData = new FormData();
            formData.append('image', file);

            const response = await axiosInstance.post(`/users/${userId}/upload-image`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            console.log('Image upload response:', response.data);

            // Update the user image with the new URL
            const newImageUrl = response.data.data?.image || response.data.image;
            setUserImage(newImageUrl);
            
            // حفظ صورة المستخدم في localStorage للتحديث المباشر
            localStorage.setItem('user_image', newImageUrl);
            
            showMessage('success', 'Image Updated', 'Profile image updated successfully');

            // Refresh user data to get the updated image
            await fetchUserData();

        } catch (error) {
            console.error('Error uploading image:', error);
            console.log('Error response:', error.response?.data);

            if (error.response?.data?.details) {
                setErrors(error.response.data.details);
            } else if (error.response?.data?.errors) {
                setErrors(error.response.data.errors);
            }

            showMessage('error', 'Upload Failed', error.response?.data?.message || 'Failed to upload image');
        } finally {
            setImageLoading(false);
        }
    };

    const handleUpdateProfile = async () => {
        setLoading(true);
        setErrors({});

        try {
            const userId = localStorage.getItem('user_id');

            console.log('Sending profile update data:', {
                name: name,
                email: email
            });

            const response = await axiosInstance.put(`/users/${userId}`, {
                name: name,
                email: email
            });

            console.log('Profile update response:', response.data);

            showMessage('success', 'Profile Updated', 'Changes saved successfully');
            setIsEditingProfile(false);
            setOriginalName(name);
            setOriginalEmail(email);
        } catch (error) {
            console.error('Error updating profile:', error);
            console.log('Error response:', error.response?.data);

            if (error.response?.data?.details) {
                setErrors(error.response.data.details);
            } else if (error.response?.data?.errors) {
                setErrors(error.response.data.errors);
            }

            showMessage('error', 'Update Failed', error.response?.data?.message || 'Failed to update profile');
        } finally {
            setLoading(false);
        }
    };

    const handleChangePassword = async () => {
        if (newPassword !== confirmPassword) {
            showMessage('error', 'Error', 'Passwords do not match');
            setErrors({ confirmPassword: 'Passwords do not match' });
            return;
        }

        setLoading(true);
        setErrors({});

        try {
            const userId = localStorage.getItem('user_id');

            const passwordData = {
                id: userId,
                current_password: currentPassword,
                new_password: newPassword,
                new_password_confirmation: confirmPassword
            };

            const response = await axiosInstance.post(`/users/change-password`, passwordData);

            console.log('Password change response:', response.data);

            showMessage('success', 'Password Changed', 'Password updated successfully');
            setIsEditingPassword(false);
            setCurrentPassword('');
            setNewPassword('');
            setConfirmPassword('');
        } catch (error) {
            console.error('Error changing password:', error);
            console.log('Error response:', error.response?.data);

            if (error.response?.data?.details) {
                setErrors(error.response.data.details);
            } else if (error.response?.data?.errors) {
                setErrors(error.response.data.errors);
            }

            showMessage('error', 'Update Failed', error.response?.data?.message || 'Failed to update password');
        } finally {
            setLoading(false);
        }
    };

    const enterEditProfileMode = () => {
        setOriginalName(name);
        setOriginalEmail(email);
        setIsEditingProfile(true);
    };

    const cancelEditProfile = () => {
        setName(originalName);
        setEmail(originalEmail);
        setIsEditingProfile(false);
    };

    const enterEditImageMode = () => {
        // فتح نافذة اختيار الملف مباشرة
        setTimeout(() => {
            fileInputRef.current?.click();
        }, 100);
    };

    return (
        <section className='w-full flex flex-col p-5 h-[95vh] overflow-y-auto bg-white' >
            <Toast ref={toast} />

            <div className="w-full">
                {/* العنوان على أقصى اليسار */}
                <div className="mb-8 text-left">
                    <h2 className="text-3xl font-bold text-gray-800 flex items-center justify-start gap-3">
                        <i className="pi pi-user bg-blue-100 rounded-full text-blue-600 text-3xl"></i>
                        Account Settings
                    </h2>
                    <p className="text-gray-500 mt-2">Manage your personal information and security preferences</p>
                </div>

                {/* Profile Section */}
                <div className="bg-white rounded-xl shadow-md p-8 mb-8 transition-all hover:shadow-lg border border-gray-100" style={{ backgroundColor: '#ffffff' }}>
                    <div className="flex flex-col md:flex-row gap-8 mb-4">
                        <div className="flex flex-col items-center">
                            <div className="relative group">
                                {/* حاوية الصورة الرئيسية */}
                                <div className="w-[130px] h-[130px] rounded-full border-4 border-blue-50 shadow-lg overflow-hidden bg-gradient-to-br from-blue-50 to-purple-50 relative">
                                    <Image 
                                        src={userImage ? (userImage.startsWith('http') ? userImage : `${API_URL}/storage/${userImage}`) : DEFAULT_USER_IMAGE} 
                                        alt="profile"
                                        imageClassName="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110" 
                                        width="130" 
                                        height="130"
                                        preview={false}
                                        onError={(e) => {
                                            e.target.src = DEFAULT_USER_IMAGE;
                                        }}
                                    />
                                    
                                    {/* طبقة التدرج فوق الصورة */}
                                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                    
                                    {/* أيقونة الكاميرا في المنتصف عند التمرير */}
                                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                                        <div className="bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-lg">
                                            <i className="pi pi-camera text-blue-600 text-lg"></i>
                                        </div>
                                    </div>
                                </div>
                                
                                {/* زر تغيير الصورة */}
                                <Button 
                                    icon="pi pi-camera" 
                                    className="p-button-rounded p-button-primary absolute -bottom-2 -right-2 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 border-2 border-white"
                                    tooltip="Change Photo" 
                                    tooltipOptions={{position: 'bottom'}}
                                    onClick={enterEditImageMode}
                                />
                                
                                {/* مؤشر التحميل */}
                                {imageLoading && (
                                    <div className="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center">
                                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                                    </div>
                                )}
                                
                                {/* Input الملف المخفي */}
                                <input
                                    ref={fileInputRef}
                                    type="file"
                                    accept="image/*"
                                    onChange={handleImageUpload}
                                    className="hidden"
                                />
                            </div>
                        </div>
                        <div className="flex-1">
                            {!isEditingProfile ? (
                                <>
                                    <div className="flex justify-between items-center mb-6">
                                        <h3 className="text-xl font-semibold text-gray-800">Profile Information</h3>
                                        <Button icon="pi pi-pencil"
                                            className="p-button-rounded p-button-outlined p-button-primary"
                                            onClick={enterEditProfileMode} />
                                    </div>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="field bg-white p-4 rounded-lg" style={{ backgroundColor: '#ffffff' }}>
                                            <label className="text-sm font-medium text-gray-500 block mb-1">Name</label>
                                            <p className="text-gray-800 font-medium">{name || 'No name provided'}</p>
                                        </div>
                                        <div className="field bg-white p-4 rounded-lg" style={{ backgroundColor: '#ffffff' }}>
                                            <label className="text-sm font-medium text-gray-500 block mb-1">Email</label>
                                            <p className="text-gray-800 font-medium">{email || 'No email provided'}</p>
                                        </div>
                                    </div>
                                </>
                            ) : (
                                <>
                                    <div className="flex justify-between items-center mb-6">
                                        <h3 className="text-xl font-semibold text-gray-800">Edit Profile</h3>
                                        <Button icon="pi pi-times"
                                            className="p-button-rounded p-button-outlined p-button-danger"
                                            onClick={cancelEditProfile} />
                                    </div>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="field">
                                            <label className="text-sm font-medium text-gray-700 block mb-2">Name</label>
                                            <InputText
                                                value={name}
                                                onChange={(e) => setName(e.target.value)}
                                                className={`w-full p-inputtext-sm p-3 rounded-lg border ${errors.name ? 'border-red-500' : 'border-gray-300'}`}
                                            />
                                            {errors.name && <small className="p-error block mt-1">{errors.name}</small>}
                                        </div>
                                        <div className="field">
                                            <label className="text-sm font-medium text-gray-700 block mb-2">Email</label>
                                            <InputText
                                                type="email"
                                                value={email}
                                                onChange={(e) => setEmail(e.target.value)}
                                                className={`w-full p-inputtext-sm p-3 rounded-lg border ${errors.email ? 'border-red-500' : 'border-gray-300'}`}
                                            />
                                            {errors.email && <small className="p-error block mt-1">{errors.email}</small>}
                                        </div>
                                    </div>
                                    <div className="flex gap-3 justify-end mt-6">
                                        <Button label="Cancel" severity="secondary" className="p-button-sm"
                                            onClick={cancelEditProfile} />
                                        <Button label="Save Changes" className="p-button-sm main-btn"
                                            onClick={handleUpdateProfile}
                                            loading={loading} />
                                    </div>
                                </>
                            )}
                        </div>
                    </div>
                </div>

                {/* خط فاصل على كامل عرض الصفحة */}
                <div className="w-full border-t border-gray-200 my-10 mx-0"></div>

                {/* Security Settings Header - على أقصى اليسار */}
                <div className="text-left mb-8">
                    <h2 className="text-3xl font-bold text-gray-800 flex items-center justify-start gap-3">
                        <i className="pi pi-lock bg-purple-100 rounded-full text-purple-600 text-3xl"></i>
                        Security Settings
                    </h2>
                    <p className="text-gray-500 mt-2">Protect your account with a strong password</p>
                </div>

                {/* Password Section */}
                <div className="bg-white rounded-xl shadow-md p-8 transition-all hover:shadow-lg border border-gray-100" style={{ backgroundColor: '#ffffff' }}>
                    {!isEditingPassword ? (
                        <div className="flex justify-between items-center">
                            <div className="text-left">
                                <h3 className="text-xl font-semibold text-gray-800 mb-2">Password</h3>
                                <p className="text-gray-500">Secure your account with a strong password</p>
                            </div>
                            <Button label="Change Password" icon="pi pi-lock"
                                className="p-button-outlined p-button-primary"
                                onClick={() => setIsEditingPassword(true)} />
                        </div>
                    ) : (
                        <>
                            <div className="flex justify-between items-center mb-6">
                                <h3 className="text-xl font-semibold text-gray-800">Change Password</h3>
                                <Button icon="pi pi-times"
                                    className="p-button-rounded p-button-outlined p-button-danger"
                                    onClick={() => setIsEditingPassword(false)} />
                            </div>
                            <div className="grid grid-cols-1 gap-6">
                                <div className="field bg-white p-4 rounded-lg" style={{ backgroundColor: '#ffffff' }}>
                                    <label className="text-sm font-medium text-gray-700 block mb-2">Current Password</label>
                                    <Password
                                        value={currentPassword}
                                        onChange={(e) => setCurrentPassword(e.target.value)}
                                        toggleMask
                                        className={`w-full ${errors.current_password ? 'p-invalid' : ''}`}
                                        inputClassName="w-full p-inputtext-sm p-3 rounded-lg border border-gray-300"
                                        feedback={false}
                                    />
                                    {errors.current_password && <small className="p-error block mt-1">{errors.current_password}</small>}
                                </div>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div className="field bg-white p-4 rounded-lg" style={{ backgroundColor: '#ffffff' }}>
                                        <label className="text-sm font-medium text-gray-700 block mb-2">New Password</label>
                                        <Password
                                            value={newPassword}
                                            onChange={(e) => setNewPassword(e.target.value)}
                                            toggleMask
                                            className={`w-full ${errors.new_password ? 'p-invalid' : ''}`}
                                            inputClassName="w-full p-inputtext-sm p-3 rounded-lg border border-gray-300"
                                        />
                                        {errors.new_password && <small className="p-error block mt-1">{errors.new_password}</small>}
                                    </div>
                                    <div className="field bg-white p-4 rounded-lg" style={{ backgroundColor: '#ffffff' }}>
                                        <label className="text-sm font-medium text-gray-700 block mb-2">Confirm Password</label>
                                        <Password
                                            value={confirmPassword}
                                            onChange={(e) => setConfirmPassword(e.target.value)}
                                            toggleMask
                                            className={`w-full ${errors.confirmPassword || (newPassword !== confirmPassword) ? 'p-invalid' : ''}`}
                                            inputClassName="w-full p-inputtext-sm p-3 rounded-lg border border-gray-300"
                                        />
                                        {errors.confirmPassword && <small className="p-error block mt-1">{errors.confirmPassword}</small>}
                                    </div>
                                </div>
                            </div>
                            <div className="flex gap-3 justify-end mt-6">
                                <Button label="Cancel" severity="secondary" className="p-button-sm"
                                    onClick={() => setIsEditingPassword(false)} />
                                <Button label="Update Password" className="p-button-sm main-btn"
                                    onClick={handleChangePassword}
                                    loading={loading} />
                            </div>
                        </>
                    )}
                </div>
            </div>
        </section>
    );
}

export default SettingsIndex;
