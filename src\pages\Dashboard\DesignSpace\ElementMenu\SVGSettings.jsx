import { useState } from 'react';
import { useDesignSpace } from '@contexts/DesignSpaceContext';
import { v4 as uuidv4 } from 'uuid';

const SVGSettings = () => {
  const { addElement } = useDesignSpace();
  const [svgContent, setSvgContent] = useState('');
  const [error, setError] = useState('');

  // Example SVG templates
  const svgTemplates = [
    {
      name: 'Gear',
      svg: `<svg width='400' height='400' viewBox='0 0 400 400' xmlns='http://www.w3.org/2000/svg'>
        <circle cx='200' cy='200' r='180' fill='#e0e0e0' stroke='#999' stroke-width='4'/>
        <g id='gear1' transform='translate(150, 150)'>
          <circle r='50' fill='#ff9800'/>
          <path d='M0,-50 L10,-40 L0,-30 L-10,-40 Z' fill='#ef6c00' transform='rotate(0)'/>
          <path d='M0,-50 L10,-40 L0,-30 L-10,-40 Z' fill='#ef6c00' transform='rotate(45)'/>
          <path d='M0,-50 L10,-40 L0,-30 L-10,-40 Z' fill='#ef6c00' transform='rotate(90)'/>
          <path d='M0,-50 L10,-40 L0,-30 L-10,-40 Z' fill='#ef6c00' transform='rotate(135)'/>
          <path d='M0,-50 L10,-40 L0,-30 L-10,-40 Z' fill='#ef6c00' transform='rotate(180)'/>
          <path d='M0,-50 L10,-40 L0,-30 L-10,-40 Z' fill='#ef6c00' transform='rotate(225)'/>
          <path d='M0,-50 L10,-40 L0,-30 L-10,-40 Z' fill='#ef6c00' transform='rotate(270)'/>
          <path d='M0,-50 L10,-40 L0,-30 L-10,-40 Z' fill='#ef6c00' transform='rotate(315)'/>
        </g>
        <polygon points='200,50 220,150 300,150 235,200 260,300 200,240 140,300 165,200 100,150 180,150' fill='#4caf50' stroke='#388e3c' stroke-width='3'/>
      </svg>`
    },
    {
      name: 'Abstract Design',
      svg: `<svg width='400' height='400' viewBox='0 0 400 400' xmlns='http://www.w3.org/2000/svg'>
        <rect width='400' height='400' fill='#f5f5f5'/>
        <circle cx='200' cy='200' r='150' fill='none' stroke='#3f51b5' stroke-width='8'/>
        <circle cx='200' cy='200' r='100' fill='none' stroke='#e91e63' stroke-width='8'/>
        <circle cx='200' cy='200' r='50' fill='none' stroke='#ff9800' stroke-width='8'/>
        <line x1='50' y1='200' x2='350' y2='200' stroke='#4caf50' stroke-width='5'/>
        <line x1='200' y1='50' x2='200' y2='350' stroke='#4caf50' stroke-width='5'/>
      </svg>`
    },
    {
      name: 'Decorative Pattern',
      svg: `<svg width='400' height='400' viewBox='0 0 400 400' xmlns='http://www.w3.org/2000/svg'>
        <defs>
          <pattern id='grid' width='40' height='40' patternUnits='userSpaceOnUse'>
            <rect width='40' height='40' fill='#f9f9f9'/>
            <circle cx='20' cy='20' r='8' fill='#e0e0e0'/>
          </pattern>
        </defs>
        <rect width='400' height='400' fill='url(#grid)'/>
        <path d='M100,100 C150,50 250,50 300,100 S350,200 300,300 S150,350 100,300 S50,200 100,100 Z' fill='none' stroke='#9c27b0' stroke-width='5'/>
      </svg>`
    }
  ];

  const handleAddSVG = (svgString) => {
    try {
      // Validate that the string contains valid SVG
      if (!svgString.includes('<svg') || !svgString.includes('</svg>')) {
        setError('Invalid SVG content. Please provide valid SVG markup.');
        return;
      }

      // Create a new SVG element
      const newElement = {
        id: uuidv4(),
        type: 'svg',
        x: 50,
        y: 50,
        width: 200,
        height: 200,
        value: svgString,
        style: {},
        attributes: {}
      };

      // Add the element to the design space
      addElement('svg', '', newElement);
      setError('');
    } catch (err) {
      setError(`Error adding SVG: ${err.message}`);
    }
  };

  const handleCustomSVGSubmit = (e) => {
    e.preventDefault();
    handleAddSVG(svgContent);
  };

  return (
    <div className="p-4">
      <h3 className="text-lg font-semibold mb-4">SVG Elements</h3>

      {/* SVG Templates */}
      <div className="mb-6">
        <h4 className="text-md font-medium mb-2">SVG Templates</h4>
        <div className="grid grid-cols-2 gap-3">
          {svgTemplates.map((template, index) => (
            <div
              key={index}
              className="border border-gray-200 rounded-md p-2 cursor-pointer hover:bg-gray-50"
              onClick={() => handleAddSVG(template.svg)}
            >
              <div className="w-full h-24 flex items-center justify-center mb-2 bg-white rounded overflow-hidden">
                <div dangerouslySetInnerHTML={{ __html: template.svg.replace(/width='400'/g, 'width="100%"').replace(/height='400'/g, 'height="100%"') }} />
              </div>
              <p className="text-sm text-center">{template.name}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Custom SVG Input */}
      <div className="mb-4">
        <h4 className="text-md font-medium mb-2">Custom SVG</h4>
        <form onSubmit={handleCustomSVGSubmit}>
          <textarea
            className="w-full h-32 p-2 border border-gray-300 rounded-md mb-2"
            placeholder="Paste your SVG code here..."
            value={svgContent}
            onChange={(e) => setSvgContent(e.target.value)}
          />
          {error && <p className="text-red-500 text-sm mb-2">{error}</p>}
          <button
            type="submit"
            className="w-full bg-blue-600 text-white py-2 rounded-md hover:bg-blue-700"
          >
            Add Custom SVG
          </button>
        </form>
      </div>
    </div>
  );
};

export default SVGSettings;
