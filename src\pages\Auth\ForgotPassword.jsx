import React, { useRef, useState, useEffect } from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { Link } from 'react-router-dom';
import { Toast } from 'primereact/toast';
import { classNames } from 'primereact/utils';
import { InputText } from 'primereact/inputtext';
import { Button } from 'primereact/button';
import SideImage from './SideImage';
import { 
  useSendOtpForPasswordResetMutation, 
  useVerifyOtpForPasswordResetMutation,
  useResetPasswordWithTokenMutation 
} from '../../quires';
import { getFormErrorMessage } from '@utils/helper';

function ForgotPassword() {
  const { formState: { errors }, handleSubmit, control, reset, watch } = useForm();
  const toast = useRef(null);
  
  // Step management
  const [currentStep, setCurrentStep] = useState(1); // 1: Email, 2: OTP, 3: New Password
  const [userEmail, setUserEmail] = useState('');
  const [resetToken, setResetToken] = useState('');
  
  // OTP state
  const [otpValues, setOtpValues] = useState(['', '', '', '']);
  const [otpInputRefs, setOtpInputRefs] = useState([]);
  const [countdown, setCountdown] = useState(0);
  const [canResend, setCanResend] = useState(true);
  const [otpExpired, setOtpExpired] = useState(false);
  const [otpSentTime, setOtpSentTime] = useState(null);
  
  // Mutations
  const sendOtp = useSendOtpForPasswordResetMutation();
  const verifyOtp = useVerifyOtpForPasswordResetMutation();
  const resetPassword = useResetPasswordWithTokenMutation();

  // Countdown timer effect
  useEffect(() => {
    let timer;
    if (countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
    } else {
      setCanResend(true);
      // Check if OTP has expired (5 minutes = 300 seconds)
      if (otpSentTime && Date.now() - otpSentTime > 300000) {
        setOtpExpired(true);
        toast.current.show({
          severity: 'warn',
          summary: 'OTP Expired',
          detail: 'The verification code has expired. Please request a new one.',
          life: 5000
        });
      }
    }
    return () => clearTimeout(timer);
  }, [countdown, otpSentTime]);

  // Initialize OTP input refs
  useEffect(() => {
    setOtpInputRefs(Array(4).fill(0).map(() => React.createRef()));
  }, []);

  // Step 1: Send OTP
  const onSendOtp = async (data) => {
    try {
      await sendOtp.mutateAsync({ email: data.email });
      setUserEmail(data.email);
      setCurrentStep(2);
      setCountdown(300); // 5 minutes = 300 seconds
      setCanResend(false);
      setOtpExpired(false);
      setOtpSentTime(Date.now());
      reset();
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  // Handle OTP input change
  const handleOtpChange = (index, value) => {
    if (value.length <= 1 && /^\d*$/.test(value)) {
      const newOtpValues = [...otpValues];
      newOtpValues[index] = value;
      setOtpValues(newOtpValues);

      // Move to next input if value is entered
      if (value && index < 3) {
        otpInputRefs[index + 1]?.current?.focus();
      }
    }
  };

  // Handle OTP input keydown
  const handleOtpKeyDown = (index, e) => {
    if (e.key === 'Backspace' && !otpValues[index] && index > 0) {
      otpInputRefs[index - 1]?.current?.focus();
    }
  };

  // Resend OTP
  const handleResendOtp = async () => {
    try {
      await sendOtp.mutateAsync({ email: userEmail });
      setCountdown(300);
      setCanResend(false);
      setOtpExpired(false);
      setOtpSentTime(Date.now());
      setOtpValues(['', '', '', '']);
      toast.current.show({
        severity: 'success',
        summary: 'Success',
        detail: 'New verification code sent successfully',
        life: 3000
      });
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  // Step 2: Verify OTP
  const onVerifyOtp = async () => {
    const otpString = otpValues.join('');
    if (otpString.length !== 4) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Please enter the complete 4-digit code',
        life: 3000
      });
      return;
    }

    // Check if OTP has expired
    if (otpExpired || (otpSentTime && Date.now() - otpSentTime > 300000)) {
      setOtpExpired(true);
      toast.current.show({
        severity: 'error',
        summary: 'OTP Expired',
        detail: 'The verification code has expired. Please request a new one.',
        life: 5000
      });
      return;
    }

    try {
      const result = await verifyOtp.mutateAsync({ 
        email: userEmail, 
        otp: otpString 
      });
      setResetToken(result.reset_token);
      setCurrentStep(3);
      reset();
    } catch (error) {
      // Check if the error is due to expired OTP
      if (error?.response?.data?.message?.toLowerCase().includes('expired') || 
          error?.response?.data?.message?.toLowerCase().includes('invalid')) {
        setOtpExpired(true);
        toast.current.show({
          severity: 'error',
          summary: 'Invalid Code',
          detail: 'The verification code is invalid or has expired. Please request a new one.',
          life: 5000
        });
      }
      // Other errors are handled by the mutation
    }
  };

  // Step 3: Reset Password
  const onResetPassword = async (data) => {
    try {
      await resetPassword.mutateAsync({
        token: resetToken,
        password: data.password,
        password_confirmation: data.password_confirmation
      });
      // Success will navigate to login
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const goBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      reset();
      if (currentStep === 2) {
        setOtpValues(['', '', '', '']);
        setCountdown(0);
        setCanResend(true);
        setOtpExpired(false);
        setOtpSentTime(null);
      }
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const renderStep1 = () => (
    <div>
      <h1 className='text-3xl font-bold mb-4'>Forgot Password</h1>
      <p className='text-[#696F79] mb-8 text-sm leading-relaxed'>
        Enter your email address and we&apos;ll send you a verification code to reset your password.
      </p>
      
      <form onSubmit={handleSubmit(onSendOtp)} className="flex flex-col">
        <div className="mb-6 w-full">
          <div className="field">
            <label className="form-label mb-2 text-[#696F79]">Email Address</label>
            <span className="p-float-label mt-2">
              <Controller 
                name="email" 
                control={control}
                rules={{
                  required: 'Email is required',
                  pattern: {
                    value: /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
                    message: 'Please enter a valid email address',
                  }
                }}
                render={({ field, fieldState }) => (
                  <InputText
                    id={field.name}
                    {...field}
                    inputRef={field.ref}
                    placeholder="Enter your email address"
                    className={`w-full text-[#696F79] p-3 ${classNames({ 'p-invalid': fieldState.invalid })}`}
                    disabled={sendOtp.isLoading}
                  />
                )}
              />
            </span>
            {getFormErrorMessage('email', errors)}
          </div>
        </div>

        <button 
          type="submit"
          className="main-btn w-full mt-4 text-md sm:text-xl"
          disabled={sendOtp.isLoading}
        >
          {sendOtp.isLoading ? 'Sending...' : 'Send Verification Code'}
        </button>
      </form>
    </div>
  );

  const renderStep2 = () => (
    <div>
      <h1 className='text-3xl font-bold mb-4'>Enter Verification Code</h1>
      <p className='text-[#696F79] mb-8 text-sm leading-relaxed'>
        We&apos;ve sent a 4-digit verification code to <strong>{userEmail}</strong>. 
        Please enter it below to continue.
      </p>
      
      <div className="flex flex-col">
        {/* OTP Input Fields */}
        <div className="mb-6">
          <label className="form-label mb-4 text-[#696F79] block text-center">Verification Code</label>
          <div className="flex justify-center gap-3 mb-4">
            {otpValues.map((value, index) => (
              <div key={index} className="relative">
                <input
                  ref={otpInputRefs[index]}
                  type="text"
                  maxLength={1}
                  value={value}
                  onChange={(e) => handleOtpChange(index, e.target.value)}
                  onKeyDown={(e) => handleOtpKeyDown(index, e)}
                  className={`w-16 h-16 text-center text-2xl font-bold border-2 rounded-lg focus:outline-none transition-all duration-200 ${
                    otpExpired 
                      ? 'border-red-400 bg-red-50' 
                      : 'border-gray-300 focus:border-[#427bf0]'
                  }`}
                  disabled={verifyOtp.isLoading}
                  autoComplete="off"
                />
                {index < 3 && (
                  <div className="absolute top-1/2 right-[-12px] transform -translate-y-1/2 w-6 h-0.5 bg-gray-300"></div>
                )}
              </div>
            ))}
          </div>
          <p className="text-center text-sm text-[#696F79]">
            Enter the 4-digit code sent to your email
          </p>
          
          {/* OTP Expired Warning */}
          {otpExpired && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-600 text-sm text-center">
                ⚠️ The verification code has expired. Please request a new one.
              </p>
            </div>
          )}
        </div>

        {/* Countdown Timer */}
        {countdown > 0 && !otpExpired && (
          <div className="mb-4 text-center">
            <p className="text-sm text-[#696F79]">
              Resend code in <span className="font-bold text-[#427bf0]">{formatTime(countdown)}</span>
            </p>
          </div>
        )}

        {/* Resend Button */}
        <div className="mb-6 text-center">
          <button
            type="button"
            onClick={handleResendOtp}
            disabled={!canResend || sendOtp.isLoading}
            className={`text-sm ${
              canResend 
                ? 'text-[#427bf0] hover:text-[#2d5bb8] underline' 
                : 'text-gray-400 cursor-not-allowed'
            }`}
          >
            {sendOtp.isLoading ? 'Sending...' : 'Resend Code'}
          </button>
        </div>

        <div className="flex gap-3">
          <Button 
            type="button"
            onClick={goBack}
            className="flex-1 p-3 bg-gray-500 hover:bg-gray-600 text-white rounded-lg"
            disabled={verifyOtp.isLoading}
          >
            Back
          </Button>
          <button 
            type="button"
            onClick={onVerifyOtp}
            className="main-btn flex-1 text-md sm:text-xl"
            disabled={verifyOtp.isLoading || otpValues.join('').length !== 4 || otpExpired}
          >
            {verifyOtp.isLoading ? 'Verifying...' : 'Verify Code'}
          </button>
        </div>
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div>
      <h1 className='text-3xl font-bold mb-4'>Create New Password</h1>
      <p className='text-[#696F79] mb-8 text-sm leading-relaxed'>
        Please enter your new password. Make sure it&apos;s secure and easy to remember.
      </p>
      
      <form onSubmit={handleSubmit(onResetPassword)} className="flex flex-col">
        <div className="mb-6 w-full">
          <div className="field">
            <label className="form-label mb-2 text-[#696F79]">New Password</label>
            <span className="p-float-label mt-2">
              <Controller 
                name="password" 
                control={control}
                rules={{
                  required: 'Password is required',
                  minLength: {
                    value: 8,
                    message: 'Password must be at least 8 characters long'
                  }
                }}
                render={({ field, fieldState }) => (
                  <InputText
                    id={field.name}
                    {...field}
                    type="password"
                    inputRef={field.ref}
                    placeholder="Enter new password"
                    className={`w-full text-[#696F79] p-3 ${classNames({ 'p-invalid': fieldState.invalid })}`}
                    disabled={resetPassword.isLoading}
                  />
                )}
              />
            </span>
            {getFormErrorMessage('password', errors)}
          </div>
        </div>

        <div className="mb-6 w-full">
          <div className="field">
            <label className="form-label mb-2 text-[#696F79]">Confirm New Password</label>
            <span className="p-float-label mt-2">
              <Controller 
                name="password_confirmation" 
                control={control}
                rules={{
                  required: 'Password confirmation is required',
                  validate: (value) => {
                    const password = watch('password');
                    return value === password || 'Passwords do not match';
                  }
                }}
                render={({ field, fieldState }) => (
                  <InputText
                    id={field.name}
                    {...field}
                    type="password"
                    inputRef={field.ref}
                    placeholder="Confirm new password"
                    className={`w-full text-[#696F79] p-3 ${classNames({ 'p-invalid': fieldState.invalid })}`}
                    disabled={resetPassword.isLoading}
                  />
                )}
              />
            </span>
            {getFormErrorMessage('password_confirmation', errors)}
          </div>
        </div>

        <div className="flex gap-3">
          <Button 
            type="button"
            onClick={goBack}
            className="flex-1 p-3 bg-gray-500 hover:bg-gray-600 text-white rounded-lg"
            disabled={resetPassword.isLoading}
          >
            Back
          </Button>
          <button 
            type="submit"
            className="main-btn flex-1 text-md sm:text-xl"
            disabled={resetPassword.isLoading}
          >
            {resetPassword.isLoading ? 'Resetting...' : 'Reset Password'}
          </button>
        </div>
      </form>
    </div>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return renderStep1();
      case 2:
        return renderStep2();
      case 3:
        return renderStep3();
      default:
        return renderStep1();
    }
  };

  return (
    <div className='w-full h-[100vh] overflow-hidden flex'>
      <Toast ref={toast} />
      <SideImage />
      <div className='w-full sm:w-7/12 h-full px-6 md:px-12 flex flex-col justify-center'>
        <div className="max-w-md mx-auto w-full">
          {renderCurrentStep()}

          <div className="mt-6 text-center">
            <p className="text-[#696F79] text-sm">
              Remember your password?
              <Link to="/login">
                <span className="mx-1 capitalize text-[#427bf0] hover:underline">Back to Login</span>
              </Link>
            </p>
          </div>

          <div className="mt-4 text-center">
            <p className="text-[#696F79] text-sm">
              Don&apos;t have an account?
              <Link to="/register">
                <span className="mx-1 capitalize text-[#427bf0] hover:underline">Sign up</span>
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ForgotPassword;
