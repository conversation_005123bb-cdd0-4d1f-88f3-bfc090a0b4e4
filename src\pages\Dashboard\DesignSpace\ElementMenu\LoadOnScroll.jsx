import { useState, useEffect } from 'react';
import { useGetImagesMutation } from '@quires';
import { useDesignSpace } from "@contexts/DesignSpaceContext";
import { Dialog } from 'primereact/dialog';
import './ProfessionalImageGallery.css';
import axiosInstance from '../../../../config/Axios';

const LoadOnScroll = ({ fileType, refetch, setRefetch }) => {
    const { addElement, cardType } = useDesignSpace()
    const getUserImages = useGetImagesMutation()

    const [items, setItems] = useState([]);
    const [page, setPage] = useState(1);
    const [loading, setLoading] = useState(false);
    const [hasMore, setHasMore] = useState(true);

    // State لإدارة المودال
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [deleteTargetId, setDeleteTargetId] = useState(null);

    // Function to calculate appropriate image dimensions
    const calculateImageDimensions = (naturalWidth, naturalHeight) => {
        let finalWidth = naturalWidth;
        let finalHeight = naturalHeight;
        
        // Scale down large images to fit within design space
        if (cardType) {
            const maxWidth = cardType.width * 0.8; // 80% of design space width
            const maxHeight = cardType.height * 0.8; // 80% of design space height
            
            // Check if image is too large
            if (naturalWidth > maxWidth || naturalHeight > maxHeight) {
                // Calculate scale factor to fit within design space
                const scaleX = maxWidth / naturalWidth;
                const scaleY = maxHeight / naturalHeight;
                const scale = Math.min(scaleX, scaleY);
                
                finalWidth = Math.round(naturalWidth * scale);
                finalHeight = Math.round(naturalHeight * scale);
            }
        }
        
        return { width: finalWidth, height: finalHeight };
    };

    const fetchItems = async (page) => {
        setLoading(true);

        // Always use "image" as the file_type for the API call
        // We'll filter the results client-side based on the fileType prop
        // Log the fileType for debugging
        console.log(`Fetching images for fileType: ${fileType}`);

        await getUserImages.mutateAsync({
            user_id: localStorage.getItem("user_id"),
            file_type: fileType === "uploaded" ? "upload_image" : fileType,
        }, {
            onSuccess: (data) => {
                // دعم كلا الحالتين: مصفوفة مباشرة أو داخل data
                let responseArray = Array.isArray(data) ? data : (data.data ? data.data : []);
                console.log('API RESPONSE:', data);
                console.log('USING ARRAY:', responseArray);
                // Apply client-side filtering based on fileType
                let filteredData = [...responseArray];
                // Log a sample item to see its structure
                if (responseArray.length > 0) {
                    console.log('Sample image item:', responseArray[0]);
                }

                // Filter logic based on fileType
                if (fileType === 'profile') {
                    // For profile images, we'll use a more flexible approach
                    // Check various properties that might indicate a profile image
                    filteredData = responseArray.filter(item => {
                        // Check file path
                        const path = item.file_path ? item.file_path.toLowerCase() : '';

                        // Check file name if available
                        const name = item.file_name ? item.file_name.toLowerCase() : '';

                        // Check metadata if available
                        const metadata = item.metadata ? JSON.stringify(item.metadata).toLowerCase() : '';

                        // Check tags if available
                        const tags = item.tags ? item.tags.join(' ').toLowerCase() : '';

                        // Check if any of these contain "profile" or "avatar"
                        return path.includes('profile') || path.includes('avatar') ||
                               name.includes('profile') || name.includes('avatar') ||
                               metadata.includes('profile') || metadata.includes('avatar') ||
                               tags.includes('profile') || tags.includes('avatar');
                    });
                    console.log(`Filtered to ${filteredData.length} profile images`);
                } else if (fileType === 'uploaded') {
                    // اعرض فقط الصور التي file_type === 'upload_image'
                    filteredData = responseArray.filter(item => item.file_type === 'upload_image');
                    console.log(`Filtered to ${filteredData.length} uploaded images (upload_image)`);
                } else if (fileType === 'image') {
                    // اعرض فقط الصور التي file_type === 'image'
                    filteredData = responseArray.filter(item => item.file_type === 'image');
                    console.log(`Filtered to ${filteredData.length} images (image)`);
                } else {
                    console.log(`Using all ${filteredData.length} images (no filtering)`);
                }

                setItems(filteredData);
                setLoading(false);
                setRefetch(false);
            }
        })
    };

    useEffect(() => {
        if (refetch)
            fetchItems();
    }, [page, refetch]);

    const handleScroll = (e) => {
        const { scrollTop, scrollHeight, clientHeight } = e.target;
        if (scrollTop + clientHeight >= scrollHeight - 10 && hasMore && !loading) {
            setPage((prevPage) => prevPage + 1);
        }
    };

    // حذف صورة من المكتبة
    const handleDelete = async (id) => {
        setLoading(true);
        try {
            await axiosInstance.delete(`/file-uploads/${id}`);
            setRefetch(true);
        } catch (err) {
            alert('An error occurred while deleting!');
        } finally {
            setLoading(false);
            setShowDeleteModal(false);
            setDeleteTargetId(null);
        }
    };

    // عند الضغط على زر الحذف
    const onDeleteClick = (id) => {
        setDeleteTargetId(id);
        setShowDeleteModal(true);
    };

    // عند إلغاء الحذف
    const onCancelDelete = () => {
        setShowDeleteModal(false);
        setDeleteTargetId(null);
    };

    return (
        <>
            <div className='professional-image-gallery'
                style={{ maxHeight: fileType === "qr" ? "400px" : "300px" }}
                onScroll={handleScroll}
            >
                {items.length > 0 ? (
                    items.map((item, index) => (
                        <div key={index} style={{ marginBottom: '18px' }}>
                            <div className='professional-image-item'>
                                <div className="professional-image-container">
                                    <button
                                        className="delete-image-btn"
                                        onClick={() => onDeleteClick(item.id)}
                                        title="حذف الصورة"
                                    >
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M3 6h18M9 6V4a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v2m2 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            <path d="M10 11v6M14 11v6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                        </svg>
                                    </button>
                                    <img
                                        onClick={() => { 
                                            // Create a temporary image to get natural dimensions
                                            const tempImg = new Image();
                                            tempImg.onload = () => {
                                                const dimensions = calculateImageDimensions(tempImg.naturalWidth, tempImg.naturalHeight);
                                                addElement(fileType === "qr" ? "qr" : "img", item.file_path, dimensions);
                                            };
                                            tempImg.onerror = () => {
                                                // Fallback to default size if image fails to load
                                                addElement(fileType === "qr" ? "qr" : "img", item.file_path);
                                            };
                                            tempImg.src = item.file_path;
                                        }}
                                        loading="lazy"
                                        src={item.file_path}
                                        alt="Professional Image"
                                        className={`professional-image ${fileType === "qr" ? "qr-image" : 'gallery-image'}`}
                                    />
                                    <div className="professional-image-overlay">
                                        <div className="professional-image-actions">
                                            <button
                                                className="professional-action-btn"
                                                onClick={() => { 
                                                    // Create a temporary image to get natural dimensions
                                                    const tempImg = new Image();
                                                    tempImg.onload = () => {
                                                        const dimensions = calculateImageDimensions(tempImg.naturalWidth, tempImg.naturalHeight);
                                                        addElement(fileType === "qr" ? "qr" : "img", item.file_path, dimensions);
                                                    };
                                                    tempImg.onerror = () => {
                                                        // Fallback to default size if image fails to load
                                                        addElement(fileType === "qr" ? "qr" : "img", item.file_path);
                                                    };
                                                    tempImg.src = item.file_path;
                                                }}
                                                title="Add to Canvas"
                                            >
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M12 5V19M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {item.qr_url && (
                                <div className="qr-link-label text-xs text-gray-600 mt-2 break-all text-center px-1">
                                    {item.qr_url}
                                </div>
                            )}
                        </div>
                    ))
                ) : !loading ? (
                    <div className="professional-empty-state">
                        <div className="professional-empty-icon">🖼️</div>
                        <div className="professional-empty-title">No images found</div>
                        <div className="professional-empty-subtitle">
                            {fileType === 'profile' ? 'No profile images available' :
                             fileType === 'uploaded' ? 'No uploaded images available' :
                             'No images available'}
                        </div>
                    </div>
                ) : null}

                {loading && (
                    <div className='professional-loading-state'>
                        <div className="professional-loading-spinner"></div>
                        <span>Loading beautiful images...</span>
                    </div>
                )}
            </div>

            {showDeleteModal && (
                <Dialog visible={showDeleteModal} onHide={onCancelDelete} header="Delete Confirmation" modal style={{ width: '350px' }} footer={
                    <div className="flex justify-end gap-2">
                        <button className="main-btn" onClick={onCancelDelete}>Cancel</button>
                        <button className="main-btn bg-red-500 hover:bg-red-600 text-white" onClick={() => handleDelete(deleteTargetId)} disabled={loading}>
                            {loading ? 'Deleting...' : 'Delete'}
                        </button>
                    </div>
                }>
                    <div className="text-center text-gray-700 py-4">
                        Are you sure you want to delete this image from the library? This action cannot be undone.
                    </div>
                </Dialog>
            )}
        </>
    );
};

export default LoadOnScroll;
