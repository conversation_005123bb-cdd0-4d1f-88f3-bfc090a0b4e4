import React, { useState } from 'react';
import { useDesignSpace } from '@contexts/DesignSpaceContext';
import { Bi<PERSON>hart, BiBarChartAlt2, BiLine<PERSON>hart, BiPieChartAlt2, BiA<PERSON>, BiScatterChart, BiRadar } from 'react-icons/bi';
import { v4 as uuidv4 } from 'uuid';

// Chart categories and items
const chartCategories = [
  {
    id: 'bar-charts',
    name: 'Bar Charts',
    icon: <BiBarChartAlt2 size={20} />,
    items: [
      { id: 'vertical-bar', name: 'Vertical Bar', icon: <BiBarChartAlt2 size={32} />, type: 'chart', chartType: 'bar-vertical' },
      { id: 'horizontal-bar', name: 'Horizontal Bar', icon: <BiBarChartAlt2 size={32} style={{ transform: 'rotate(90deg)' }} />, type: 'chart', chartType: 'bar-horizontal' },
      { id: 'stacked-bar', name: 'Stacked Bar', icon: <BiBarChartAlt2 size={32} />, type: 'chart', chartType: 'bar-stacked' },
      { id: 'grouped-bar', name: 'Grouped Bar', icon: <BiBarChartAlt2 size={32} />, type: 'chart', chartType: 'bar-grouped' },
    ]
  },
  {
    id: 'line-charts',
    name: 'Line Charts',
    icon: <BiLineChart size={20} />,
    items: [
      { id: 'basic-line', name: 'Basic Line', icon: <BiLineChart size={32} />, type: 'chart', chartType: 'line-basic' },
      { id: 'multi-line', name: 'Multi Line', icon: <BiLineChart size={32} />, type: 'chart', chartType: 'line-multi' },
      { id: 'curved-line', name: 'Curved Line', icon: <BiLineChart size={32} />, type: 'chart', chartType: 'line-curved' },
      { id: 'stepped-line', name: 'Stepped Line', icon: <BiLineChart size={32} />, type: 'chart', chartType: 'line-stepped' },
    ]
  },
  {
    id: 'pie-charts',
    name: 'Pie Charts',
    icon: <BiPieChartAlt2 size={20} />,
    items: [
      { id: 'pie-chart', name: 'Pie Chart', icon: <BiPieChartAlt2 size={32} />, type: 'chart', chartType: 'pie' },
      { id: 'donut-chart', name: 'Donut Chart', icon: <BiPieChartAlt2 size={32} />, type: 'chart', chartType: 'donut' },
      { id: 'semi-pie', name: 'Semi Pie', icon: <BiPieChartAlt2 size={32} />, type: 'chart', chartType: 'semi-pie' },
      { id: 'nested-pie', name: 'Nested Pie', icon: <BiPieChartAlt2 size={32} />, type: 'chart', chartType: 'nested-pie' },
    ]
  },
  {
    id: 'area-charts',
    name: 'Area Charts',
    icon: <BiArea size={20} />,
    items: [
      { id: 'basic-area', name: 'Basic Area', icon: <BiArea size={32} />, type: 'chart', chartType: 'area-basic' },
      { id: 'stacked-area', name: 'Stacked Area', icon: <BiArea size={32} />, type: 'chart', chartType: 'area-stacked' },
      { id: 'percentage-area', name: 'Percentage Area', icon: <BiArea size={32} />, type: 'chart', chartType: 'area-percentage' },
      { id: 'gradient-area', name: 'Gradient Area', icon: <BiArea size={32} />, type: 'chart', chartType: 'area-gradient' },
    ]
  },
  {
    id: 'other-charts',
    name: 'Other Charts',
    icon: <BiChart size={20} />,
    items: [
      { id: 'scatter-plot', name: 'Scatter Plot', icon: <BiScatterChart size={32} />, type: 'chart', chartType: 'scatter' },
      { id: 'bubble-chart', name: 'Bubble Chart', icon: <BiScatterChart size={32} />, type: 'chart', chartType: 'bubble' },
      { id: 'radar-chart', name: 'Radar Chart', icon: <BiRadar size={32} />, type: 'chart', chartType: 'radar' },
      { id: 'polar-chart', name: 'Polar Chart', icon: <BiRadar size={32} />, type: 'chart', chartType: 'polar' },
    ]
  }
];

function ChartsSettings() {
  const { addElement } = useDesignSpace();
  const [activeCategory, setActiveCategory] = useState('bar-charts');
  const [searchTerm, setSearchTerm] = useState('');

  // Get the active category data
  const activeCategoryData = chartCategories.find(cat => cat.id === activeCategory);

  // Filter items based on search term
  const filteredItems = activeCategoryData?.items.filter(item =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle adding a chart to the canvas
  const handleAddChart = (item) => {
    // Create chart element data
    const chartData = {
      id: uuidv4(),
      type: 'chart',
      chartType: item.chartType,
      width: 300,
      height: 200,
      x: 100,
      y: 100,
      data: generateSampleData(item.chartType),
      options: getDefaultOptions(item.chartType),
      backgroundColor: 'transparent'
    };

    // Add the chart to the canvas
    addElement('chart', '', chartData);
  };

  // Generate sample data based on chart type
  const generateSampleData = (chartType) => {
    // This would be replaced with actual chart data generation logic
    if (chartType.includes('pie') || chartType.includes('donut')) {
      return {
        labels: ['Category 1', 'Category 2', 'Category 3', 'Category 4'],
        datasets: [
          {
            data: [30, 50, 20, 40],
            backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0']
          }
        ]
      };
    } else {
      return {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [
          {
            label: 'Dataset 1',
            data: [65, 59, 80, 81, 56, 55],
            backgroundColor: '#7c3aed',
            borderColor: '#7c3aed'
          }
        ]
      };
    }
  };

  // Get default options based on chart type
  const getDefaultOptions = (chartType) => {
    // This would be replaced with actual chart options configuration
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
        },
        title: {
          display: true,
          text: 'Chart Title'
        }
      }
    };
  };

  return (
    <div className="canva-charts">
      <h3 className="text-xl font-semibold mb-4 text-gray-800">Charts</h3>
      
      {/* Search */}
      <div className="mb-4">
        <div className="relative">
          <input
            type="text"
            placeholder="Search charts..."
            className="w-full p-3 pr-12 border border-gray-200 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>
      
      {/* Categories */}
      <div className="mb-6">
        <h4 className="text-md font-medium mb-4 text-gray-700">Categories</h4>
        <div className="grid grid-cols-5 gap-3 overflow-x-auto hide-scrollbar pb-2">
          {chartCategories.map(category => {
            // Define category colors
            let bgGradient = '';
            
            switch(category.id) {
              case 'bar-charts':
                bgGradient = 'from-blue-500 to-indigo-600';
                break;
              case 'line-charts':
                bgGradient = 'from-indigo-500 to-purple-600';
                break;
              case 'pie-charts':
                bgGradient = 'from-purple-500 to-pink-600';
                break;
              case 'area-charts':
                bgGradient = 'from-pink-500 to-rose-600';
                break;
              case 'other-charts':
                bgGradient = 'from-amber-400 to-orange-500';
                break;
              default:
                bgGradient = 'from-gray-400 to-gray-500';
            }
            
            return (
              <div 
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`relative overflow-hidden rounded-xl cursor-pointer transition-all duration-300 ${
                  activeCategory === category.id 
                    ? 'ring-2 ring-offset-2 ring-purple-500 scale-105 shadow-lg' 
                    : 'hover:shadow-md hover:scale-105'
                }`}
              >
                {/* Background gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${bgGradient} ${activeCategory === category.id ? 'opacity-100' : 'opacity-80'}`}></div>
                
                {/* Content */}
                <div className="relative p-3 flex flex-col items-center justify-center h-20">
                  <span className="text-2xl mb-1 text-white">{category.icon}</span>
                  <span className="text-xs font-medium text-white text-center truncate w-full">
                    {category.name}
                  </span>
                </div>
                
                {/* Active indicator */}
                {activeCategory === category.id && (
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-white"></div>
                )}
              </div>
            );
          })}
        </div>
      </div>
      
      {/* Charts Grid */}
      <div className="grid grid-cols-2 gap-3">
        {filteredItems?.map(item => (
          <div
            key={item.id}
            className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-3 aspect-square flex flex-col items-center justify-center cursor-pointer hover:shadow-sm transition-all duration-200 border border-gray-200"
            onClick={() => handleAddChart(item)}
          >
            <div className="text-purple-600 mb-2 text-2xl">{item.icon}</div>
            <span className="text-xs font-medium text-center text-gray-700">{item.name}</span>
          </div>
        ))}
        
        {filteredItems?.length === 0 && (
          <div className="col-span-2 py-10 text-center text-gray-500">
            <div className="text-3xl mb-2">🔍</div>
            <div className="font-medium">No charts found</div>
            <div className="text-xs mt-1">Try a different search term or category</div>
          </div>
        )}
      </div>
    </div>
  );
}

export default ChartsSettings;
