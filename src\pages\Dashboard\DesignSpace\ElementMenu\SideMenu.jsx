import { useState } from 'react';
import { useDesignSpace } from '@contexts/DesignSpaceContext';

// Icons
import { RxImage, RxText } from 'react-icons/rx';
import { BsQrCode, BsTable } from 'react-icons/bs';
import { TbIcons } from 'react-icons/tb';
import { MdOutlinePhotoLibrary, MdOutlineVideoLibrary, MdOutlineUpload } from 'react-icons/md';
import { IoShapesOutline, IoColorPaletteOutline } from 'react-icons/io5';
import { BiChart, BiShapeSquare } from 'react-icons/bi';
import { FaRegFileAlt } from 'react-icons/fa';
import { PiSlideshowLight } from 'react-icons/pi';

// Components
import TextSettings from './TextSettings';
import ImageSettings from './ImageSettings';
import IconsSettings from './IconsSettings';
import QrCodeSettings from './QrCodeSettings';
import ElementsSettings from './ElementsSettings';
import BackgroundSettings from './BackgroundSettings';
import ChartsSettings from './ChartsSettings';
import TablesSettings from './TablesSettings';
import LoadOnScroll from './LoadOnScroll';

// Templates Data
import {
  socialMediaTemplates,
  presentationTemplates,
  printTemplates,
  businessCardTemplates,
  idBadgeTemplates,
  allTemplates
} from '@constants/TemplatesData';

// Additional Templates
import {
  professionalBusinessCards,
  professionalIdBadges
} from '@constants/TemplatesData.additional';
import {
  creativeBusinessCards
} from '@constants/TemplatesData.more';
import {
  corporateIdBadges,
  eventBadges
} from '@constants/TemplatesData.badges';
import {
  instagramTemplates,
  facebookTemplates
} from '@constants/TemplatesData.social';
import {
  instagramStoryTemplates,
  twitterPostTemplates,
  linkedinPostTemplates
} from '@constants/TemplatesData.social.more';
import {
  businessPresentationTemplates
} from '@constants/TemplatesData.presentation';
// Print and photo templates not needed anymore as we're using LoadOnScroll
import {
  videoThumbnailTemplates,
  videoIntroTemplates
} from '@constants/TemplatesData.videos';
import {
  modernBusinessCards,
  luxuryBusinessCards
} from '@constants/TemplatesData.business';


const TemplatesSettings = () => {
    const { setElements, cardType } = useDesignSpace();
    const [activeCategory, setActiveCategory] = useState('all');
    const [searchTerm, setSearchTerm] = useState('');

    // Filter templates based on category, search term, and card type
    const filteredTemplates = () => {
        let templates = [];

        switch(activeCategory) {
            case 'business':
                templates = [...businessCardTemplates, ...professionalBusinessCards, ...creativeBusinessCards, ...modernBusinessCards, ...luxuryBusinessCards];
                break;
            case 'badge':
                templates = [...idBadgeTemplates, ...professionalIdBadges, ...corporateIdBadges, ...eventBadges];
                break;
            case 'social':
                templates = [...socialMediaTemplates, ...instagramTemplates, ...facebookTemplates, ...instagramStoryTemplates, ...twitterPostTemplates, ...linkedinPostTemplates];
                break;
            case 'presentation':
                templates = [...presentationTemplates, ...businessPresentationTemplates];
                break;

            default:
                templates = allTemplates;
        }

        // Filter by search term if provided
        if (searchTerm) {
            templates = templates.filter(template =>
                template.name.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        // Filter by card type dimensions if available
        if (cardType) {
            // Allow some flexibility in dimensions (within 10% of the card dimensions)
            const widthTolerance = cardType.width * 0.1;
            const heightTolerance = cardType.height * 0.1;

            templates = templates.filter(template => {
                // Check if template dimensions are within tolerance of card dimensions
                const widthMatch = Math.abs(template.width - cardType.width) <= widthTolerance;
                const heightMatch = Math.abs(template.height - cardType.height) <= heightTolerance;

                // Also check for aspect ratio match (for templates that can be scaled)
                const cardRatio = cardType.width / cardType.height;
                const templateRatio = template.width / template.height;
                const ratioMatch = Math.abs(cardRatio - templateRatio) <= 0.1; // 10% tolerance for aspect ratio

                return widthMatch && heightMatch || ratioMatch;
            });
        }

        return templates;
    };

    // Apply template
    const applyTemplate = (template) => {
        // Scale elements to match card dimensions if needed
        let scaledElements = [...template.elements];

        if (cardType && (template.width !== cardType.width || template.height !== cardType.height)) {
            const scaleX = cardType.width / template.width;
            const scaleY = cardType.height / template.height;

            // Scale all element positions and dimensions
            scaledElements = scaledElements.map(el => ({
                ...el,
                x: Math.round(el.x * scaleX),
                y: Math.round(el.y * scaleY),
                width: Math.round(el.width * scaleX),
                height: Math.round(el.height * scaleY)
            }));
        }

        // Set elements with scaled positions and dimensions
        setElements(scaledElements);
    };

    return (
        <div className="canva-templates">
            <h3 className="text-xl font-semibold mb-4 text-gray-800">Templates</h3>

            {/* Search */}
            <div className="mb-4">
                <div className="relative">
                    <input
                        type="text"
                        placeholder="Search templates..."
                        className="w-full p-3 pr-12 border border-gray-200 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                </div>
            </div>

            {/* Categories */}
            <div className="mb-6">
                <h4 className="text-md font-medium mb-4 text-gray-700">Categories</h4>
                <div className="grid grid-cols-3 gap-3 overflow-x-auto hide-scrollbar pb-2">
                    {[
                        { id: 'all', name: 'All Templates', bgGradient: 'from-blue-500 to-indigo-600', icon: '📄' },
                        { id: 'business', name: 'Business Cards', bgGradient: 'from-indigo-500 to-purple-600', icon: '💼' },
                        { id: 'badge', name: 'ID Badges', bgGradient: 'from-purple-500 to-pink-600', icon: '🪪' },
                        { id: 'social', name: 'Social Media', bgGradient: 'from-pink-500 to-rose-600', icon: '📱' },
                        { id: 'presentation', name: 'Presentations', bgGradient: 'from-amber-400 to-orange-500', icon: '📊' }
                    ].map(category => (
                        <div
                            key={category.id}
                            onClick={() => setActiveCategory(category.id)}
                            className={`relative overflow-hidden rounded-xl cursor-pointer transition-all duration-300 ${
                                activeCategory === category.id
                                ? 'ring-2 ring-offset-2 ring-purple-500 scale-105 shadow-lg'
                                : 'hover:shadow-md hover:scale-105'
                            }`}
                        >
                            {/* Background gradient */}
                            <div className={`absolute inset-0 bg-gradient-to-br ${category.bgGradient} ${activeCategory === category.id ? 'opacity-100' : 'opacity-80'}`}></div>

                            {/* Content */}
                            <div className="relative p-3 flex flex-col items-center justify-center h-20">
                                <span className="text-2xl mb-1 text-white">{category.icon}</span>
                                <span className="text-xs font-medium text-white text-center truncate w-full">
                                    {category.name}
                                </span>
                            </div>

                            {/* Active indicator */}
                            {activeCategory === category.id && (
                                <div className="absolute bottom-0 left-0 right-0 h-1 bg-white"></div>
                            )}
                        </div>
                    ))}
                </div>
            </div>

            {/* Templates Grid */}
            <div className="grid grid-cols-2 gap-3">
                {filteredTemplates().map(template => (
                    <div
                        key={template.id}
                        className="rounded-lg overflow-hidden shadow-sm cursor-pointer hover:shadow-md transition-all duration-200 hover:scale-105 border border-gray-200"
                        onClick={() => applyTemplate(template)}
                    >
                        <div className="aspect-video bg-gray-100 relative group">
                            {/* Template thumbnail */}
                            <img
                                src={template.thumbnail}
                                alt={template.name}
                                className="w-full h-full object-cover"
                            />

                            {/* Preview overlay - appears on hover */}
                            <div className="absolute inset-0 bg-gradient-to-br from-black/90 to-black/80 backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-all duration-300 flex flex-col items-center justify-center p-3">
                                <div className="w-full flex flex-col items-center">
                                    {/* Preview label with animation */}
                                    <div className="flex items-center mb-2 bg-white/10 px-3 py-1 rounded-full">
                                        <span className="animate-pulse mr-1.5 h-2 w-2 rounded-full bg-green-400"></span>
                                        <p className="text-white text-xs font-medium tracking-wider">LIVE PREVIEW</p>
                                    </div>

                                    {/* Template preview with shadow and reflection */}
                                    <div className="w-full overflow-hidden rounded-lg shadow-[0_10px_20px_rgba(0,0,0,0.3)] bg-white/5 backdrop-blur-md p-2 transform perspective-[800px] rotate-x-[5deg] group-hover:rotate-x-0 transition-transform duration-500">
                                        {/* Template container with proper scaling */}
                                        <div className="relative w-full bg-white rounded-md overflow-hidden"
                                            style={{
                                                // Fixed height for better visibility with zoom effect
                                                height: '180px',
                                                boxShadow: 'inset 0 0 30px rgba(0,0,0,0.1)',
                                                transform: 'scale(1.2)',
                                                transformOrigin: 'center center'
                                            }}>
                                            {/* Scaled content container */}
                                            <div className="absolute inset-0">
                                                {/* Elements rendering with improved styling */}
                                                {template.elements.map((el, idx) => {
                                                    // Calculate scaled positions for better visibility
                                                    const scale = Math.min(180 / template.height, 300 / template.width) * 0.8; // 0.8 factor to account for the 1.2 scale of parent
                                                    const xScaled = (el.x * scale);
                                                    const yScaled = (el.y * scale);
                                                    const widthScaled = (el.width * scale);
                                                    const heightScaled = (el.height * scale);

                                                    if (el.type === 'shape') {
                                                        return (
                                                            <div
                                                                key={idx}
                                                                style={{
                                                                    position: 'absolute',
                                                                    left: `${xScaled}px`,
                                                                    top: `${yScaled}px`,
                                                                    width: `${widthScaled}px`,
                                                                    height: `${heightScaled}px`,
                                                                    backgroundColor: el.backgroundColor,
                                                                    borderRadius: el.shapeType === 'circle' ? '50%' : (el.borderRadius ? `${el.borderRadius}px` : '0'),
                                                                    opacity: el.opacity || 1,
                                                                    boxShadow: el.shadow ? '0 4px 6px rgba(0,0,0,0.1)' : 'none',
                                                                    background: el.style?.background || el.backgroundColor,
                                                                    border: el.borderWidth ? `${el.borderWidth}px solid ${el.borderColor || '#000'}` : 'none',
                                                                }}
                                                            ></div>
                                                        );
                                                    } else if (el.type === 'text') {
                                                        // Calculate scaled font size
                                                        const fontSizeScaled = el.fontSize * scale * 0.8; // 0.8 factor to make text more readable

                                                        return (
                                                            <div
                                                                key={idx}
                                                                style={{
                                                                    position: 'absolute',
                                                                    left: `${xScaled}px`,
                                                                    top: `${yScaled}px`,
                                                                    width: `${widthScaled}px`,
                                                                    height: `${heightScaled}px`,
                                                                    color: el.color,
                                                                    fontSize: `${fontSizeScaled}px`,
                                                                    fontWeight: el.fontWeight,
                                                                    textAlign: el.textAlign,
                                                                    letterSpacing: el.letterSpacing,
                                                                    overflow: 'hidden',
                                                                    display: 'flex',
                                                                    alignItems: 'center',
                                                                    justifyContent: el.textAlign === 'center' ? 'center' :
                                                                                  el.textAlign === 'right' ? 'flex-end' : 'flex-start',
                                                                    textShadow: el.textShadow || 'none',
                                                                }}
                                                            >
                                                                <span style={{
                                                                    overflow: 'hidden',
                                                                    textOverflow: 'ellipsis',
                                                                    whiteSpace: 'nowrap',
                                                                    maxWidth: '100%',
                                                                }}>
                                                                    {el.value}
                                                                </span>
                                                            </div>
                                                        );
                                                    }
                                                    return null;
                                                })}
                                            </div>
                                        </div>

                                        {/* Reflection effect */}
                                        <div className="absolute bottom-0 left-0 right-0 h-1/3 bg-gradient-to-b from-white/10 to-transparent opacity-50 transform scale-y-[-1] blur-sm"></div>
                                    </div>
                                </div>

                                {/* Use template button with animation */}
                                <button className="mt-4 bg-gradient-to-r from-purple-600 to-indigo-600 text-white text-xs py-1.5 px-4 rounded-full hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-purple-500/30 transform hover:scale-105 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
                                    </svg>
                                    Use Template
                                </button>
                            </div>

                            {/* Template info */}
                            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent h-12"></div>
                            <div className="absolute bottom-2 left-2 right-2">
                                <p className="text-xs font-medium truncate text-white">{template.name}</p>
                                <p className="text-xs text-gray-300">{template.width}×{template.height}</p>
                            </div>
                        </div>
                    </div>
                ))}

                {filteredTemplates().length === 0 && (
                    <div className="col-span-2 py-10 text-center text-gray-500">
                        <div className="text-3xl mb-2">🔍</div>
                        <div className="font-medium">No templates found</div>
                        <div className="text-xs mt-1">Try a different search term or category</div>
                    </div>
                )}
            </div>
        </div>
    );
};



const PhotosSettings = () => {
    const [activeCategory, setActiveCategory] = useState('all');
    const [refetch, setRefetch] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');

    return (
        <div className="canva-photos">
            <h3 className="text-xl font-semibold mb-4 text-gray-800">Images</h3>

            {/* Search */}
            <div className="mb-4">
                <div className="relative">
                    <input
                        type="text"
                        placeholder="Search images..."
                        className="w-full p-3 pr-12 border border-gray-200 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                </div>
            </div>

            {/* Categories */}
            <div className="mb-6">
                <h4 className="text-md font-medium mb-4 text-gray-700">Categories</h4>
                <div className="grid grid-cols-3 gap-3 overflow-x-auto hide-scrollbar pb-2">
                    {[
                        { id: 'all', name: 'All Photos', bgGradient: 'from-blue-500 to-indigo-600', icon: '🖼️' },
                        { id: 'profile', name: 'Profile Images', bgGradient: 'from-indigo-500 to-purple-600', icon: '👥' },
                        { id: 'uploaded', name: 'Uploaded Images', bgGradient: 'from-purple-500 to-pink-600', icon: '📤' },
                    ].map(category => (
                        <div
                            key={category.id}
                            onClick={() => {
                                setActiveCategory(category.id);
                                // Trigger a refetch when changing categories
                                setRefetch(true);
                            }}
                            className={`relative overflow-hidden rounded-xl cursor-pointer transition-all duration-300 ${
                                activeCategory === category.id
                                ? 'ring-2 ring-offset-2 ring-purple-500 scale-105 shadow-lg'
                                : 'hover:shadow-md hover:scale-105'
                            }`}
                        >
                            {/* Background gradient */}
                            <div className={`absolute inset-0 bg-gradient-to-br ${category.bgGradient} ${activeCategory === category.id ? 'opacity-100' : 'opacity-80'}`}></div>

                            {/* Content */}
                            <div className="relative p-3 flex flex-col items-center justify-center h-20">
                                <span className="text-2xl mb-1 text-white">{category.icon}</span>
                                <span className="text-xs font-medium text-white text-center truncate w-full">
                                    {category.name}
                                </span>
                            </div>

                            {/* Active indicator */}
                            {activeCategory === category.id && (
                                <div className="absolute bottom-0 left-0 right-0 h-1 bg-white"></div>
                            )}
                        </div>
                    ))}
                </div>
            </div>

            {/* Image Gallery based on selected category */}
            <div className="flex flex-col justify-start">
                <h4 className="font-bold mb-2 text-[#676666]">Image Library</h4>

                {activeCategory === 'all' && (
                    <LoadOnScroll fileType="image" refetch={refetch} setRefetch={setRefetch} />
                )}

                {activeCategory === 'profile' && (
                    <LoadOnScroll fileType="profile" refetch={refetch} setRefetch={setRefetch} />
                )}

                {activeCategory === 'uploaded' && (
                    <LoadOnScroll fileType="uploaded" refetch={refetch} setRefetch={setRefetch} />
                )}

                {/* No results message */}
                {/* This will be handled by LoadOnScroll component */}
            </div>
        </div>
    );
};

const VideosSettings = () => {
    const { setElements } = useDesignSpace();
    const [activeCategory, setActiveCategory] = useState('all');
    const [searchTerm, setSearchTerm] = useState('');

    // Filter templates based on category and search term
    const filteredTemplates = () => {
        let templates = [];

        switch(activeCategory) {
            case 'thumbnails':
                templates = videoThumbnailTemplates;
                break;
            case 'intros':
                templates = videoIntroTemplates;
                break;
            default:
                templates = [...videoThumbnailTemplates, ...videoIntroTemplates];
        }

        // Filter by search term if provided
        if (searchTerm) {
            templates = templates.filter(template =>
                template.name.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        return templates;
    };

    // Apply template
    const applyTemplate = (template) => {
        setElements(template.elements);
    };

    return (
        <div className="canva-videos">
            <h3 className="text-xl font-semibold mb-4 text-gray-800">Video Templates</h3>

            {/* Search */}
            <div className="mb-4">
                <div className="relative">
                    <input
                        type="text"
                        placeholder="Search video templates..."
                        className="w-full p-3 pr-12 border border-gray-200 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                </div>
            </div>

            {/* Categories */}
            <div className="mb-6">
                <h4 className="text-md font-medium mb-4 text-gray-700">Categories</h4>
                <div className="grid grid-cols-3 gap-3 overflow-x-auto hide-scrollbar pb-2">
                    {[
                        { id: 'all', name: 'All Videos', bgGradient: 'from-red-500 to-orange-600', icon: '🎬' },
                        { id: 'thumbnails', name: 'Thumbnails', bgGradient: 'from-orange-500 to-amber-600', icon: '👁️' },
                        { id: 'intros', name: 'Video Intros', bgGradient: 'from-amber-500 to-yellow-600', icon: '🎥' },
                    ].map(category => (
                        <div
                            key={category.id}
                            onClick={() => setActiveCategory(category.id)}
                            className={`relative overflow-hidden rounded-xl cursor-pointer transition-all duration-300 ${
                                activeCategory === category.id
                                ? 'ring-2 ring-offset-2 ring-purple-500 scale-105 shadow-lg'
                                : 'hover:shadow-md hover:scale-105'
                            }`}
                        >
                            {/* Background gradient */}
                            <div className={`absolute inset-0 bg-gradient-to-br ${category.bgGradient} ${activeCategory === category.id ? 'opacity-100' : 'opacity-80'}`}></div>

                            {/* Content */}
                            <div className="relative p-3 flex flex-col items-center justify-center h-20">
                                <span className="text-2xl mb-1 text-white">{category.icon}</span>
                                <span className="text-xs font-medium text-white text-center truncate w-full">
                                    {category.name}
                                </span>
                            </div>

                            {/* Active indicator */}
                            {activeCategory === category.id && (
                                <div className="absolute bottom-0 left-0 right-0 h-1 bg-white"></div>
                            )}
                        </div>
                    ))}
                </div>
            </div>

            {/* Templates Grid */}
            <div className="grid grid-cols-2 gap-3">
                {filteredTemplates().map(template => (
                    <div
                        key={template.id}
                        className="rounded-lg overflow-hidden shadow-sm cursor-pointer hover:shadow-md transition-all duration-200 hover:scale-105 border border-gray-200"
                        onClick={() => applyTemplate(template)}
                    >
                        <div className="aspect-video bg-gray-100 relative group">
                            <img
                                src={template.thumbnail}
                                alt={template.name}
                                className="w-full h-full object-cover"
                            />
                            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent h-12"></div>
                            <div className="absolute bottom-2 left-2 right-2">
                                <p className="text-xs font-medium truncate text-white">{template.name}</p>
                            </div>
                        </div>
                    </div>
                ))}

                {filteredTemplates().length === 0 && (
                    <div className="col-span-2 py-10 text-center text-gray-500">
                        <div className="text-3xl mb-2">🔍</div>
                        <div className="font-medium">No video templates found</div>
                        <div className="text-xs mt-1">Try a different search term or category</div>
                    </div>
                )}
            </div>
        </div>
    );
};

const UploadsSettings = () => {
    const [activeCategory, setActiveCategory] = useState('all');
    const [searchTerm, setSearchTerm] = useState('');
    const [refetch, setRefetch] = useState(true);

    return (
        <div className="canva-uploads">
            <h3 className="text-xl font-semibold mb-4 text-gray-800">Uploaded Images</h3>

            {/* Upload Button */}
            <div className="mb-4">
                <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg p-4 flex flex-col items-center justify-center hover:bg-gray-50 transition-colors">
                    <MdOutlineUpload size={32} className="mb-2 text-purple-600" />
                    <p className="text-center text-gray-600">Drag and drop files here or click to upload</p>
                    <button className="mt-3 bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-5 py-2 rounded-lg hover:from-purple-700 hover:to-indigo-700 shadow-md transition-all duration-300">
                        Upload
                    </button>
                </div>
            </div>

            {/* Search */}
            <div className="mb-4">
                <div className="relative">
                    <input
                        type="text"
                        placeholder="Search uploaded images..."
                        className="w-full p-3 pr-12 border border-gray-200 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                </div>
            </div>

            {/* Categories */}
            <div className="mb-6">
                <h4 className="text-md font-medium mb-4 text-gray-700">Categories</h4>
                <div className="grid grid-cols-3 gap-3 overflow-x-auto hide-scrollbar pb-2">
                    {[
                        { id: 'all', name: 'All Uploads', bgGradient: 'from-emerald-500 to-teal-600', icon: '📤' },
                        { id: 'images', name: 'Image Uploads', bgGradient: 'from-teal-500 to-cyan-600', icon: '🖼️' },
                        { id: 'documents', name: 'Document Uploads', bgGradient: 'from-cyan-500 to-sky-600', icon: '📄' },
                    ].map(category => (
                        <div
                            key={category.id}
                            onClick={() => {
                                setActiveCategory(category.id);
                                // Trigger a refetch when changing categories
                                setRefetch(true);
                            }}
                            className={`relative overflow-hidden rounded-xl cursor-pointer transition-all duration-300 ${
                                activeCategory === category.id
                                ? 'ring-2 ring-offset-2 ring-purple-500 scale-105 shadow-lg'
                                : 'hover:shadow-md hover:scale-105'
                            }`}
                        >
                            {/* Background gradient */}
                            <div className={`absolute inset-0 bg-gradient-to-br ${category.bgGradient} ${activeCategory === category.id ? 'opacity-100' : 'opacity-80'}`}></div>

                            {/* Content */}
                            <div className="relative p-3 flex flex-col items-center justify-center h-20">
                                <span className="text-2xl mb-1 text-white">{category.icon}</span>
                                <span className="text-xs font-medium text-white text-center truncate w-full">
                                    {category.name}
                                </span>
                            </div>

                            {/* Active indicator */}
                            {activeCategory === category.id && (
                                <div className="absolute bottom-0 left-0 right-0 h-1 bg-white"></div>
                            )}
                        </div>
                    ))}
                </div>
            </div>

            {/* Image Gallery based on selected category */}
            <div className="flex flex-col justify-start">
                <h4 className="font-bold mb-2 text-[#676666]">Uploaded Images</h4>

                {activeCategory === 'all' && (
                    <LoadOnScroll fileType="uploaded" refetch={refetch} setRefetch={setRefetch} />
                )}

                {activeCategory === 'images' && (
                    <LoadOnScroll fileType="uploaded" refetch={refetch} setRefetch={setRefetch} />
                )}

                {activeCategory === 'documents' && (
                    <LoadOnScroll fileType="uploaded" refetch={refetch} setRefetch={setRefetch} />
                )}
            </div>
        </div>
    );
};





function SideMenu() {
    const [activeIndex, setActiveIndex] = useState(0);

    const items = [
        { label: 'Templates', icon: <FaRegFileAlt />, command: () => {}, component: <TemplatesSettings /> },
        { label: 'Elements', icon: <IoShapesOutline />, command: () => {}, component: <ElementsSettings /> },
        { label: 'Text', icon: <RxText />, command: () => {}, component: <TextSettings /> },
        { label: 'Photos', icon: <MdOutlinePhotoLibrary />, command: () => {}, component: <PhotosSettings /> },
        { label: 'Videos', icon: <MdOutlineVideoLibrary />, command: () => {}, component: <VideosSettings /> },
        { label: 'Backgrounds', icon: <IoColorPaletteOutline />, command: () => {}, component: <BackgroundSettings /> },
        { label: 'Uploads', icon: <MdOutlineUpload />, command: () => {}, component: <UploadsSettings /> },
        { label: 'Images', icon: <RxImage />, command: () => {}, component: <ImageSettings /> },
        { label: 'Icons', icon: <TbIcons />, command: () => {}, component: <IconsSettings /> },
        { label: 'Charts', icon: <BiChart />, command: () => {}, component: <ChartsSettings /> },
        { label: 'Tables', icon: <BsTable />, command: () => {}, component: <TablesSettings /> },
        { label: 'QR', icon: <BsQrCode />, command: () => {}, component: <QrCodeSettings /> },
    ];

    return (
        <div className="canva-side-menu h-full flex flex-col">
            <div className="canva-side-menu-header border-b border-gray-200 p-2">
                <h2 className="text-lg font-medium">Design Elements</h2>
            </div>
            <div className="canva-side-menu-tabs overflow-x-auto flex">
                {items.map((item, index) => (
                    <div
                        key={index}
                        className={`p-3 flex flex-col items-center justify-center cursor-pointer min-w-[80px] ${activeIndex === index ? 'border-b-2 border-purple-600 text-purple-600' : 'text-gray-600 hover:bg-gray-100'}`}
                        onClick={() => setActiveIndex(index)}
                    >
                        <div className="text-xl mb-1">{item.icon}</div>
                        <div className="text-xs">{item.label}</div>
                    </div>
                ))}
            </div>
            <div className="canva-side-menu-content flex-grow overflow-y-auto p-3">
                {items[activeIndex].component}
            </div>
        </div>
    );
}

export default SideMenu;