import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useDesignSpace } from '@contexts/DesignSpaceContext';
import { fieldsOptions } from '@constants/DesignSpaceConfig';

// Default templates in case imports fail
const DEFAULT_TEMPLATES = [
  {
    id: 'default-template-1',
    name: 'Business Card Template',
    width: 350,
    height: 200,
    thumbnail: 'https://placehold.co/350x200/4338ca/ffffff?text=Business+Card',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 350,
        height: 200,
        backgroundColor: '#4338ca',
      },
      {
        id: 'el_2',
        type: 'text',
        x: 30,
        y: 50,
        width: 290,
        height: 40,
        value: 'YOUR NAME',
        fontSize: 24,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'left',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 30,
        y: 90,
        width: 290,
        height: 20,
        value: 'Your Position',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#e0e0e0',
        textAlign: 'left',
      },
    ]
  },
  {
    id: 'default-template-2',
    name: 'Social Media Post',
    width: 1080,
    height: 1080,
    thumbnail: 'https://placehold.co/1080x1080/4338ca/ffffff?text=Social+Media',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1080,
        height: 1080,
        backgroundColor: '#4338ca',
      },
      {
        id: 'el_2',
        type: 'text',
        x: 100,
        y: 400,
        width: 880,
        height: 200,
        value: 'YOUR CONTENT HERE',
        fontSize: 60,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
    ]
  }
];

const TemplatesSettings = () => {
    const { setElements, cardType } = useDesignSpace();
    const [activeCategory, setActiveCategory] = useState('all');
    const [searchTerm, setSearchTerm] = useState('');

    // State for templates
    const [templates, setTemplates] = useState(DEFAULT_TEMPLATES);

    // Try to load templates from modules
    useEffect(() => {
        try {
            // Dynamically import templates
            const loadTemplates = async () => {
                try {
                    // Try to import the main templates file
                    const mainTemplates = await import('@constants/TemplatesData');

                    // If successful, set the templates
                    if (mainTemplates && mainTemplates.allTemplates) {
                        // بدل ترتيب name و type فقط عند التوزيع
                        const swappedFieldsOptions = [...fieldsOptions];
                        const nameIdx = swappedFieldsOptions.findIndex(f => f.value === 'name');
                        const typeIdx = swappedFieldsOptions.findIndex(f => f.value === 'type');
                        if (nameIdx !== -1 && typeIdx !== -1) {
                            const temp = swappedFieldsOptions[nameIdx];
                            swappedFieldsOptions[nameIdx] = swappedFieldsOptions[typeIdx];
                            swappedFieldsOptions[typeIdx] = temp;
                        }
                        const replacedTemplates = mainTemplates.allTemplates.map(template => {
                            let textFieldIndex = 0;
                            const newElements = template.elements.map(el => {
                                if (el.type === 'text') {
                                    const field = swappedFieldsOptions[textFieldIndex % swappedFieldsOptions.length];
                                    textFieldIndex++;
                                    return {
                                        ...el,
                                        value: field.value
                                    };
                                }
                                return el;
                            });
                            return {
                                ...template,
                                elements: newElements
                            };
                        });
                        setTemplates(replacedTemplates);
                    }
                } catch (error) {
                    console.error('Error loading templates:', error);
                    // Keep using default templates
                }
            };

            loadTemplates();
        } catch (error) {
            console.error('Error in templates loading effect:', error);
        }
    }, []);

    // Filter templates based on category, search term, and card type - memoized to prevent re-renders
    const filteredTemplates = useMemo(() => {
        try {
            let filtered = [...templates];

            // Filter by category
            if (activeCategory !== 'all') {
                filtered = filtered.filter(template =>
                    template.category === activeCategory ||
                    template.name.toLowerCase().includes(activeCategory.toLowerCase())
                );
            }

            // Filter by search term if provided
            if (searchTerm) {
                filtered = filtered.filter(template =>
                    template.name.toLowerCase().includes(searchTerm.toLowerCase())
                );
            }

            // Filter by card type dimensions if available
            if (cardType && cardType.width && cardType.height) {
                // Allow some flexibility in dimensions (within 10% of the card dimensions)
                const widthTolerance = cardType.width * 0.1;
                const heightTolerance = cardType.height * 0.1;

                filtered = filtered.filter(template => {
                    if (!template.width || !template.height) return true;

                    // Check if template dimensions are within tolerance of card dimensions
                    const widthMatch = Math.abs(template.width - cardType.width) <= widthTolerance;
                    const heightMatch = Math.abs(template.height - cardType.height) <= heightTolerance;

                    // Also check for aspect ratio match (for templates that can be scaled)
                    const cardRatio = cardType.width / cardType.height;
                    const templateRatio = template.width / template.height;
                    const ratioMatch = Math.abs(cardRatio - templateRatio) <= 0.1; // 10% tolerance for aspect ratio

                    return widthMatch && heightMatch || ratioMatch;
                });
            }

            // Limit to 10 templates to prevent performance issues
            return filtered.slice(0, 10);
        } catch (error) {
            console.error('Error filtering templates:', error);
            return DEFAULT_TEMPLATES.slice(0, 10);
        }
    }, [templates, activeCategory, searchTerm, cardType]); // Dependencies that should trigger re-filtering

    // Apply template - memoized to prevent re-renders
    const applyTemplate = useCallback((template) => {
        try {
            if (!template || !template.elements || !Array.isArray(template.elements)) {
                console.error('Invalid template or missing elements');
                return;
            }

            // Scale elements to match card dimensions if needed
            let scaledElements = [...template.elements];

            if (cardType && cardType.width && cardType.height &&
                template.width && template.height &&
                (template.width !== cardType.width || template.height !== cardType.height)) {

                const scaleX = cardType.width / template.width;
                const scaleY = cardType.height / template.height;

                // Scale all element positions and dimensions
                scaledElements = scaledElements.map(el => {
                    try {
                        return {
                            ...el,
                            x: Math.round((el.x || 0) * scaleX),
                            y: Math.round((el.y || 0) * scaleY),
                            width: Math.round((el.width || 100) * scaleX),
                            height: Math.round((el.height || 100) * scaleY)
                        };
                    } catch (error) {
                        console.error('Error scaling element:', error);
                        return el;
                    }
                });
            }

            // Set elements with scaled positions and dimensions
            setElements(scaledElements);

            // Show success message
            console.log('Template applied successfully');
        } catch (error) {
            console.error('Error applying template:', error);
        }
    }, [cardType, setElements]); // Only re-create when cardType or setElements changes

    return (
        <div className="canva-templates">
            <div className="flex items-center justify-between mb-5">
                <h3 className="text-xl font-bold text-gray-800 flex items-center">
                    <span className="text-purple-600 mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
                        </svg>
                    </span>
                    Professional Templates
                </h3>
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                    {filteredTemplates.length} templates
                </span>
            </div>

            {/* Search - Professional Design */}
            <div className="mb-5">
                <div className="relative">
                    <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-purple-500">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                    <input
                        type="text"
                        placeholder="Search professional templates..."
                        className="w-full p-3 pl-10 pr-10 border border-gray-200 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm bg-white"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    {searchTerm && (
                        <button
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                            onClick={() => setSearchTerm('')}
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    )}
                </div>
            </div>

            {/* Categories - Professional Design */}
            <div className="mb-6">
                <h4 className="text-sm font-medium mb-3 text-gray-700 flex items-center">
                    <span className="mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                    </span>
                    Categories
                </h4>
                <div className="grid grid-cols-2 gap-3">
                    {[
                        { id: 'all', name: 'All Templates', icon: '🏆' },
                        { id: 'business', name: 'Business Cards', icon: '💼' },
                        { id: 'badge', name: 'ID Badges', icon: '🪪' },
                        { id: 'social', name: 'Social Media', icon: '📱' },
                        { id: 'presentation', name: 'Presentations', icon: '📊' }
                    ].map(category => (
                        <button
                            key={category.id}
                            onClick={() => setActiveCategory(category.id)}
                            className={`px-3 py-2.5 text-sm rounded-lg flex items-center justify-center transition-all duration-200 ${
                                activeCategory === category.id
                                ? 'bg-gradient-to-r from-purple-600 to-indigo-600 text-white shadow-md transform scale-105'
                                : 'bg-white text-gray-700 border border-gray-200 hover:border-purple-300 hover:shadow-sm'
                            }`}
                        >
                            <span className="mr-2">{category.icon}</span>
                            {category.name}
                        </button>
                    ))}
                </div>
            </div>

            {/* Templates Grid - Professional Design */}
            <div className="grid grid-cols-2 gap-3">
                {filteredTemplates.map(template => (
                    <div
                        key={template.id}
                        className="rounded-lg overflow-hidden shadow-sm cursor-pointer hover:shadow-lg transition-all duration-300 border border-gray-200 group transform hover:scale-105"
                        onClick={() => applyTemplate(template)}
                    >
                        <div className="aspect-video bg-gray-100 relative">
                            <img
                                src={template.thumbnail}
                                alt={template.name}
                                className="w-full h-full object-cover"
                                loading="lazy"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent h-12"></div>
                            <div className="absolute bottom-2 left-2 right-2">
                                <p className="text-sm font-medium truncate text-white">{template.name}</p>
                                <p className="text-xs text-gray-300 truncate">{template.width} × {template.height}px</p>
                            </div>
                            <div className="absolute top-2 right-2 bg-purple-600 text-white text-xs px-2 py-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                Use
                            </div>
                        </div>
                        <div className="p-2 bg-white">
                            {template.elements.filter(el => el.type === 'text').map((el, idx) => (
                                <div key={el.id || idx} className="text-xs text-gray-700 truncate">
                                    {fieldsOptions.find(f => f.value === el.value)?.label || el.value}
                                </div>
                            ))}
                        </div>
                    </div>
                ))}

                {filteredTemplates.length === 0 && (
                    <div className="col-span-2 py-8 text-center text-gray-500 bg-gray-50 rounded-lg border border-gray-200">
                        <div className="text-3xl mb-3">🔍</div>
                        <div className="font-medium text-base">No templates found</div>
                        <div className="text-sm mt-2 text-gray-400">Try a different search term or category</div>
                        <button
                            className="mt-4 px-4 py-2 bg-purple-600 text-white rounded-lg text-sm hover:bg-purple-700 transition-colors"
                            onClick={() => {
                                setActiveCategory('all');
                                setSearchTerm('');
                            }}
                        >
                            View All Templates
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
};

export default TemplatesSettings;
