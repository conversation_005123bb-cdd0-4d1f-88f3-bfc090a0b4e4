import { useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { fetchUser } from '@quires';


export function AdminMiddleware({ children }) {
    const token = localStorage.getItem("token")

    if (!token) {
        return <Navigate replace to="/login" />;

    }


    // const { data } = fetchUser( localStorage.getItem("user_id"));

    // if (data?.userType !== "admin") {
    //     return <Navigate replace to="/login" />;
    // }

    return children;

}
