// More professional templates for DesignSpace
// This file contains additional high-quality templates

// Creative Business Card Templates
export const creativeBusinessCards = [
  {
    id: 'cbc-001',
    name: 'Creative Agency Card',
    width: 350,
    height: 200,
    thumbnail: 'https://placehold.co/350x200/ff5722/ffffff?text=Creative+Agency',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 350,
        height: 200,
        backgroundColor: '#ff5722',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'circle',
        x: 25,
        y: 25,
        width: 50,
        height: 50,
        backgroundColor: '#ffffff',
        opacity: 0.2,
      },
      {
        id: 'el_3',
        type: 'shape',
        shapeType: 'circle',
        x: 275,
        y: 125,
        width: 100,
        height: 100,
        backgroundColor: '#ffffff',
        opacity: 0.1,
      },
      {
        id: 'el_4',
        type: 'text',
        x: 30,
        y: 70,
        width: 290,
        height: 30,
        value: 'EMMA CREATIVE',
        fontSize: 22,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'left',
      },
      {
        id: 'el_5',
        type: 'text',
        x: 30,
        y: 100,
        width: 290,
        height: 20,
        value: 'Art Director',
        fontSize: 16,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'left',
      },
      {
        id: 'el_6',
        type: 'text',
        x: 30,
        y: 130,
        width: 290,
        height: 20,
        value: '<EMAIL>',
        fontSize: 12,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'left',
      },
    ]
  },
  {
    id: 'cbc-002',
    name: 'Photographer Card',
    width: 350,
    height: 200,
    thumbnail: 'https://placehold.co/350x200/111111/ffffff?text=Photographer',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 350,
        height: 200,
        backgroundColor: '#111111',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 20,
        y: 20,
        width: 310,
        height: 160,
        backgroundColor: 'transparent',
        borderColor: '#ffffff',
        borderWidth: 1,
      },
      {
        id: 'el_3',
        type: 'text',
        x: 40,
        y: 60,
        width: 270,
        height: 30,
        value: 'DAVID WILLIAMS',
        fontSize: 20,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 40,
        y: 90,
        width: 270,
        height: 20,
        value: 'PHOTOGRAPHER',
        fontSize: 12,
        fontWeight: 'normal',
        color: '#ffffff',
        letterSpacing: '3px',
        textAlign: 'center',
      },
      {
        id: 'el_5',
        type: 'shape',
        shapeType: 'rectangle',
        x: 125,
        y: 120,
        width: 100,
        height: 1,
        backgroundColor: '#ffffff',
      },
      {
        id: 'el_6',
        type: 'text',
        x: 40,
        y: 130,
        width: 270,
        height: 20,
        value: 'www.davidwilliamsphotography.com',
        fontSize: 10,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'center',
      },
    ]
  },
  {
    id: 'cbc-003',
    name: 'Architect Card',
    width: 350,
    height: 200,
    thumbnail: 'https://placehold.co/350x200/f5f5f5/333333?text=Architect',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 350,
        height: 200,
        backgroundColor: '#f5f5f5',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 20,
        height: 200,
        backgroundColor: '#333333',
      },
      {
        id: 'el_3',
        type: 'shape',
        shapeType: 'rectangle',
        x: 30,
        y: 30,
        width: 290,
        height: 1,
        backgroundColor: '#333333',
      },
      {
        id: 'el_4',
        type: 'shape',
        shapeType: 'rectangle',
        x: 30,
        y: 170,
        width: 290,
        height: 1,
        backgroundColor: '#333333',
      },
      {
        id: 'el_5',
        type: 'text',
        x: 40,
        y: 60,
        width: 270,
        height: 30,
        value: 'SOPHIA ANDERSON',
        fontSize: 18,
        fontWeight: 'bold',
        color: '#333333',
        textAlign: 'left',
      },
      {
        id: 'el_6',
        type: 'text',
        x: 40,
        y: 90,
        width: 270,
        height: 20,
        value: 'ARCHITECT',
        fontSize: 12,
        fontWeight: 'normal',
        color: '#666666',
        letterSpacing: '2px',
        textAlign: 'left',
      },
      {
        id: 'el_7',
        type: 'text',
        x: 40,
        y: 130,
        width: 270,
        height: 20,
        value: '<EMAIL>',
        fontSize: 10,
        fontWeight: 'normal',
        color: '#666666',
        textAlign: 'left',
      },
    ]
  },
  {
    id: 'cbc-004',
    name: 'Colorful Designer Card',
    width: 350,
    height: 200,
    thumbnail: 'https://placehold.co/350x200/8b5cf6/ffffff?text=Designer',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 350,
        height: 200,
        backgroundColor: '#8b5cf6',
        style: { background: 'linear-gradient(45deg, #8b5cf6, #ec4899)' },
      },
      {
        id: 'el_2',
        type: 'text',
        x: 30,
        y: 70,
        width: 290,
        height: 30,
        value: 'MARCUS DESIGN',
        fontSize: 24,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 30,
        y: 100,
        width: 290,
        height: 20,
        value: 'UI/UX Designer',
        fontSize: 16,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 30,
        y: 130,
        width: 290,
        height: 20,
        value: '<EMAIL> | 555-123-4567',
        fontSize: 12,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'center',
      },
    ]
  },
  {
    id: 'cbc-005',
    name: 'Geometric Card',
    width: 350,
    height: 200,
    thumbnail: 'https://placehold.co/350x200/ffffff/333333?text=Geometric',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 350,
        height: 200,
        backgroundColor: '#ffffff',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 175,
        height: 200,
        backgroundColor: '#3b82f6',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 190,
        y: 60,
        width: 150,
        height: 30,
        value: 'OLIVIA',
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333333',
        textAlign: 'left',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 190,
        y: 85,
        width: 150,
        height: 30,
        value: 'PARKER',
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333333',
        textAlign: 'left',
      },
      {
        id: 'el_5',
        type: 'text',
        x: 190,
        y: 120,
        width: 150,
        height: 20,
        value: 'Graphic Designer',
        fontSize: 12,
        fontWeight: 'normal',
        color: '#666666',
        textAlign: 'left',
      },
      {
        id: 'el_6',
        type: 'text',
        x: 190,
        y: 140,
        width: 150,
        height: 20,
        value: '<EMAIL>',
        fontSize: 10,
        fontWeight: 'normal',
        color: '#666666',
        textAlign: 'left',
      },
      {
        id: 'el_7',
        type: 'text',
        x: 20,
        y: 90,
        width: 135,
        height: 20,
        value: 'CREATIVE',
        fontSize: 16,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
    ]
  },
];

// Export all additional templates
export const moreTemplates = [
  ...creativeBusinessCards
];
