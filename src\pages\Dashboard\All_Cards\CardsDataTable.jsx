import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useFetchCards } from '@quires/card';
import Container from '@components/Container';
import { DataTable } from 'primereact/datatable';
import { Tooltip } from 'primereact/tooltip';
import { Column } from 'primereact/column';
import { FaRegEye } from 'react-icons/fa';
import { FiEdit } from 'react-icons/fi';
import { TfiTrash } from "react-icons/tfi";

function CardsDataTable() {
  const { data: cards, isLoading, isError } = useFetchCards();

  const actionBodyTemplate = (rowData) => {
    return (
      <div className="d-flex justify-center items-center w-full ms-auto">
        {/* preview */}
        <Tooltip target={`.view-button-${rowData.id}`} showDelay={100} className="fs-8" />
        <Link to={`/cards/${rowData.id}`}>
          <button
            className={`btn btn-sm btn-icon view-button-${rowData.id} me-4`}
            data-pr-position="bottom"
            data-pr-tooltip="View Card"
          >
            <FaRegEye size={20} />
          </button>
        </Link>

        {/* Edit */}
        <Tooltip target={`.edit-button-${rowData.id}`} showDelay={100} className="fs-8" />
        <button
          className={`btn btn-sm btn-icon edit-button-${rowData.id} me-3`}
          data-pr-position="bottom"
          data-pr-tooltip="Edit Card"
        >
          <FiEdit size={20} />
        </button>

        {/* Delete */}
        <Tooltip target={`.delete-button-${rowData.id}`} showDelay={100} className="fs-8" />
        <button
          className={`btn btn-sm btn-icon delete-button-${rowData.id}`}
          data-pr-position="bottom"
          data-pr-tooltip="Delete Card"
        >
          <TfiTrash size={20} />
        </button>
      </div>
    );
  };

  return (
    <Container>
      <div className="w-full flex justify-center">
        <div className='w-4/12'>
          <h1 className='text-xl font-bold '>Cards List</h1>
        </div>
        <div className='flex justify-end w-8/12'>
          <Link to="/cards/create">
            <button className="main-btn text-md shadow-md">Create New Card</button>
          </Link>
        </div>
      </div>

      {/* Cards Data Table */}
      <div className="w-full mt-8 ">
        <div className='table-responsive text-nowrap'>
          <DataTable
            value={cards}
            loading={isLoading}
            emptyMessage={isError ? "Error loading cards" : "No cards available"}
            paginator
            rows={5}
            rowsPerPageOptions={[5, 10, 25, 50]}
            dataKey="id"

            lazy
            filterDisplay="row"
            responsiveLayout="stack"
            breakpoint="960px"


            paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
            currentPageReportTemplate="Showing {first} to {last} of {totalRecords} products"

            scrollable
            scrollHeight="calc(100vh - 400px)"
          >
            <Column field="name" header="Card Name" filter sortable />
            <Column field="number" header="Card Number" filter sortable />
            <Column body={actionBodyTemplate} exportable={false} style={{ minWidth: '8rem' }} />
          </DataTable>
        </div>
      </div>
    </Container>
  );
}

export default CardsDataTable;
