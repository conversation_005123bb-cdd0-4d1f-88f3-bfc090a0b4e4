// Print Templates for DesignSpace
// This file contains professional print templates

// Flyer Templates
export const flyerTemplates = [
  {
    id: 'fl-001',
    name: 'Modern Event Flyer',
    width: 800,
    height: 1200,
    thumbnail: 'https://placehold.co/800x1200/6d28d9/ffffff?text=Event+Flyer',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 800,
        height: 1200,
        backgroundColor: '#6d28d9',
        style: { background: 'linear-gradient(135deg, #6d28d9, #9333ea)' },
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 40,
        y: 40,
        width: 720,
        height: 1120,
        backgroundColor: 'transparent',
        borderColor: '#ffffff',
        borderWidth: 2,
      },
      {
        id: 'el_3',
        type: 'text',
        x: 100,
        y: 150,
        width: 600,
        height: 100,
        value: 'SUMMER',
        fontSize: 80,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 100,
        y: 250,
        width: 600,
        height: 100,
        value: 'MUSIC FESTIVAL',
        fontSize: 60,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_5',
        type: 'shape',
        shapeType: 'rectangle',
        x: 250,
        y: 380,
        width: 300,
        height: 300,
        backgroundColor: '#f0f0f0',
      },
      {
        id: 'el_6',
        type: 'text',
        x: 100,
        y: 720,
        width: 600,
        height: 60,
        value: 'FEATURING',
        fontSize: 40,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_7',
        type: 'text',
        x: 100,
        y: 780,
        width: 600,
        height: 150,
        value: 'ARTIST NAME\nARTIST NAME\nARTIST NAME',
        fontSize: 30,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_8',
        type: 'text',
        x: 100,
        y: 980,
        width: 600,
        height: 60,
        value: 'JUNE 15-17, 2023',
        fontSize: 36,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_9',
        type: 'text',
        x: 100,
        y: 1040,
        width: 600,
        height: 40,
        value: 'CENTRAL PARK, NEW YORK',
        fontSize: 24,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'center',
      },
    ]
  },
  {
    id: 'fl-002',
    name: 'Business Conference Flyer',
    width: 800,
    height: 1200,
    thumbnail: 'https://placehold.co/800x1200/0f172a/ffffff?text=Business+Conference',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 800,
        height: 1200,
        backgroundColor: '#0f172a',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 800,
        height: 300,
        backgroundColor: '#1e3a8a',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 50,
        y: 100,
        width: 700,
        height: 100,
        value: 'BUSINESS LEADERSHIP',
        fontSize: 60,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 50,
        y: 200,
        width: 700,
        height: 60,
        value: 'CONFERENCE 2023',
        fontSize: 40,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_5',
        type: 'shape',
        shapeType: 'rectangle',
        x: 100,
        y: 350,
        width: 600,
        height: 400,
        backgroundColor: '#f0f0f0',
      },
      {
        id: 'el_6',
        type: 'text',
        x: 50,
        y: 800,
        width: 700,
        height: 60,
        value: 'KEYNOTE SPEAKERS',
        fontSize: 36,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_7',
        type: 'text',
        x: 50,
        y: 860,
        width: 700,
        height: 120,
        value: 'John Smith, CEO\nJane Doe, Marketing Director\nRobert Johnson, Industry Expert',
        fontSize: 24,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_8',
        type: 'text',
        x: 50,
        y: 1000,
        width: 700,
        height: 40,
        value: 'SEPTEMBER 20-22, 2023',
        fontSize: 30,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_9',
        type: 'text',
        x: 50,
        y: 1050,
        width: 700,
        height: 40,
        value: 'GRAND HOTEL CONFERENCE CENTER',
        fontSize: 24,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_10',
        type: 'text',
        x: 50,
        y: 1100,
        width: 700,
        height: 40,
        value: 'Register at: www.businessconference2023.com',
        fontSize: 20,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'center',
      },
    ]
  },
  {
    id: 'fl-003',
    name: 'Product Promotion Flyer',
    width: 800,
    height: 1200,
    thumbnail: 'https://placehold.co/800x1200/dc2626/ffffff?text=Product+Promotion',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 800,
        height: 1200,
        backgroundColor: '#ffffff',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 800,
        height: 200,
        backgroundColor: '#dc2626',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 50,
        y: 70,
        width: 700,
        height: 60,
        value: 'NEW PRODUCT LAUNCH',
        fontSize: 50,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_4',
        type: 'shape',
        shapeType: 'rectangle',
        x: 150,
        y: 250,
        width: 500,
        height: 500,
        backgroundColor: '#f0f0f0',
      },
      {
        id: 'el_5',
        type: 'text',
        x: 50,
        y: 800,
        width: 700,
        height: 80,
        value: 'PRODUCT NAME',
        fontSize: 60,
        fontWeight: 'bold',
        color: '#dc2626',
        textAlign: 'center',
      },
      {
        id: 'el_6',
        type: 'text',
        x: 50,
        y: 880,
        width: 700,
        height: 120,
        value: 'Introducing our revolutionary new product! Premium quality, innovative design, and exceptional performance. Limited time special offer available now.',
        fontSize: 24,
        fontWeight: 'normal',
        color: '#333333',
        textAlign: 'center',
      },
      {
        id: 'el_7',
        type: 'shape',
        shapeType: 'rectangle',
        x: 250,
        y: 1030,
        width: 300,
        height: 60,
        backgroundColor: '#dc2626',
        borderRadius: 30,
      },
      {
        id: 'el_8',
        type: 'text',
        x: 250,
        y: 1050,
        width: 300,
        height: 20,
        value: 'ORDER NOW',
        fontSize: 24,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_9',
        type: 'text',
        x: 50,
        y: 1120,
        width: 700,
        height: 30,
        value: 'www.yourproductwebsite.com',
        fontSize: 20,
        fontWeight: 'normal',
        color: '#666666',
        textAlign: 'center',
      },
    ]
  }
];

// Brochure Templates
export const brochureTemplates = [
  {
    id: 'br-001',
    name: 'Company Brochure',
    width: 1100,
    height: 850,
    thumbnail: 'https://placehold.co/1100x850/3b82f6/ffffff?text=Company+Brochure',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1100,
        height: 850,
        backgroundColor: '#ffffff',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 350,
        height: 850,
        backgroundColor: '#3b82f6',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 50,
        y: 100,
        width: 250,
        height: 100,
        value: 'COMPANY NAME',
        fontSize: 40,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 50,
        y: 200,
        width: 250,
        height: 50,
        value: 'ESTABLISHED 2010',
        fontSize: 20,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_5',
        type: 'shape',
        shapeType: 'rectangle',
        x: 50,
        y: 300,
        width: 250,
        height: 250,
        backgroundColor: '#f0f0f0',
      },
      {
        id: 'el_6',
        type: 'text',
        x: 50,
        y: 600,
        width: 250,
        height: 200,
        value: 'Contact Us:\n\<EMAIL>\n+1 (555) 123-4567\n\n123 Business Street\nNew York, NY 10001',
        fontSize: 16,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_7',
        type: 'text',
        x: 400,
        y: 100,
        width: 650,
        height: 80,
        value: 'ABOUT OUR COMPANY',
        fontSize: 50,
        fontWeight: 'bold',
        color: '#3b82f6',
        textAlign: 'left',
      },
      {
        id: 'el_8',
        type: 'text',
        x: 400,
        y: 200,
        width: 650,
        height: 200,
        value: 'We are a leading company in our industry with over 10 years of experience. Our dedicated team of professionals is committed to delivering exceptional service and innovative solutions to meet all your business needs.',
        fontSize: 18,
        fontWeight: 'normal',
        color: '#333333',
        textAlign: 'left',
      },
      {
        id: 'el_9',
        type: 'text',
        x: 400,
        y: 450,
        width: 650,
        height: 60,
        value: 'OUR SERVICES',
        fontSize: 30,
        fontWeight: 'bold',
        color: '#3b82f6',
        textAlign: 'left',
      },
      {
        id: 'el_10',
        type: 'text',
        x: 400,
        y: 520,
        width: 650,
        height: 200,
        value: '• Professional Consulting\n• Strategic Planning\n• Market Analysis\n• Business Development\n• Financial Services',
        fontSize: 18,
        fontWeight: 'normal',
        color: '#333333',
        textAlign: 'left',
      },
    ]
  }
];

// Export all print templates
export const allPrintTemplates = [
  ...flyerTemplates,
  ...brochureTemplates
];
