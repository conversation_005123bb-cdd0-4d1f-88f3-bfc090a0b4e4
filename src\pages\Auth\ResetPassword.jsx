import React, { useRef, useEffect, useState } from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { Toast } from 'primereact/toast';
import { classNames } from 'primereact/utils';
import { Password } from 'primereact/password';
import SideImage from './SideImage';
import { useResetPasswordMutation } from '../../quires';
import { getFormErrorMessage } from '@utils/helper';

function ResetPassword() {
  const { t } = useTranslation("auth");
  const { formState: { errors }, handleSubmit, control, watch } = useForm();
  const resetPassword = useResetPasswordMutation();
  const toast = useRef(null);
  const navigate = useNavigate();
  const { token } = useParams();
  const [isValidToken, setIsValidToken] = useState(true);

  // Watch password field for confirmation validation
  const password = watch('password');

  useEffect(() => {
    // Check if token exists in URL
    if (!token) {
      setIsValidToken(false);
      toast.current?.show({
        severity: 'error',
        summary: 'Invalid Link',
        detail: 'This password reset link is invalid or has expired.',
        life: 5000
      });
    }
  }, [token]);

  const onSubmit = async (data) => {
    if (!token) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Invalid reset token. Please request a new password reset.',
        life: 5000
      });
      return;
    }

    try {
      await resetPassword.mutateAsync({
        token: token,
        data: {
          password: data.password,
          password_confirmation: data.password_confirmation
        }
      });
      
      toast.current.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Your password has been reset successfully. Redirecting to login...',
        life: 3000
      });
      
      // The mutation already handles navigation to login
    } catch (error) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: error.response?.data?.message || 'Failed to reset password. Please try again.',
        life: 5000
      });
    }
  };

  if (!isValidToken) {
    return (
      <div className='w-full h-[100vh] overflow-hidden flex'>
        <Toast ref={toast} />
        <SideImage />
        <div className='w-full sm:w-7/12 h-full px-6 md:px-12 flex flex-col justify-center'>
          <div className="max-w-md mx-auto w-full text-center">
            <h1 className='text-3xl font-bold mb-4 text-red-600'>Invalid Link</h1>
            <p className='text-[#696F79] mb-8 text-sm leading-relaxed'>
              This password reset link is invalid or has expired. Please request a new password reset.
            </p>
            <Link to="/forgot-password">
              <button className="main-btn w-full text-md sm:text-xl">
                Request New Reset Link
              </button>
            </Link>
            <div className="mt-6">
              <Link to="/login">
                <span className="text-[#427bf0] hover:underline">Back to Login</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='w-full h-[100vh] overflow-hidden flex'>
      <Toast ref={toast} />
      <SideImage />
      <div className='w-full sm:w-7/12 h-full px-6 md:px-12 flex flex-col justify-center'>
        <div className="max-w-md mx-auto w-full">
          <h1 className='text-3xl font-bold mb-4'>Reset Password</h1>
          <p className='text-[#696F79] mb-8 text-sm leading-relaxed'>
            Enter your new password below. Make sure it's strong and secure.
          </p>
          
          <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col">
            {/* New Password Field */}
            <div className="mb-4 w-full">
              <div className="field">
                <label className="form-label mb-2 text-[#696F79]">New Password</label>
                <span className="p-float-label mt-2">
                  <Controller 
                    name="password" 
                    control={control}
                    rules={{
                      required: 'Password is required',
                      minLength: {
                        value: 8,
                        message: 'Password must be at least 8 characters long'
                      }
                    }}
                    render={({ field, fieldState }) => (
                      <Password
                        id={field.name}
                        {...field}
                        inputRef={field.ref}
                        placeholder="Enter your new password"
                        className={`text-[#696F79] pass-input w-full ${classNames({ 'p-invalid': fieldState.invalid })}`}
                        toggleMask
                        feedback={true}
                        disabled={resetPassword.isLoading}
                      />
                    )}
                  />
                </span>
                {getFormErrorMessage('password', errors)}
              </div>
            </div>

            {/* Confirm Password Field */}
            <div className="mb-6 w-full">
              <div className="field">
                <label className="form-label mb-2 text-[#696F79]">Confirm New Password</label>
                <span className="p-float-label mt-2">
                  <Controller 
                    name="password_confirmation" 
                    control={control}
                    rules={{
                      required: 'Please confirm your password',
                      validate: value => value === password || 'Passwords do not match'
                    }}
                    render={({ field, fieldState }) => (
                      <Password
                        id={field.name}
                        {...field}
                        inputRef={field.ref}
                        placeholder="Confirm your new password"
                        className={`text-[#696F79] pass-input w-full ${classNames({ 'p-invalid': fieldState.invalid })}`}
                        toggleMask
                        feedback={false}
                        disabled={resetPassword.isLoading}
                      />
                    )}
                  />
                </span>
                {getFormErrorMessage('password_confirmation', errors)}
              </div>
            </div>

            <button 
              type="submit"
              className="main-btn w-full mt-4 text-md sm:text-xl"
              disabled={resetPassword.isLoading}
            >
              {resetPassword.isLoading ? 'Resetting...' : 'Reset Password'}
            </button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-[#696F79] text-sm">
              Remember your password?
              <Link to="/login">
                <span className="mx-1 capitalize text-[#427bf0] hover:underline">Back to Login</span>
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ResetPassword;
