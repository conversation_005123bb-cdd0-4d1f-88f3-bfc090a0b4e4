import { useEffect, useState, useMemo } from "react";
import { Dropdown } from "primereact/dropdown";

import { useDataTableContext } from '@contexts/DataTableContext';
import { useQueryClient } from "react-query";
import axiosInstance from '../../../../config/Axios';

const GroupFilter = ({ groupData }) => {
    const { setLazyParams } = useDataTableContext();
    const queryClient = useQueryClient();
    const cachedDesigns = queryClient.getQueryData("getDesigns");
    const [designId, setDesignId] = useState();
    const [designs, setDesigns] = useState([]);
    const [loading, setLoading] = useState(false);

    // Fetch designs if not available in cache
    useEffect(() => {
        const fetchDesigns = async () => {
            if (!cachedDesigns || !Array.isArray(cachedDesigns) || cachedDesigns.length === 0) {
                setLoading(true);
                try {
                    const response = await axiosInstance.get('/designs');
                    if (response.data && Array.isArray(response.data.data)) {
                        setDesigns(response.data.data);
                        // Also update the query cache for future use
                        queryClient.setQueryData("getDesigns", response.data.data);
                    }
                } catch (error) {
                    console.error('Error fetching designs:', error);
                    setDesigns([]);
                } finally {
                    setLoading(false);
                }
            } else {
                setDesigns(cachedDesigns);
            }
        };

        fetchDesigns();
    }, [cachedDesigns, queryClient]);

    // Filter designs based on the group's card type
    const filteredDesigns = useMemo(() => {
        const designsToFilter = designs.length > 0 ? designs : (cachedDesigns || []);

        if (!designsToFilter || !Array.isArray(designsToFilter)) {
            return [];
        }

        // If no group data or no card type, return all designs (instead of an empty list or Error/Crash)
        if (!groupData || !groupData.data || !groupData.data.card_type_id) {
            return designsToFilter;
        }

        // Get the group's card type
        const groupCardTypeId = groupData.data.card_type_id;

        // Filter designs to only include those that match the group's card type
        return designsToFilter.filter(design => {
            // If the design has a card_type_id property, check if it matches the group's card type
            if (design.card_type_id) {
                return design.card_type_id === groupCardTypeId;
            }

            // If the design has a card_type property with an id, check if it matches the group's card type               //Dear whoever reads this in the future. Here is a bunch of comments to help you understand what is going on here.
            if (design.card_type && design.card_type.id) {
                return design.card_type.id === groupCardTypeId;
            }

            // If we can't determine the design's card type, include it by default
            return true;
        });
    }, [designs, cachedDesigns, groupData]);

    useEffect(() => {
        // Update lazyParams when designId changes (including when it becomes null)
        setLazyParams(prev => ({
            ...prev,
            designID: designId || null // Use null when designId is undefined/null/empty (thus fixing the bug relating to pressing "x" on the dropdown and not properly clearing the filter)
        }));
    }, [designId, setLazyParams]);

    const handleChange = (e) => {
        // Set designId to the new value (or undefined if cleared)
        setDesignId(e.value);
    };

    return (
        <Dropdown
            filter
            showClear
            filterBy="name"
            className='rounded-[6px] text-[black] w-full'
            optionLabel="name"
            optionValue="id"
            value={designId}
            options={filteredDesigns}
            onChange={handleChange}
            placeholder={
                loading
                    ? "Loading designs..."
                    : filteredDesigns.length > 0
                        ? "Select design"
                        : "No matching designs"
            }
            emptyMessage={loading ? "Loading designs..." : "No designs available for this card type"}
            disabled={loading}
        />
    );
};

export default GroupFilter;