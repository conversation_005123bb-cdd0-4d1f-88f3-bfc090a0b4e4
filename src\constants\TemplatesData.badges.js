// Professional ID Badge Templates for DesignSpace
// This file contains high-quality ID badge templates

// Corporate ID Badge Templates
export const corporateIdBadges = [
  {
    id: 'cid-001',
    name: 'Blue Corporate Badge',
    width: 300,
    height: 450,
    thumbnail: 'https://placehold.co/300x450/1e40af/ffffff?text=Blue+Corporate',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 300,
        height: 450,
        backgroundColor: '#ffffff',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 300,
        height: 120,
        backgroundColor: '#1e40af',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 20,
        y: 40,
        width: 260,
        height: 40,
        value: 'BLUE CORP',
        fontSize: 24,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_4',
        type: 'shape',
        shapeType: 'rectangle',
        x: 100,
        y: 140,
        width: 100,
        height: 100,
        backgroundColor: '#f0f0f0',
        borderColor: '#1e40af',
        borderWidth: 2,
      },
      {
        id: 'el_5',
        type: 'text',
        x: 20,
        y: 260,
        width: 260,
        height: 40,
        value: 'DAVID JOHNSON',
        fontSize: 22,
        fontWeight: 'bold',
        color: '#1e40af',
        textAlign: 'center',
      },
      {
        id: 'el_6',
        type: 'text',
        x: 20,
        y: 300,
        width: 260,
        height: 30,
        value: 'Senior Developer',
        fontSize: 16,
        fontWeight: 'normal',
        color: '#333333',
        textAlign: 'center',
      },
      {
        id: 'el_7',
        type: 'shape',
        shapeType: 'rectangle',
        x: 50,
        y: 340,
        width: 200,
        height: 1,
        backgroundColor: '#e0e0e0',
      },
      {
        id: 'el_8',
        type: 'text',
        x: 20,
        y: 360,
        width: 260,
        height: 20,
        value: 'ID: BC-2023-0042',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#666666',
        textAlign: 'center',
      },
      {
        id: 'el_9',
        type: 'text',
        x: 20,
        y: 390,
        width: 260,
        height: 20,
        value: 'Expires: 12/31/2024',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#666666',
        textAlign: 'center',
      },
    ]
  },
  {
    id: 'cid-002',
    name: 'Green Corporate Badge',
    width: 300,
    height: 450,
    thumbnail: 'https://placehold.co/300x450/166534/ffffff?text=Green+Corporate',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 300,
        height: 450,
        backgroundColor: '#ffffff',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 300,
        height: 100,
        backgroundColor: '#166534',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 20,
        y: 35,
        width: 260,
        height: 30,
        value: 'GREEN ENTERPRISES',
        fontSize: 18,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_4',
        type: 'shape',
        shapeType: 'circle',
        x: 100,
        y: 130,
        width: 100,
        height: 100,
        backgroundColor: '#f0f0f0',
        borderColor: '#166534',
        borderWidth: 3,
      },
      {
        id: 'el_5',
        type: 'text',
        x: 20,
        y: 250,
        width: 260,
        height: 40,
        value: 'SARAH GREEN',
        fontSize: 22,
        fontWeight: 'bold',
        color: '#166534',
        textAlign: 'center',
      },
      {
        id: 'el_6',
        type: 'text',
        x: 20,
        y: 290,
        width: 260,
        height: 30,
        value: 'Environmental Specialist',
        fontSize: 16,
        fontWeight: 'normal',
        color: '#333333',
        textAlign: 'center',
      },
      {
        id: 'el_7',
        type: 'shape',
        shapeType: 'rectangle',
        x: 75,
        y: 330,
        width: 150,
        height: 2,
        backgroundColor: '#166534',
      },
      {
        id: 'el_8',
        type: 'text',
        x: 20,
        y: 350,
        width: 260,
        height: 20,
        value: 'ID: GE-2023-0078',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#666666',
        textAlign: 'center',
      },
      {
        id: 'el_9',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 400,
        width: 300,
        height: 50,
        backgroundColor: '#166534',
      },
      {
        id: 'el_10',
        type: 'text',
        x: 20,
        y: 415,
        width: 260,
        height: 20,
        value: 'Access Level: All Areas',
        fontSize: 14,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
    ]
  },
  {
    id: 'cid-003',
    name: 'Red Corporate Badge',
    width: 300,
    height: 450,
    thumbnail: 'https://placehold.co/300x450/b91c1c/ffffff?text=Red+Corporate',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 300,
        height: 450,
        backgroundColor: '#ffffff',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 20,
        height: 450,
        backgroundColor: '#b91c1c',
      },
      {
        id: 'el_3',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 300,
        height: 80,
        backgroundColor: '#b91c1c',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 40,
        y: 25,
        width: 240,
        height: 30,
        value: 'RED TECHNOLOGIES',
        fontSize: 18,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_5',
        type: 'shape',
        shapeType: 'rectangle',
        x: 100,
        y: 120,
        width: 100,
        height: 120,
        backgroundColor: '#f0f0f0',
      },
      {
        id: 'el_6',
        type: 'text',
        x: 40,
        y: 260,
        width: 240,
        height: 40,
        value: 'MICHAEL CHEN',
        fontSize: 22,
        fontWeight: 'bold',
        color: '#b91c1c',
        textAlign: 'center',
      },
      {
        id: 'el_7',
        type: 'text',
        x: 40,
        y: 300,
        width: 240,
        height: 30,
        value: 'Security Officer',
        fontSize: 16,
        fontWeight: 'normal',
        color: '#333333',
        textAlign: 'center',
      },
      {
        id: 'el_8',
        type: 'shape',
        shapeType: 'rectangle',
        x: 40,
        y: 340,
        width: 240,
        height: 1,
        backgroundColor: '#e0e0e0',
      },
      {
        id: 'el_9',
        type: 'text',
        x: 40,
        y: 360,
        width: 240,
        height: 20,
        value: 'ID: RT-SEC-2023-0015',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#666666',
        textAlign: 'center',
      },
      {
        id: 'el_10',
        type: 'text',
        x: 40,
        y: 390,
        width: 240,
        height: 20,
        value: 'Security Clearance: Level 3',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#b91c1c',
        textAlign: 'center',
      },
    ]
  },
];

// Event Badge Templates
export const eventBadges = [
  {
    id: 'eb-001',
    name: 'Tech Conference Badge',
    width: 300,
    height: 450,
    thumbnail: 'https://placehold.co/300x450/6d28d9/ffffff?text=Tech+Conference',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 300,
        height: 450,
        backgroundColor: '#ffffff',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 300,
        height: 120,
        backgroundColor: '#6d28d9',
        style: { background: 'linear-gradient(135deg, #6d28d9, #9333ea)' },
      },
      {
        id: 'el_3',
        type: 'text',
        x: 20,
        y: 30,
        width: 260,
        height: 40,
        value: 'TECH SUMMIT 2023',
        fontSize: 22,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 20,
        y: 70,
        width: 260,
        height: 20,
        value: 'June 15-17, San Francisco',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_5',
        type: 'text',
        x: 20,
        y: 160,
        width: 260,
        height: 60,
        value: 'ALEX RODRIGUEZ',
        fontSize: 28,
        fontWeight: 'bold',
        color: '#333333',
        textAlign: 'center',
      },
      {
        id: 'el_6',
        type: 'text',
        x: 20,
        y: 220,
        width: 260,
        height: 30,
        value: 'SPEAKER',
        fontSize: 18,
        fontWeight: 'bold',
        color: '#6d28d9',
        textAlign: 'center',
        letterSpacing: '2px',
      },
      {
        id: 'el_7',
        type: 'shape',
        shapeType: 'rectangle',
        x: 75,
        y: 260,
        width: 150,
        height: 2,
        backgroundColor: '#e0e0e0',
      },
      {
        id: 'el_8',
        type: 'text',
        x: 20,
        y: 280,
        width: 260,
        height: 20,
        value: 'Company: TechInnovate Inc.',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#666666',
        textAlign: 'center',
      },
      {
        id: 'el_9',
        type: 'text',
        x: 20,
        y: 310,
        width: 260,
        height: 20,
        value: 'Session: AI in Enterprise',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#666666',
        textAlign: 'center',
      },
      {
        id: 'el_10',
        type: 'text',
        x: 20,
        y: 340,
        width: 260,
        height: 20,
        value: 'Time: Friday, 2:00 PM',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#666666',
        textAlign: 'center',
      },
      {
        id: 'el_11',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 400,
        width: 300,
        height: 50,
        backgroundColor: '#6d28d9',
        style: { background: 'linear-gradient(135deg, #6d28d9, #9333ea)' },
      },
      {
        id: 'el_12',
        type: 'text',
        x: 20,
        y: 415,
        width: 260,
        height: 20,
        value: 'Access: All Areas + VIP Lounge',
        fontSize: 14,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
    ]
  }
];

// Export all badge templates
export const allBadgeTemplates = [
  ...corporateIdBadges,
  ...eventBadges
];
