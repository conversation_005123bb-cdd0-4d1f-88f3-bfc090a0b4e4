// Professional Business Card Templates for DesignSpace
// This file contains high-quality business card templates

// Modern Business Card Templates
export const modernBusinessCards = [
  {
    id: 'mbc-001',
    name: 'Modern Blue Business Card',
    width: 350,
    height: 200,
    thumbnail: 'https://placehold.co/350x200/3b82f6/ffffff?text=Modern+Blue',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 350,
        height: 200,
        backgroundColor: '#3b82f6',
      },
      {
        id: 'el_2',
        type: 'text',
        x: 30,
        y: 50,
        width: 290,
        height: 40,
        value: 'JAMES WILSON',
        fontSize: 24,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'left',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 30,
        y: 90,
        width: 290,
        height: 20,
        value: 'Software Engineer',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#e0e0e0',
        textAlign: 'left',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 30,
        y: 130,
        width: 290,
        height: 20,
        value: '<EMAIL> | (555) 123-4567',
        fontSize: 12,
        fontWeight: 'normal',
        color: '#e0e0e0',
        textAlign: 'left',
      },
    ]
  },
  {
    id: 'mbc-002',
    name: 'Modern Dark Business Card',
    width: 350,
    height: 200,
    thumbnail: 'https://placehold.co/350x200/0f172a/ffffff?text=Modern+Dark',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 350,
        height: 200,
        backgroundColor: '#0f172a',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 10,
        height: 200,
        backgroundColor: '#3b82f6',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 30,
        y: 50,
        width: 290,
        height: 40,
        value: 'SARAH JOHNSON',
        fontSize: 24,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'left',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 30,
        y: 90,
        width: 290,
        height: 20,
        value: 'Marketing Director',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#e0e0e0',
        textAlign: 'left',
      },
      {
        id: 'el_5',
        type: 'text',
        x: 30,
        y: 130,
        width: 290,
        height: 20,
        value: '<EMAIL> | (555) 987-6543',
        fontSize: 12,
        fontWeight: 'normal',
        color: '#e0e0e0',
        textAlign: 'left',
      },
    ]
  },
  {
    id: 'mbc-003',
    name: 'Modern Gradient Business Card',
    width: 350,
    height: 200,
    thumbnail: 'https://placehold.co/350x200/8b5cf6/ffffff?text=Modern+Gradient',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 350,
        height: 200,
        backgroundColor: '#8b5cf6',
        style: { background: 'linear-gradient(135deg, #8b5cf6, #d946ef)' },
      },
      {
        id: 'el_2',
        type: 'text',
        x: 30,
        y: 50,
        width: 290,
        height: 40,
        value: 'ALEX CHEN',
        fontSize: 24,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'left',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 30,
        y: 90,
        width: 290,
        height: 20,
        value: 'UX Designer',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'left',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 30,
        y: 130,
        width: 290,
        height: 20,
        value: '<EMAIL> | (555) 234-5678',
        fontSize: 12,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'left',
      },
    ]
  },
  {
    id: 'mbc-004',
    name: 'Modern White Business Card',
    width: 350,
    height: 200,
    thumbnail: 'https://placehold.co/350x200/ffffff/333333?text=Modern+White',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 350,
        height: 200,
        backgroundColor: '#ffffff',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 350,
        height: 10,
        backgroundColor: '#3b82f6',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 30,
        y: 50,
        width: 290,
        height: 40,
        value: 'MICHAEL BROWN',
        fontSize: 24,
        fontWeight: 'bold',
        color: '#333333',
        textAlign: 'left',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 30,
        y: 90,
        width: 290,
        height: 20,
        value: 'Financial Advisor',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#666666',
        textAlign: 'left',
      },
      {
        id: 'el_5',
        type: 'text',
        x: 30,
        y: 130,
        width: 290,
        height: 20,
        value: '<EMAIL> | (555) 345-6789',
        fontSize: 12,
        fontWeight: 'normal',
        color: '#666666',
        textAlign: 'left',
      },
    ]
  },
  {
    id: 'mbc-005',
    name: 'Modern Green Business Card',
    width: 350,
    height: 200,
    thumbnail: 'https://placehold.co/350x200/10b981/ffffff?text=Modern+Green',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 350,
        height: 200,
        backgroundColor: '#10b981',
      },
      {
        id: 'el_2',
        type: 'text',
        x: 30,
        y: 50,
        width: 290,
        height: 40,
        value: 'EMILY GREEN',
        fontSize: 24,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'left',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 30,
        y: 90,
        width: 290,
        height: 20,
        value: 'Environmental Consultant',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#e0e0e0',
        textAlign: 'left',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 30,
        y: 130,
        width: 290,
        height: 20,
        value: '<EMAIL> | (555) 456-7890',
        fontSize: 12,
        fontWeight: 'normal',
        color: '#e0e0e0',
        textAlign: 'left',
      },
    ]
  },
];

// Luxury Business Card Templates
export const luxuryBusinessCards = [
  {
    id: 'lbc-001',
    name: 'Gold Luxury Business Card',
    width: 350,
    height: 200,
    thumbnail: 'https://placehold.co/350x200/1a1a1a/d4af37?text=Gold+Luxury',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 350,
        height: 200,
        backgroundColor: '#1a1a1a',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 20,
        y: 20,
        width: 310,
        height: 160,
        backgroundColor: 'transparent',
        borderColor: '#d4af37',
        borderWidth: 1,
      },
      {
        id: 'el_3',
        type: 'text',
        x: 40,
        y: 60,
        width: 270,
        height: 40,
        value: 'JONATHAN SMITH',
        fontSize: 22,
        fontWeight: 'bold',
        color: '#d4af37',
        textAlign: 'center',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 40,
        y: 100,
        width: 270,
        height: 20,
        value: 'LUXURY BRAND CONSULTANT',
        fontSize: 12,
        fontWeight: 'normal',
        color: '#d4af37',
        textAlign: 'center',
        letterSpacing: '2px',
      },
      {
        id: 'el_5',
        type: 'text',
        x: 40,
        y: 130,
        width: 270,
        height: 20,
        value: '<EMAIL>',
        fontSize: 10,
        fontWeight: 'normal',
        color: '#d4af37',
        textAlign: 'center',
      },
    ]
  }
];

// Export all business card templates
export const allBusinessCardTemplates = [
  ...modernBusinessCards,
  ...luxuryBusinessCards
];
