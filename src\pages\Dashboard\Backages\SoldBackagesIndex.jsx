import React from 'react';
import { useQuery } from 'react-query';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Tooltip } from 'primereact/tooltip';
import { TfiTrash } from 'react-icons/tfi';
import { FaRegEye } from 'react-icons/fa';
import { FiEdit } from 'react-icons/fi';
import { Link } from 'react-router-dom';
import Container from '@components/Container';
import axiosInstance from "../../../config/Axios"; // Ensure the correct path
import { loadStripe } from '@stripe/stripe-js';

// Load Stripe key
const stripePromise = loadStripe('pk_test_51Kg7JrJPZyIMVMipnIL0gpi2E3jvHhQ4h6UDReB84sBKDnuC5dATko0CkagEPc639o7dbfiY9Ub7zmG1g3M9eq0p009uekzZe3');

// Fetch packages function
const fetchPackages = async () => {
  try {
    // Get the token from localStorage
    const token = localStorage.getItem('token');
    
    if (!token) {
      console.error("Token not found in localStorage");
      return; // Stop execution if token is not found
    }

    // Send request to the server
    const response = await axiosInstance.get('packages/show-sold-packages', {
      headers: {
        Authorization: `Bearer ${token}`,  // Add token to the header
      }
    });

    // Log response to ensure data is received
    console.log('Response data:', response.data);
    return response.data;

  } catch (error) {
    // Handle errors during the request
    console.error("Error fetching packages:", error);

    if (error.response) {
      // If there is a response from the server
      console.error('Response error:', error.response.data);
      console.error('Status code:', error.response.status);
    } else if (error.request) {
      // If no response received
      console.error('No response received:', error.request);
    } else {
      // Other errors
      console.error('Error message:', error.message);
    }
  }
};

const PackagesDataTable = () => {
  const { data: packages, isLoading, isError, error } = useQuery('packages', fetchPackages);

  if (isLoading) return <p className="text-center">Loading...</p>;
  if (isError) return <p className="text-center text-red-500">Error: {error.message}</p>;

  // Handle purchase of a package


  // Action icons for each package
  const actionsBodyTemplate = (rowData) => {
    return (
      <div className="flex justify-around space-x-3">
        {/* View details
        <Tooltip target=".view-icon" content="View Details" position="top" />
        <Link to={`/package-details/${rowData.id}`} className="view-icon">
          <FaRegEye className="text-blue-600 hover:text-blue-800 transition duration-200" size={20} />
        </Link> */}
  
        {/* Edit package */}
        <Tooltip target=".edit-icon" content="Edit" position="top" />
        <Link to={`/edit-package/${rowData.id}`} className="edit-icon">
          <FiEdit className="text-yellow-500 hover:text-yellow-700 transition duration-200" size={20} />
        </Link>
  
        {/* Buy or Upgrade package */}
        {/* <Tooltip target=".buy-icon" content={rowData.is_purchased ? "Upgrade Package" : "Buy Package"} position="top" />
        <button
          className={`main-btn text-md shadow-md px-5 py-2 rounded-lg transition duration-200 ${rowData.is_purchased ? "bg-orange-600 hover:bg-orange-700" : "bg-green-600 hover:bg-green-700"} text-white`}
          onClick={() => handleBuy(rowData.id)}
        >
          {rowData.is_purchased ? "Upgrade" : "Buy"}
        </button> */}
  
        {/* Delete package */}
        <Tooltip target=".delete-icon" content="Delete" position="top" />
        <button
          className="delete-icon text-red-500 hover:text-red-700 transition duration-200"
          onClick={() => handleDelete(rowData.id)}
        >
          <TfiTrash className="text-red-500" size={20} />
        </button>
      </div>
    );
  };

  return (
<Container className="flex flex-col h-full">
  <div className="w-full flex justify-between mb-4">
    <h1 className="text-2xl font-semibold text-gray-700">Sold Packages</h1>
    <Link to="/create-package"></Link>
  </div>
  {/* Display table data */}
  <div className="flex-1">
    <DataTable 
      value={packages} 
      paginator 
      rows={10}
      className="mt-4 shadow-lg rounded-lg bg-white h-full"
      responsiveLayout="scroll"
      lazy
      filterDisplay="row"
      breakpoint="960px"
      dataKey="id"
      rowsPerPageOptions={[5, 25, 50, 100]}
      paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} products"
      scrollable
      scrollHeight="100%"
    >
    <Column field="name" header="Package Name" className="text-left" />
    <Column field="total_price" header="Total Price" className="text-left" />
    <Column field="card_limit" header="Card Limit" className="text-center" />
    <Column 
      field="card_types.name" 
      header="Card Type" 
      className="text-center"
      body={(rowData) => {
        return rowData.card_types.length > 1 
          ? rowData.card_types.map(card => card.name).join(' + ') 
          : rowData.card_types[0].name;
      }}
    />
        <Column 
      field="purchased_by_manager_name" 
      header="Purchased By" 
      className="text-center"
    />

  </DataTable>
  </div>
</Container>

  );
};

export default PackagesDataTable;
