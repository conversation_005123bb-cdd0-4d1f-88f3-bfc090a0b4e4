name: Build and Deploy production 

on:
  workflow_dispatch:
  push:
    branches:
      - staging
jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:       
      - name: Checkout code from repository
        uses: actions/checkout@v3
        
      - name: Install Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'npm'
          
      - name: Cache NPM dependencies
        uses: actions/cache@v3
        with:
          path: node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('package-lock.json') }} # (1)

      - name: Install dependencies
        run: yarn install

      - name : configure api endpoint
        run : |
          touch .env
          chmod 755 .env
          printf "%s" "${{secrets.API_ENDPOINT_STAGING}}" > .env
          
      - name: Build React application
        run :  yarn build
        continue-on-error: true
        
      - name: Set AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          role-to-assume: arn:aws:iam::878833450016:role/Github-pipeline
          aws-region: us-east-1 
      
      - name : upload to s3
        run : aws s3 sync ./dist s3://inknull-frontend-staging --region us-east-1  --delete
            
      
      - name: Get current date
        if: always()
        id: date
        run: echo "jordan_time=$(TZ=Asia/Amman date)" >> $GITHUB_ENV


      - name: Send custom JSON data to Slack workflow
        if: always()
        id: slack
        uses: slackapi/slack-github-action@v1.23.0
        with:
          payload: |
            {
              "text": "GitHub Action build result: ${{ job.status }}\n${{ github.event.pull_request.html_url || github.event.head_commit.url }}",
              "blocks": [
              {
                "type": "header",
                "text": {
                  "type": "plain_text",
                  "text": "${{ github.repository }}",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "fields": [
                  {
                    "type": "mrkdwn",
                    "text": "*Triggered via:*\n${{GITHUB.EVENT_NAME}} "
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Created by:*\n<https://github.com/${{GITHUB.ACTOR}}|${{GITHUB.ACTOR}}>"
                  }
                ]
              },
              {
                "type": "section",
                "fields": [
                  
                  {
                    "type": "mrkdwn",
                    "text": "*Created at:*\n${{ env.jordan_time }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Commit:*\n<https://github.com/${{github.repository }}/commit/${{ github.sha }}|${{ github.sha }}>"
                  }
                ]
              },
              {
                "type": "section",
                "fields": [
                  {
                    "type": "mrkdwn",
                    "text": "*${{GITHUB.REF_TYPE}}* and *Ref* \n${{GITHUB.REF_NAME}} and ${{GITHUB.REF}}:ghost:" 
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Status:*\n ${{ job.status }} "
                  }
                ]
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "<https://github.com/${{github.repository }}/actions/runs/${{GITHUB.RUN_ID}}|View Full Workflow>"
                }
              }
            ]
                    }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.WEBHOOK }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK
