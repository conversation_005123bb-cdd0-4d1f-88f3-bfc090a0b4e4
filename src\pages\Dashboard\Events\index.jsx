import React, { useMemo, useState } from 'react'
import Container from '@components/Container'


function EventsIndex() {
    return (
        <Container>
            <div className="w-full flex justify-center">
                <div className='w-4/12'>
                    <h1 className='text-xl font-bold '> Events</h1>
                </div>
                <div className='flex justify-end  w-8/12'>
                    {/* <button className="main-btn text-md me-2 shadow-md">Create Group</button> */}
                    <button className="main-btn text-md shadow-md"  >Add Event</button>
                </div>
            </div>

        </Container >
    )
}

export default EventsIndex