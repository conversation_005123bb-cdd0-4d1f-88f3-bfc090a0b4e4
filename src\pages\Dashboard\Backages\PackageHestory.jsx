import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom"; 
import Container from "@components/Container";
import axiosInstance from "../../../config/Axios";
import { useGlobalContext } from "@contexts/GlobalContext";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { InputText } from "primereact/inputtext";
import { Dropdown } from "primereact/dropdown";

export default function PackagesHistory() {
  const [packagesHistory, setPackagesHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    package_name: '',
    status: '',
  });
  const { userId } = useParams(); // استخراج userId من الرابط

  const statusOptions = [
    { label: 'All Statuses', value: '' },
    { label: 'Active', value: 'active' },
    { label: 'Expired', value: 'expired' },
    { label: 'Suspended', value: 'suspended' },
  ];

  useEffect(() => {
    const fetchPackagesHistory = async () => {
      try {
        setLoading(true);
        console.log('userId:', userId); // التأكد من أن userId صحيح
        console.log("Filters:", filters); // تحقق من الفلاتر المرسلة
  
        if (!userId) return;
  
        const { data } = await axiosInstance.get(
          `/packages/${userId}/packages_history`,
          {
            params: {
              package_name: filters.package_name,
              status: filters.status,
            }
          }
        );
  
        setPackagesHistory(data.history_packages);
        console.log("Fetched Packages History:", data.history_packages);
      } catch (error) {
        console.error("Error fetching packages history:", error);
      } finally {
        setLoading(false);
      }
    };
  
    fetchPackagesHistory();
  }, [userId, filters]);
  
  const dateBodyTemplate = (rowData) => {
    return new Date(rowData.created_at).toLocaleDateString();
  };

  const statusBodyTemplate = (rowData) => {
    const statusClass = rowData.status === "active" 
      ? "bg-[#22C55E]" 
      : "bg-[#DC2626]";

    return (
      <span
        className={`text-white rounded-[6px] font-bold text-sm py-2 px-3 capitalize ${statusClass}`}
      >
        {rowData.status}
      </span>
    );
  };

  const onFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const renderHeader = () => {
    return (
      <div className="flex flex-wrap gap-4 mb-4">
       
      </div>
    );
  };

  return (
    <Container>
       <div className="w-full ">
        </div>
      <div className="w-full flex justify-between mb-5">
        <button 
          onClick={() => window.history.back()}
          className="main-btn text-md w-28 shadow-md px-5 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-200"
        >
          Go Back
        </button>
       
      </div>
      <div className="w-full mb-5">
          <h1 className="text-xl font-bold">Packages History</h1>
        </div>
      <div className="w-full">
        <div className="table-responsive overflow-x-auto">
          <DataTable
            value={packagesHistory}
            loading={loading}
            className="table w-full border"
            paginator
            rows={10}
            rowsPerPageOptions={[5, 10, 25, 50]}
            emptyMessage="No packages history found"
            header={renderHeader()}
            filterDisplay="row"
          >
            <Column 
              field="package_name" 
              header="Package Name" 
              sortable 
            />
            <Column field="total_price" header="Total Price" sortable />
            <Column 
              field="purchased_at" 
              header="Purchase Date" 
              sortable
            />
            <Column 
              field="expiry_date" 
              header="Expiry Date" 
              sortable
            />
            <Column field="card_limit" header="Allowed Cards Count" />
            <Column 
              field="status" 
              header="Status" 
              body={statusBodyTemplate}
              sortable
              // filter
              // filterField="status"
              // filterElement={
              //   <Dropdown
              //     options={statusOptions}
              //     value={filters.status.value}
              //     onChange={(e) => onFilterChange('status', e.value)}
              //     placeholder="All Statuses"
              //   />
              // }
            />
          </DataTable>
        </div>
      </div>
    </Container>
  );
}
