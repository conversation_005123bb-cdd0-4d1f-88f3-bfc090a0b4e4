import { useMutation, useQuery } from "react-query";
import axiosInstance from "../config/Axios";
import { useGlobalContext } from "@contexts/GlobalContext";
import { useDataTableContext } from "@contexts/DataTableContext";
import { handleErrors } from "@utils/helper";
import { cardsTableConfig } from "@constants/tableConfig";

//-------------- Fetch all cards --------------//
const fetchCards = async () => {
    const { data } = await axiosInstance.get("/all_cards_admin?all=1");
    // The API now returns { status, message, data: [...] }, so return the whole response
    return data; // UI should use data.data
};

export const useFetchCards = () => {
    const { showToast } = useGlobalContext();
    return useQuery("all_cards_admin", fetchCards, {
        onError: (error) => handleErrors(showToast, error)
    });
};


//-------------- Fetch all card types --------------//
const fetchCardsTypes = async () => {
    const { data } = await axiosInstance.get("/all_cards_admin?all=1");
    // The API now returns { status, message, data: [...] }, so return the whole response
    return data; // UI should use data.data
};

export const useFetchCard = () => {
    const { showToast } = useGlobalContext();
    return useQuery("all_cards_admin", fetchCards, {
        onError: (error) => handleErrors(showToast, error)
    });
};

//-------------- Create card --------------//
const createCard = async (payload) => {
    const { data } = await axiosInstance.post("/cards", payload);
    return data;
};

export const useCreateCardMutation = () => {
    const { showToast, dialogHandler } = useGlobalContext();
    return useMutation(createCard, {
        onSuccess: async (data) => {
            dialogHandler("createCard");
            showToast("success", data?.message);
        },
        onError: (error) => handleErrors(showToast, error)
    });
};

//-------------- Update card --------------//
const updateCard = async (payload) => {
    const { data } = await axiosInstance.put(`/cards/${payload.id}`, payload);
    return data;
};

export const useUpdateCardMutation = () => {
    const { showToast, dialogHandler } = useGlobalContext();
    const { setLazyParams } = useDataTableContext();
    return useMutation(updateCard, {
        onSuccess: async (data) => {
            dialogHandler("updateCard");
            showToast("success", data?.message);
            setLazyParams(prev => ({ ...prev, ...cardsTableConfig }));
        },
        onError: (error) => handleErrors(showToast, error)
    });
};

//-------------- Delete card --------------//
const deleteCard = async (id) => {
    const { data } = await axiosInstance.delete(`/cards/${id}`);
    return data;
};

export const useDeleteCardMutation = () => {
    const { showToast } = useGlobalContext();
    const { setLazyParams } = useDataTableContext();
    return useMutation(deleteCard, {
        onSuccess: async () => {
            showToast("success", "Card deleted successfully!");
            setLazyParams(prev => ({ ...prev, ...cardsTableConfig }));
        },
        onError: (error) => handleErrors(showToast, error)
    });
};

//-------------- Fetch single card by ID --------------//
const fetchCardById = async (id) => {
    const { data } = await axiosInstance.get(`/cards/${id}`);
    return data;
};

export const useFetchCardById = (id) => {
    const { showToast } = useGlobalContext();
    return useQuery(["card", id], () => fetchCardById(id), {
        onError: (error) => handleErrors(showToast, error)
    });
};
