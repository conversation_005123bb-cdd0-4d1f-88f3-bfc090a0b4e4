import React, { useEffect } from 'react'

import { Dialog } from 'primereact/dialog'

import { useGlobalContext } from '@contexts/GlobalContext';
import CreateGroupForm from './CreateGroupForm';
import UpdateGroupForm from './UpdateGroupForm';

function AssignGroupDialog({ data = {} }) {
    const { openDialog, dialogHandler,  } = useGlobalContext();
 
    const onHide = () => {
        if (openDialog.createGroup)
            dialogHandler("createGroup")
        else
            dialogHandler("updateGroup")
    }

    return (
        <Dialog visible={openDialog.createGroup || openDialog.updateGroup}
            style={{ width: '40vw' }}
            breakpoints={{ '960px': '95vw' }}
            header="Group"
            modal className="p-fluid"
            onHide={ onHide}
        >
            {
                openDialog.createGroup ? <CreateGroupForm selectedUsers = {data} /> : <UpdateGroupForm selectedRow={data} />
            }


        </Dialog>
    )
}

export default AssignGroupDialog
