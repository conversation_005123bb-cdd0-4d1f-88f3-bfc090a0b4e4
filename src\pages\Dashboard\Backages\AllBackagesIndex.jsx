import React from 'react';
import { useQuery } from 'react-query';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Tooltip } from 'primereact/tooltip';
import { TfiTrash } from 'react-icons/tfi';
import { FaRegEye } from 'react-icons/fa';
import { FiEdit } from 'react-icons/fi';
import { Link } from 'react-router-dom';
import Container from '@components/Container';
import axiosInstance from "../../../config/Axios"; // Ensure the correct path
import { loadStripe } from '@stripe/stripe-js';


const stripePromise = loadStripe('pk_test_51Kg7JrJPZyIMVMipnIL0gpi2E3jvHhQ4h6UDReB84sBKDnuC5dATko0CkagEPc639o7dbfiY9Ub7zmG1g3M9eq0p009uekzZe3');


const fetchPackages = async () => {
  try {

    const token = localStorage.getItem('token');
    
    if (!token) {
      console.error("Token not found in localStorage");
      return;
    }

    const response = await axiosInstance.get('packages/original_packages', {
      headers: {
        Authorization: `Bearer ${token}`, 
      }
    });

    // Log response to ensure data is received
    console.log('Response data:', response.data);
    return response.data;

  } catch (error) {
    // Handle errors during the request
    console.error("Error fetching packages:", error);

    if (error.response) {
      // If there is a response from the server
      console.error('Response error:', error.response.data);
      console.error('Status code:', error.response.status);
    } else if (error.request) {
      // If no response received
      console.error('No response received:', error.request);
    } else {
      // Other errors
      console.error('Error message:', error.message);
    }
  }
};

const PackagesDataTable = () => {
  const { data: packages, isLoading, isError, error } = useQuery('packages', fetchPackages);

  if (isLoading) return <p className="text-center">Loading...</p>;
  if (isError) return <p className="text-center text-red-500">Error: {error.message}</p>;

  // Handle purchase of a package
  const handleBuy = async (id) => {
    try {
        const response = await axiosInstance.post(`/packages/${id}/purchase`);

        const { sessionId, packageId } = response.data;

        if (sessionId) {
            const stripe = await stripePromise;
            const { error } = await stripe.redirectToCheckout({ sessionId });

            if (error) {
                console.error('Error during checkout redirect:', error.message);
                return;
            }

            const successResponse = await axiosInstance.get(`/packages/payment/success`, {
              params: { package_id: packageId }
          });
          
            console.log('toooooken:', `Bearer ${localStorage.getItem('token')}`);

            if (successResponse.status === 200) {
                console.log(successResponse.data.message);
            } else {
                console.error("Error: Payment verification failed.");
            }
        } else {
            console.error("Error: No session ID received");
        }
    } catch (error) {
        console.error('Error purchasing package:', error.response?.data || error);
    }
};

  // Action icons for each package
  const actionsBodyTemplate = (rowData) => {
    return (
      <div className="flex justify-around space-x-3">
        {/* View details */}
        <Tooltip target=".view-icon" content="View Details" position="top" />
        <Link to={`/package-details/${rowData.id}`} className="view-icon">
          <FaRegEye className="text-blue-600 hover:text-blue-800 transition duration-200" size={20} />
        </Link>

        {/* Edit package */}
        <Tooltip target=".edit-icon" content="Edit" position="top" />
        <Link to={`/edit-package/${rowData.id}`} className="edit-icon">
          <FiEdit className="text-yellow-500 hover:text-yellow-700 transition duration-200" size={20} />
        </Link>

        {/* Buy package */}
        <Tooltip target=".buy-icon" content="Buy Package" position="top" />
        <button
          className="main-btn text-md shadow-md px-5 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition duration-200"
          onClick={() => handleBuy(rowData.id)}
        >
          Buy
        </button>

        {/* Delete package */}
        <Tooltip target=".delete-icon" content="Delete" position="top" />
        <button
          className="delete-icon text-red-500 hover:text-red-700 transition duration-200"
          onClick={() => handleDelete(rowData.id)}
        >
          <TfiTrash className="text-red-500" size={20} />
        </button>
      </div>
    );
  };

  return (
    <Container>
      <div className="w-full flex justify-between mb-4">
        <h1 className="text-2xl font-semibold text-gray-700">Package Management</h1>
        <Link to="/create-package">
          <button className="main-btn text-md shadow-md px-5 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-200">
            Create New Package
          </button>
        </Link>
      </div>

      {/* Display table data */}
      <DataTable value={packages} paginator rows={10} className="mt-4 shadow-lg rounded-lg bg-white" responsiveLayout="scroll">
        <Column field="name" header="Package Name" className="text-left" />
        <Column field="total_price" header="Total Price" className="text-left" />
        <Column field="card_limit" header="Card Limit" className="text-center" />
        <Column field="purchased_by_manager_name" header="Purchased By " className="text-center" />
        <Column field="is_purchased" header="Is Purchased" body={(rowData) => rowData.is_purchased ? "Yes" : "No"} className="text-center" />
        <Column body={actionsBodyTemplate} header="Actions" />
      </DataTable>
    </Container>
  );
};

export default PackagesDataTable;
