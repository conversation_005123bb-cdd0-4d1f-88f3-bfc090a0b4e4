// More Social Media Templates for DesignSpace
// This file contains additional professional social media templates

// Instagram Story Templates
export const instagramStoryTemplates = [
  {
    id: 'is-001',
    name: 'Instagram Story - Gradient',
    width: 1080,
    height: 1920,
    thumbnail: 'https://placehold.co/1080x1920/8b5cf6/ffffff?text=Instagram+Story',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1080,
        height: 1920,
        backgroundColor: '#8b5cf6',
        style: { background: 'linear-gradient(135deg, #8b5cf6, #ec4899)' },
      },
      {
        id: 'el_2',
        type: 'text',
        x: 100,
        y: 800,
        width: 880,
        height: 200,
        value: 'YOUR STORY TITLE',
        fontSize: 80,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
        textShadow: '2px 2px 4px rgba(0,0,0,0.3)',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 100,
        y: 1000,
        width: 880,
        height: 100,
        value: 'Add your story description here',
        fontSize: 36,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'center',
        textShadow: '1px 1px 2px rgba(0,0,0,0.3)',
      },
      {
        id: 'el_4',
        type: 'shape',
        shapeType: 'rectangle',
        x: 390,
        y: 1150,
        width: 300,
        height: 2,
        backgroundColor: '#ffffff',
      },
      {
        id: 'el_5',
        type: 'text',
        x: 100,
        y: 1200,
        width: 880,
        height: 40,
        value: 'SWIPE UP',
        fontSize: 24,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
        letterSpacing: '5px',
      },
    ]
  },
  {
    id: 'is-002',
    name: 'Instagram Story - Product',
    width: 1080,
    height: 1920,
    thumbnail: 'https://placehold.co/1080x1920/ffffff/333333?text=Product+Story',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1080,
        height: 1920,
        backgroundColor: '#ffffff',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 240,
        y: 400,
        width: 600,
        height: 600,
        backgroundColor: '#f8fafc',
        borderRadius: 20,
      },
      {
        id: 'el_3',
        type: 'text',
        x: 100,
        y: 1100,
        width: 880,
        height: 100,
        value: 'NEW PRODUCT',
        fontSize: 70,
        fontWeight: 'bold',
        color: '#0f172a',
        textAlign: 'center',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 100,
        y: 1200,
        width: 880,
        height: 100,
        value: 'Available Now',
        fontSize: 40,
        fontWeight: 'normal',
        color: '#64748b',
        textAlign: 'center',
      },
      {
        id: 'el_5',
        type: 'shape',
        shapeType: 'rectangle',
        x: 340,
        y: 1350,
        width: 400,
        height: 60,
        backgroundColor: '#0f172a',
        borderRadius: 30,
      },
      {
        id: 'el_6',
        type: 'text',
        x: 340,
        y: 1365,
        width: 400,
        height: 30,
        value: 'SHOP NOW',
        fontSize: 24,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
    ]
  },
  {
    id: 'is-003',
    name: 'Instagram Story - Sale',
    width: 1080,
    height: 1920,
    thumbnail: 'https://placehold.co/1080x1920/dc2626/ffffff?text=Sale+Story',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1080,
        height: 1920,
        backgroundColor: '#dc2626',
      },
      {
        id: 'el_2',
        type: 'text',
        x: 100,
        y: 600,
        width: 880,
        height: 200,
        value: 'FLASH SALE',
        fontSize: 100,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 100,
        y: 850,
        width: 880,
        height: 300,
        value: '50% OFF',
        fontSize: 180,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 100,
        y: 1200,
        width: 880,
        height: 100,
        value: 'TODAY ONLY',
        fontSize: 60,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_5',
        type: 'shape',
        shapeType: 'rectangle',
        x: 290,
        y: 1350,
        width: 500,
        height: 80,
        backgroundColor: '#ffffff',
        borderRadius: 40,
      },
      {
        id: 'el_6',
        type: 'text',
        x: 290,
        y: 1375,
        width: 500,
        height: 30,
        value: 'SHOP NOW',
        fontSize: 30,
        fontWeight: 'bold',
        color: '#dc2626',
        textAlign: 'center',
      },
    ]
  }
];

// Twitter Post Templates
export const twitterPostTemplates = [
  {
    id: 'tp-001',
    name: 'Twitter Post - Quote',
    width: 1200,
    height: 675,
    thumbnail: 'https://placehold.co/1200x675/0f172a/ffffff?text=Twitter+Quote',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1200,
        height: 675,
        backgroundColor: '#0f172a',
      },
      {
        id: 'el_2',
        type: 'text',
        x: 100,
        y: 100,
        width: 1000,
        height: 100,
        value: '"',
        fontSize: 150,
        fontWeight: 'bold',
        color: '#3b82f6',
        textAlign: 'center',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 100,
        y: 250,
        width: 1000,
        height: 200,
        value: 'Your inspirational quote goes here. Make it meaningful and impactful.',
        fontSize: 36,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 100,
        y: 450,
        width: 1000,
        height: 50,
        value: '- Your Name',
        fontSize: 24,
        fontWeight: 'bold',
        color: '#3b82f6',
        textAlign: 'center',
      },
      {
        id: 'el_5',
        type: 'text',
        x: 100,
        y: 550,
        width: 1000,
        height: 30,
        value: '@yourtwitterhandle',
        fontSize: 18,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'center',
      },
    ]
  }
];

// LinkedIn Post Templates
export const linkedinPostTemplates = [
  {
    id: 'lp-001',
    name: 'LinkedIn Post - Professional',
    width: 1200,
    height: 627,
    thumbnail: 'https://placehold.co/1200x627/0a66c2/ffffff?text=LinkedIn+Post',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1200,
        height: 627,
        backgroundColor: '#0a66c2',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 40,
        y: 40,
        width: 1120,
        height: 547,
        backgroundColor: 'transparent',
        borderColor: '#ffffff',
        borderWidth: 2,
      },
      {
        id: 'el_3',
        type: 'text',
        x: 100,
        y: 150,
        width: 1000,
        height: 150,
        value: 'PROFESSIONAL HEADLINE',
        fontSize: 60,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 100,
        y: 300,
        width: 1000,
        height: 100,
        value: 'Add your professional insight or announcement here',
        fontSize: 30,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_5',
        type: 'text',
        x: 100,
        y: 450,
        width: 1000,
        height: 50,
        value: 'YOUR NAME | YOUR POSITION',
        fontSize: 24,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
    ]
  }
];

// Export all additional social media templates
export const moreSocialTemplates = [
  ...instagramStoryTemplates,
  ...twitterPostTemplates,
  ...linkedinPostTemplates
];
