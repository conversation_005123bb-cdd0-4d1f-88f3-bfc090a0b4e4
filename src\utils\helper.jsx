import _ from "lodash"
import { useLocation } from "react-router-dom";

// get forms validation errors
export const getFormErrorMessage = (name, errors) => {
  return errors[name] && <small className="p-error">{errors[name].message}</small>
};

// get random key for lists items
export const generateKey = () => {
  return Math.random().toString(36).substr(2, 9)
}

// get random key for lists items
export const handleErrors = (showToast, error) => {
  let _errors = error?.response?.data?.errors;
  
  // التحقق من وجود _errors قبل استخدام foreach
  if (_errors && typeof _errors === 'object') {
    _.forIn(_errors, (value, key) => {
      showToast("error", "Error", value)
    });
  } else {
    // إذا لم تكن _errors موجودة أو ليست كائنًا، استخدم رسالة الخطأ العامة
    showToast("error", "Error", error.response?.data?.message || "An unexpected error occurred")
  }
}

export function useQueryParams() {
  const { search } = useLocation();
  return new URLSearchParams(search);
}
