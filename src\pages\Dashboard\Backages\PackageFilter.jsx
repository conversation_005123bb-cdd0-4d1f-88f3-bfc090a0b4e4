import React from 'react';
import { useQuery } from 'react-query';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Tooltip } from 'primereact/tooltip';
import { TfiTrash } from 'react-icons/tfi';
import { FaRegEye } from 'react-icons/fa';
import { FiEdit } from 'react-icons/fi';
import { Link } from 'react-router-dom';
import Container from '@components/Container';
import axiosInstance from "../../../config/Axios"; // Ensure the correct path
import { loadStripe } from '@stripe/stripe-js';
import CreateBackageForm from '../../../pages/Dashboard/Backages/CreateBackageForm';


import { useState } from 'react';
// import { Link } from 'react-router-dom';
// import { Container, DataTable, Column, Tooltip } from 'primereact';
// import { FaRegEye } from 'react-icons/fa';
// import { FiEdit } from 'react-icons/fi';
// import { TfiTrash } from 'react-icons/tfi';
// Load Stripe key
const stripePromise = loadStripe('pk_test_51Kg7JrJPZyIMVMipnIL0gpi2E3jvHhQ4h6UDReB84sBKDnuC5dATko0CkagEPc639o7dbfiY9Ub7zmG1g3M9eq0p009uekzZe3');

// Fetch packages function
const fetchPackages = async () => {
  try {
    // Get the token from localStorage
    const token = localStorage.getItem('token');
    
    if (!token) {
      console.error("Token not found in localStorage");
      return; // Stop execution if token is not found
    }

    // Send request to the server
    const response = await axiosInstance.get('packages/show-all-packages', {
      headers: {
        Authorization: `Bearer ${token}`,  // Add token to the header
      }
    });

    // Log response to ensure data is received
    console.log('Response data:', response.data);
    return response.data;

  } catch (error) {
    // Handle errors during the request
    console.error("Error fetching packages:", error);

    if (error.response) {
      // If there is a response from the server
      console.error('Response error:', error.response.data);
      console.error('Status code:', error.response.status);
    } else if (error.request) {
      // If no response received
      console.error('No response received:', error.request);
    } else {
      // Other errors
      console.error('Error message:', error.message);
    }
  }
};




const PackagesDataTable = () => {
  const { data: packages, isLoading, isError, error } = useQuery('packages', fetchPackages);
  const [isModalOpen, setIsModalOpen] = useState(false);

  if (isLoading) return <p className="text-center">Loading...</p>;
  if (isError) return <p className="text-center text-red-500">Error: {error.message}</p>;

  const purchasedPackage = packages.find(pkg => pkg.is_purchased);
  const otherPackages = packages.filter(pkg => !pkg.is_purchased);

  const getPlanStatus = (expiryDate) => {
    const now = new Date();
    const expiry = new Date(expiryDate);
    const remainingDuration = expiry - now;
    const remainingDays = Math.ceil(remainingDuration / (1000 * 60 * 60 * 24));

    if (remainingDuration <= 0) {
      return {
        color: 'red',
        message: 'Subscription Expired ❌',
        icon: '❌',
        remainingDays: 0,
      };
    }

    const totalDuration = expiry - new Date();
    const remainingPercentage = (remainingDuration / totalDuration) * 100;
    let statusMessage = '';

    if (remainingPercentage > 50) {
      statusMessage = 'Subscription Active - Safe Period ✅';
    } else if (remainingPercentage >= 20 && remainingPercentage <= 50) {
      statusMessage = 'Subscription is nearing its end ⚠️';
    } else {
      statusMessage = 'Subscription is about to expire ❌';
    }

    return {
      color: remainingPercentage > 50 ? 'green' : remainingPercentage >= 20 ? 'yellow' : 'red',
      message: `${statusMessage} (${remainingDays} days remaining)`,
      icon: remainingPercentage > 50 ? '✅' : remainingPercentage >= 20 ? '⚠️' : '❌',
      remainingDays,
    };
  };

  const expiryDate = purchasedPackage?.expiry_date;
  let planStatus = getPlanStatus(expiryDate);

  const actionsBodyTemplate = (rowData) => {
    const userType = localStorage.getItem('user_type');
    return (
      <div className="flex justify-around space-x-3">
        {userType === 'admin' && (
          <>
            <Tooltip target=".view-icon" content="View Details" position="top" />
            <Link to={`/package-details/${rowData.id}`} className="view-icon">
              <FaRegEye className="text-blue-600 hover:text-blue-800 transition duration-200" size={20} />
            </Link>

            <Tooltip target=".edit-icon" content="Edit" position="top" />
            <Link to={`/edit-package/${rowData.id}`} className="edit-icon">
              <FiEdit className="text-yellow-500 hover:text-yellow-700 transition duration-200" size={20} />
            </Link>

            <Tooltip target=".delete-icon" content="Delete" position="top" />
            <button
              className="delete-icon text-red-500 hover:text-red-700 transition duration-200"
              onClick={() => handleDelete(rowData.id)}
            >
              <TfiTrash className="text-red-500" size={20} />
            </button>
          </>
        )}

        {userType === 'manager' && (
          <button
            className={`main-btn text-md shadow-md px-5 py-2 rounded-lg transition duration-200 ${
              purchasedPackage ? "bg-orange-500 hover:bg-orange-600" : "bg-green-600 hover:bg-green-700"
            } text-white`}
            onClick={() => handleBuy(rowData.id)}
          >
            {purchasedPackage ? "Upgrade" : "Buy"}
          </button>
        )}
      </div>
    );
  };

  return (
    <Container>
      <div className="w-full flex flex-col items-center space-y-6">

        {purchasedPackage && (
          <div 
            className={`w-full max-w-2xl p-6 border-4 shadow-xl rounded-lg bg-white text-center border-${planStatus.color}-500 transform transition-all duration-500 hover:scale-105 hover:shadow-2xl`}
          >
            <h2 
              className={`text-3xl font-semibold text-${planStatus.color}-600 mb-4 animate__animated animate__fadeIn`}
            >
              Current Plan
            </h2>
            <p className="text-gray-700 mt-2 text-lg font-medium">{purchasedPackage.name}</p>
            <p className="text-gray-500 text-sm mt-1">Total Price: <span className="font-bold text-gray-800">${purchasedPackage.total_price}</span></p>
            <p className="text-gray-500 text-sm mt-1">Card Limit: <span className="font-bold text-gray-800">{purchasedPackage.card_limit}</span></p>
            <p 
              className={`text-${planStatus.color}-500 mt-4 font-semibold flex items-center justify-center animate__animated animate__fadeIn`}
            >
              {planStatus.icon} {planStatus.message}
            </p>
          </div>
        )}
  
        <div className="w-full flex justify-between">
          <h1 className="text-2xl font-semibold text-gray-700"></h1>

          <button
            className="main-btn text-md shadow-md px-5 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-200"
            onClick={() => setIsModalOpen(true)}
          >
            Create New Package
          </button>
        </div>

        <CreateBackageForm isModalOpen={isModalOpen} setIsModalOpen={setIsModalOpen} />
  
        <div className="w-full">
          <h1 className="text-2xl font-semibold text-gray-700 mb-4">Available Packages</h1>
          <DataTable 
            value={otherPackages} 
            paginator 
            rows={10} 
            className="shadow-lg rounded-lg bg-white" 
            responsiveLayout="scroll"
            paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
            currentPageReportTemplate="Showing {first} to {last} of {totalRecords} packages"
          >
            <Column field="name" header="Package Name" className="text-left" />
            <Column field="total_price" header="Total Price" className="text-left" />
            <Column field="card_limit" header="Card Limit" className="text-center" />
            <Column body={actionsBodyTemplate} header="Actions" />
          </DataTable>
        </div>
      </div>
    </Container>
  );
};

export default PackagesDataTable;



