import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { useState, useEffect } from "react";
import axiosInstance from "../../../../config/Axios";

import { useCreateGroupMutation } from "@quires";
import { getFormErrorMessage } from "@utils/helper";
import { groupStatusOptions } from "@constants/group";
import { useGlobalContext } from "@contexts/GlobalContext";

import { InputTextarea } from "primereact/inputtextarea";
import { classNames } from "primereact/utils";
import { InputText } from "primereact/inputtext";

import { Button } from "primereact/button";
import { Dropdown } from "primereact/dropdown";

function CreateGroupForm({ selectedUsers }) {
  const {
    control,
    formState: { errors },
    handleSubmit,
    reset,
  } = useForm();
  const { dialogHandler, disableBtn } = useGlobalContext();
  const createGroup = useCreateGroupMutation();
  const [cardTypes, setCardTypes] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchCardTypes = async () => {
      setLoading(true);
      try {
        const response = await axiosInstance.get("get-card-types");
        console.log("API Response:", response.data);
        console.log("Card Types Data Structure:", response.data.data);
        if (response.data.data && response.data.data.length > 0) {
          console.log("First Card Type Example:", response.data.data[0]);
        }
        setCardTypes(response.data.data || []);
      } catch (error) {
        console.error("Error fetching card types:", error);
        setCardTypes([]);
      } finally {
        setLoading(false);
      }
    };
    fetchCardTypes();
  }, []);

  const createHandler = async (formData) => {
    console.log("Form data before submission:", formData);
    console.log("Card Type ID from form:", formData.card_type_id);
    const userIds = selectedUsers.map((item) => item.id);
    const dataToSubmit = {
      ...formData,
      user_ids: userIds,
      print_status: "unprinted",
      group_type: "regular",
      parent_group_id: null,
    };
    console.log("Final data being sent:", dataToSubmit);
    console.log("Card Type ID in final data:", dataToSubmit.card_type_id);
    await createGroup.mutateAsync(
      dataToSubmit,
      { onSuccess: () => reset() }
    );
  };

  return (
    <form
      onSubmit={handleSubmit(createHandler)}
      className="w-full flex flex-col justify-center"
    >
      <div className="col-full flex flex-wrap  justify-start py-4 border-[gray]">
        {/* Name */}
        {/* <div className='w-6/12 mb-3 px-2'>
                    <div className="field ">
                        <label className="form-label text-sm"> Name </label>
                        <span className="p-float-label">
                            <Controller name="name" control={control}
                                rules={{ required: 'Name is required.' }}
                                render={({ field, fieldState }) => (
                                    <InputText
                                        id={field.name}
                                        {...field}
                                        autoFocus
                                        ref={field.ref}
                                        className={`w-100  ${classNames({ 'p-invalid': fieldState.invalid })}`}
                                    />
                                )} />
                        </span>
                        {getFormErrorMessage('name', errors)}
                    </div>
                </div> */}

        {/* Title */}
        <div className="w-full mb-3 px-2">
          <div className="field ">
            <label className="form-label text-sm"> Title </label>
            <span className="p-float-label">
              <Controller
                name="title"
                control={control}
                rules={{ required: "Title is required." }}
                render={({ field, fieldState }) => (
                  <InputText
                    id={field.name}
                    {...field}
                    ref={field.ref}
                    className={`w-100  ${classNames({
                      "p-invalid": fieldState.invalid,
                    })}`}
                  />
                )}
              />
            </span>
            {getFormErrorMessage("title", errors)}
          </div>
        </div>

        {/* Description */}
        <div className="w-full  mb-3 px-2">
          <div className="field ">
            <label className="form-label text-sm"> Description </label>
            <span className="p-float-label">
              <Controller
                name="description"
                control={control}
                rules={{ required: "Description is required." }}
                render={({ field, fieldState }) => (
                  <InputTextarea
                    rows={3}
                    cols={30}
                    id={field.name}
                    {...field}
                    ref={field.ref}
                    className={`w-100  ${classNames({
                      "p-invalid": fieldState.invalid,
                    })}`}
                  />
                )}
              />
            </span>
            {getFormErrorMessage("description", errors)}
          </div>
        </div>

        {/* Card Type */}
        <div className="w-full mb-3 px-2">
          <label htmlFor="" className="form-label text-sm">
            Card Type
          </label>
          <Controller
            name="card_type_id"
            control={control}
            rules={{ required: "Card type is required!" }}
            render={({ field, fieldState }) => {
              console.log("Current field value:", field.value);
              return (
                <Dropdown
                  id={field.name}
                  {...field}
                  value={field.value}
                  options={cardTypes}
                  onChange={(e) => {
                    console.log("Selected card type value:", e.value);
                    field.onChange(e.value);
                  }}
                  optionLabel="name"
                  optionValue="id"
                  placeholder="Select card type"
                  className={`w-100 ${classNames({
                    "p-invalid": fieldState.invalid,
                  })}`}
                  loading={loading}
                />
              );
            }}
          />
          {getFormErrorMessage("card_type_id", errors)}
        </div>

        {/* Status */}
        <div className="w-full mb-3 px-2">
          <label htmlFor="" className="form-label text-sm">
            Status
          </label>
          <Controller
            name="status"
            control={control}
            rules={{ required: "Status is required!" }}
            render={({ field, fieldState }) => (
              <Dropdown
                id={field.name}
                {...field}
                value={field.value}
                options={groupStatusOptions}
                onChange={(e) => {
                  field.onChange(e.value);
                }}
                optionLabel="label"
                optionValue="value"
                ref={field.ref}
                placeholder="select..."
                className={`w-100 ${classNames({
                  "p-invalid": fieldState.invalid,
                })}`}
              />
            )}
          />
          {getFormErrorMessage("status", errors)}
        </div>
      </div>

      <div className="col-full text-center flex items-end justify-start px-24 py-4">
        <Button
          label="Cancel"
          aria-label="Close"
          type="reset"
          className="gray-btn w-3/12  me-2 text-center"
          disabled={disableBtn || createGroup.isLoading}
          data-bs-dismiss="modal"
          onClick={() => dialogHandler("createGroup")}
        />

        <Button
          label="Create"
          aria-label="Add"
          type="submit"
          className="main-btn w-auto  ms-2 text-center"
          disabled={disableBtn}
          loading={createGroup.isLoading}
        />
      </div>
    </form>
  );
}

export default CreateGroupForm;
