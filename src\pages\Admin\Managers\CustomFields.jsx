import { Controller } from 'react-hook-form'
import { classNames } from 'primereact/utils'
import { InputText } from 'primereact/inputtext';
import _ from 'lodash';


import { getFormErrorMessage } from '@utils/helper'

export default function CustomFields({ numberOfInputs = 10, control, errors }) {
    return (
        _.times(numberOfInputs, (index) => {
            return <CustomField control={control} index={index} errors={errors} />;
        }))

}

function CustomField({ control, index, errors }) {
    index += 1
    const fieldLabel = `Custom Field ${index}`;
    const fieldName = `custom_field_${index}`;

    return (
        <div className='w-6/12 mb-3 px-2'>
            <div className="field ">
                <label className="form-label text-sm"> {fieldLabel} </label>
                <span className="p-float-label">
                    <Controller name={fieldName} control={control}
                        rules={{ required: true ? false : `${fieldLabel} is required.` }}
                        render={({ field, fieldState }) => (
                            <InputText
                                id={field.name}
                                {...field}
                                ref={field.ref}
                                className={`w-100  ${classNames({ 'p-invalid': fieldState.invalid })}`}
                            />
                        )} />
                </span>
                {getFormErrorMessage(fieldName, errors)}
            </div>
        </div>
    )
}


