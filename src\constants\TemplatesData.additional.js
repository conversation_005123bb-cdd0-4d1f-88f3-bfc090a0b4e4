// Additional professional templates for DesignSpace
// This file contains more templates to enhance the template library

// Professional Business Card Templates
export const professionalBusinessCards = [
  {
    id: 'pbc-001',
    name: 'Corporate Blue Card',
    width: 350,
    height: 200,
    thumbnail: 'https://placehold.co/350x200/1e3a8a/ffffff?text=Corporate+Blue',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 350,
        height: 200,
        backgroundColor: '#1e3a8a',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 20,
        y: 20,
        width: 310,
        height: 160,
        backgroundColor: '#1e3a8a',
        borderColor: '#ffffff',
        borderWidth: 1,
      },
      {
        id: 'el_3',
        type: 'text',
        x: 40,
        y: 50,
        width: 270,
        height: 40,
        value: 'ROBERT SMITH',
        fontSize: 22,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 40,
        y: 90,
        width: 270,
        height: 20,
        value: 'Chief Executive Officer',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#e0e0e0',
        textAlign: 'center',
      },
      {
        id: 'el_5',
        type: 'text',
        x: 40,
        y: 130,
        width: 270,
        height: 20,
        value: '<EMAIL> | +1 (555) 987-6543',
        fontSize: 12,
        fontWeight: 'normal',
        color: '#e0e0e0',
        textAlign: 'center',
      },
    ]
  },
  {
    id: 'pbc-002',
    name: 'Gold Luxury Card',
    width: 350,
    height: 200,
    thumbnail: 'https://placehold.co/350x200/1a1a1a/d4af37?text=Gold+Luxury',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 350,
        height: 200,
        backgroundColor: '#1a1a1a',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 10,
        height: 200,
        backgroundColor: '#d4af37',
      },
      {
        id: 'el_3',
        type: 'shape',
        shapeType: 'rectangle',
        x: 340,
        y: 0,
        width: 10,
        height: 200,
        backgroundColor: '#d4af37',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 30,
        y: 60,
        width: 290,
        height: 40,
        value: 'VICTORIA REYNOLDS',
        fontSize: 20,
        fontWeight: 'bold',
        color: '#d4af37',
        textAlign: 'center',
      },
      {
        id: 'el_5',
        type: 'text',
        x: 30,
        y: 100,
        width: 290,
        height: 20,
        value: 'Luxury Brand Consultant',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_6',
        type: 'text',
        x: 30,
        y: 130,
        width: 290,
        height: 20,
        value: '<EMAIL>',
        fontSize: 12,
        fontWeight: 'normal',
        color: '#d4af37',
        textAlign: 'center',
      },
    ]
  },
  {
    id: 'pbc-003',
    name: 'Gradient Tech Card',
    width: 350,
    height: 200,
    thumbnail: 'https://placehold.co/350x200/6d28d9/ffffff?text=Gradient+Tech',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 350,
        height: 200,
        backgroundColor: '#6d28d9',
        style: { background: 'linear-gradient(135deg, #6d28d9, #db2777)' },
      },
      {
        id: 'el_2',
        type: 'text',
        x: 30,
        y: 60,
        width: 290,
        height: 40,
        value: 'ALEX CHEN',
        fontSize: 24,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'left',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 30,
        y: 100,
        width: 290,
        height: 20,
        value: 'Full Stack Developer',
        fontSize: 16,
        fontWeight: 'normal',
        color: '#f0f0f0',
        textAlign: 'left',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 30,
        y: 130,
        width: 290,
        height: 20,
        value: '<EMAIL> | github.com/alexchen',
        fontSize: 12,
        fontWeight: 'normal',
        color: '#f0f0f0',
        textAlign: 'left',
      },
      {
        id: 'el_5',
        type: 'shape',
        shapeType: 'rectangle',
        x: 30,
        y: 155,
        width: 100,
        height: 3,
        backgroundColor: '#ffffff',
      },
    ]
  },
  {
    id: 'pbc-004',
    name: 'Minimal White Card',
    width: 350,
    height: 200,
    thumbnail: 'https://placehold.co/350x200/ffffff/333333?text=Minimal+White',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 350,
        height: 200,
        backgroundColor: '#ffffff',
      },
      {
        id: 'el_2',
        type: 'text',
        x: 30,
        y: 70,
        width: 290,
        height: 30,
        value: 'SARAH JOHNSON',
        fontSize: 18,
        fontWeight: 'bold',
        color: '#333333',
        textAlign: 'center',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 30,
        y: 100,
        width: 290,
        height: 20,
        value: 'Interior Designer',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#666666',
        textAlign: 'center',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 30,
        y: 130,
        width: 290,
        height: 20,
        value: '<EMAIL>',
        fontSize: 12,
        fontWeight: 'normal',
        color: '#999999',
        textAlign: 'center',
      },
    ]
  },
  {
    id: 'pbc-005',
    name: 'Green Nature Card',
    width: 350,
    height: 200,
    thumbnail: 'https://placehold.co/350x200/166534/ffffff?text=Green+Nature',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 350,
        height: 200,
        backgroundColor: '#166534',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'circle',
        x: 275,
        y: 25,
        width: 50,
        height: 50,
        backgroundColor: '#ffffff',
        opacity: 0.2,
      },
      {
        id: 'el_3',
        type: 'shape',
        shapeType: 'circle',
        x: 250,
        y: 50,
        width: 30,
        height: 30,
        backgroundColor: '#ffffff',
        opacity: 0.1,
      },
      {
        id: 'el_4',
        type: 'text',
        x: 30,
        y: 70,
        width: 290,
        height: 30,
        value: 'MICHAEL GREEN',
        fontSize: 20,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'left',
      },
      {
        id: 'el_5',
        type: 'text',
        x: 30,
        y: 100,
        width: 290,
        height: 20,
        value: 'Environmental Consultant',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#e0e0e0',
        textAlign: 'left',
      },
      {
        id: 'el_6',
        type: 'text',
        x: 30,
        y: 130,
        width: 290,
        height: 20,
        value: '<EMAIL> | (555) 123-4567',
        fontSize: 12,
        fontWeight: 'normal',
        color: '#e0e0e0',
        textAlign: 'left',
      },
    ]
  },
];

// Professional ID Badge Templates
export const professionalIdBadges = [
  {
    id: 'pid-001',
    name: 'Modern Corporate Badge',
    width: 300,
    height: 450,
    thumbnail: 'https://placehold.co/300x450/0f172a/ffffff?text=Modern+Corporate',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 300,
        height: 450,
        backgroundColor: '#ffffff',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 300,
        height: 100,
        backgroundColor: '#0f172a',
      },
      {
        id: 'el_3',
        type: 'text',
        x: 20,
        y: 35,
        width: 260,
        height: 30,
        value: 'GLOBAL ENTERPRISES',
        fontSize: 18,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
      {
        id: 'el_4',
        type: 'shape',
        shapeType: 'rectangle',
        x: 100,
        y: 120,
        width: 100,
        height: 100,
        backgroundColor: '#f0f0f0',
      },
      {
        id: 'el_5',
        type: 'text',
        x: 20,
        y: 240,
        width: 260,
        height: 40,
        value: 'JAMES WILSON',
        fontSize: 24,
        fontWeight: 'bold',
        color: '#0f172a',
        textAlign: 'center',
      },
      {
        id: 'el_6',
        type: 'text',
        x: 20,
        y: 280,
        width: 260,
        height: 30,
        value: 'Chief Technology Officer',
        fontSize: 16,
        fontWeight: 'normal',
        color: '#475569',
        textAlign: 'center',
      },
      {
        id: 'el_7',
        type: 'shape',
        shapeType: 'rectangle',
        x: 75,
        y: 320,
        width: 150,
        height: 1,
        backgroundColor: '#e2e8f0',
      },
      {
        id: 'el_8',
        type: 'text',
        x: 20,
        y: 340,
        width: 260,
        height: 20,
        value: 'ID: GE-CTO-2023',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#475569',
        textAlign: 'center',
      },
      {
        id: 'el_9',
        type: 'text',
        x: 20,
        y: 370,
        width: 260,
        height: 20,
        value: 'Access Level: Executive',
        fontSize: 14,
        fontWeight: 'normal',
        color: '#475569',
        textAlign: 'center',
      },
      {
        id: 'el_10',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 410,
        width: 300,
        height: 40,
        backgroundColor: '#0f172a',
      },
    ]
  }
];

// Export all additional templates
export const additionalTemplates = [
  ...professionalBusinessCards,
  ...professionalIdBadges
];
