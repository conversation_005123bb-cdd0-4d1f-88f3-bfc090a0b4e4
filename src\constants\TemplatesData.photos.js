// Photo Templates for DesignSpace
// This file contains professional photo templates and layouts

// Photo Layout Templates
export const photoLayoutTemplates = [
  {
    id: 'pl-001',
    name: 'Single Photo Layout',
    width: 1080,
    height: 1080,
    thumbnail: 'https://placehold.co/1080x1080/f8fafc/333333?text=Single+Photo',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1080,
        height: 1080,
        backgroundColor: '#f8fafc',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 90,
        y: 90,
        width: 900,
        height: 900,
        backgroundColor: '#e2e8f0',
        borderColor: '#ffffff',
        borderWidth: 10,
        shadow: true,
      },
      {
        id: 'el_3',
        type: 'text',
        x: 90,
        y: 1000,
        width: 900,
        height: 40,
        value: 'YOUR PHOTO TITLE',
        fontSize: 24,
        fontWeight: 'bold',
        color: '#333333',
        textAlign: 'center',
      },
    ]
  },
  {
    id: 'pl-002',
    name: 'Photo Collage - 2 Photos',
    width: 1080,
    height: 1080,
    thumbnail: 'https://placehold.co/1080x1080/f8fafc/333333?text=2+Photo+Collage',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1080,
        height: 1080,
        backgroundColor: '#f8fafc',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 90,
        y: 90,
        width: 430,
        height: 900,
        backgroundColor: '#e2e8f0',
        borderColor: '#ffffff',
        borderWidth: 10,
        shadow: true,
      },
      {
        id: 'el_3',
        type: 'shape',
        shapeType: 'rectangle',
        x: 560,
        y: 90,
        width: 430,
        height: 900,
        backgroundColor: '#e2e8f0',
        borderColor: '#ffffff',
        borderWidth: 10,
        shadow: true,
      },
      {
        id: 'el_4',
        type: 'text',
        x: 90,
        y: 1000,
        width: 900,
        height: 40,
        value: 'PHOTO COLLECTION',
        fontSize: 24,
        fontWeight: 'bold',
        color: '#333333',
        textAlign: 'center',
      },
    ]
  },
  {
    id: 'pl-003',
    name: 'Photo Collage - 3 Photos',
    width: 1080,
    height: 1080,
    thumbnail: 'https://placehold.co/1080x1080/f8fafc/333333?text=3+Photo+Collage',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1080,
        height: 1080,
        backgroundColor: '#f8fafc',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 90,
        y: 90,
        width: 900,
        height: 430,
        backgroundColor: '#e2e8f0',
        borderColor: '#ffffff',
        borderWidth: 10,
        shadow: true,
      },
      {
        id: 'el_3',
        type: 'shape',
        shapeType: 'rectangle',
        x: 90,
        y: 560,
        width: 430,
        height: 430,
        backgroundColor: '#e2e8f0',
        borderColor: '#ffffff',
        borderWidth: 10,
        shadow: true,
      },
      {
        id: 'el_4',
        type: 'shape',
        shapeType: 'rectangle',
        x: 560,
        y: 560,
        width: 430,
        height: 430,
        backgroundColor: '#e2e8f0',
        borderColor: '#ffffff',
        borderWidth: 10,
        shadow: true,
      },
      {
        id: 'el_5',
        type: 'text',
        x: 90,
        y: 1000,
        width: 900,
        height: 40,
        value: 'MEMORIES',
        fontSize: 24,
        fontWeight: 'bold',
        color: '#333333',
        textAlign: 'center',
      },
    ]
  },
  {
    id: 'pl-004',
    name: 'Photo Collage - 4 Photos',
    width: 1080,
    height: 1080,
    thumbnail: 'https://placehold.co/1080x1080/f8fafc/333333?text=4+Photo+Collage',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1080,
        height: 1080,
        backgroundColor: '#f8fafc',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 90,
        y: 90,
        width: 430,
        height: 430,
        backgroundColor: '#e2e8f0',
        borderColor: '#ffffff',
        borderWidth: 10,
        shadow: true,
      },
      {
        id: 'el_3',
        type: 'shape',
        shapeType: 'rectangle',
        x: 560,
        y: 90,
        width: 430,
        height: 430,
        backgroundColor: '#e2e8f0',
        borderColor: '#ffffff',
        borderWidth: 10,
        shadow: true,
      },
      {
        id: 'el_4',
        type: 'shape',
        shapeType: 'rectangle',
        x: 90,
        y: 560,
        width: 430,
        height: 430,
        backgroundColor: '#e2e8f0',
        borderColor: '#ffffff',
        borderWidth: 10,
        shadow: true,
      },
      {
        id: 'el_5',
        type: 'shape',
        shapeType: 'rectangle',
        x: 560,
        y: 560,
        width: 430,
        height: 430,
        backgroundColor: '#e2e8f0',
        borderColor: '#ffffff',
        borderWidth: 10,
        shadow: true,
      },
      {
        id: 'el_6',
        type: 'text',
        x: 90,
        y: 1000,
        width: 900,
        height: 40,
        value: 'PHOTO GALLERY',
        fontSize: 24,
        fontWeight: 'bold',
        color: '#333333',
        textAlign: 'center',
      },
    ]
  },
  {
    id: 'pl-005',
    name: 'Photo Frame - Polaroid',
    width: 1080,
    height: 1080,
    thumbnail: 'https://placehold.co/1080x1080/ffffff/333333?text=Polaroid+Frame',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1080,
        height: 1080,
        backgroundColor: '#f8fafc',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 290,
        y: 140,
        width: 500,
        height: 700,
        backgroundColor: '#ffffff',
        borderColor: '#e2e8f0',
        borderWidth: 1,
        shadow: true,
      },
      {
        id: 'el_3',
        type: 'shape',
        shapeType: 'rectangle',
        x: 315,
        y: 165,
        width: 450,
        height: 450,
        backgroundColor: '#e2e8f0',
      },
      {
        id: 'el_4',
        type: 'text',
        x: 315,
        y: 650,
        width: 450,
        height: 100,
        value: 'Memories\nJune 15, 2023',
        fontSize: 24,
        fontWeight: 'normal',
        color: '#333333',
        textAlign: 'center',
      },
    ]
  }
];

// Photo Frame Templates
export const photoFrameTemplates = [
  {
    id: 'pf-001',
    name: 'Modern Black Frame',
    width: 1080,
    height: 1080,
    thumbnail: 'https://placehold.co/1080x1080/000000/ffffff?text=Black+Frame',
    elements: [
      {
        id: 'el_1',
        type: 'shape',
        shapeType: 'rectangle',
        x: 0,
        y: 0,
        width: 1080,
        height: 1080,
        backgroundColor: '#000000',
      },
      {
        id: 'el_2',
        type: 'shape',
        shapeType: 'rectangle',
        x: 90,
        y: 90,
        width: 900,
        height: 900,
        backgroundColor: '#e2e8f0',
        borderColor: '#ffffff',
        borderWidth: 20,
      },
      {
        id: 'el_3',
        type: 'text',
        x: 90,
        y: 1000,
        width: 900,
        height: 40,
        value: 'CAPTURED MOMENTS',
        fontSize: 24,
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
      },
    ]
  }
];

// Export all photo templates
export const allPhotoTemplates = [
  ...photoLayoutTemplates,
  ...photoFrameTemplates
];
