import React, { useState } from 'react';
import LoadOnScroll from './LoadOnScroll';
import './ProfessionalImageGallery.css';

const PhotosSettings = () => {
    const [activeCategory, setActiveCategory] = useState('all');
    const [refetch, setRefetch] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');

    return (
        <div className="canva-photos">
            <h3 className="text-lg font-semibold mb-4 text-gray-800">Images</h3>

            {/* Search */}
            <div className="mb-4">
                <div className="relative">
                    <input
                        type="text"
                        placeholder="Search images..."
                        className="w-full p-2 pr-8 border border-gray-200 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <div className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                </div>
            </div>

            {/* Categories */}
            <div className="mb-4">
                <h4 className="text-sm font-medium mb-2 text-gray-700">Categories</h4>
                <div className="flex flex-wrap gap-2">
                    {[
                        { id: 'all', name: 'All Photos' },
                        { id: 'profile', name: 'Profile' },
                        { id: 'uploaded', name: 'Uploaded' },
                    ].map(category => (
                        <button
                            key={category.id}
                            onClick={() => {
                                setActiveCategory(category.id);
                                setRefetch(true);
                            }}
                            className={`px-3 py-1 text-xs rounded-full ${
                                activeCategory === category.id
                                ? 'bg-purple-600 text-white'
                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                            }`}
                        >
                            {category.name}
                        </button>
                    ))}
                </div>
            </div>

            {/* Professional Image Gallery based on selected category */}
            <div className="professional-library-section">
                <div className="professional-library-header">
                    <h4 className="professional-library-title">
                        <span className="professional-library-icon">📸</span>
                        Professional Photo Gallery
                    </h4>
                    <div className="professional-library-subtitle">
                        {activeCategory === 'all' ? 'All your beautiful photos' :
                         activeCategory === 'profile' ? 'Profile & avatar images' :
                         'Your uploaded photos'}
                    </div>
                </div>

                {activeCategory === 'all' && (
                    <LoadOnScroll fileType="image" refetch={refetch} setRefetch={setRefetch} />
                )}

                {activeCategory === 'profile' && (
                    <LoadOnScroll fileType="profile" refetch={refetch} setRefetch={setRefetch} />
                )}

                {activeCategory === 'uploaded' && (
                    <LoadOnScroll fileType="uploaded" refetch={refetch} setRefetch={setRefetch} />
                )}
            </div>
        </div>
    );
};

export default PhotosSettings;
