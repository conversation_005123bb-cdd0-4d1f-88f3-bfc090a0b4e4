import React, { useState, useRef, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Toast } from 'primereact/toast';
import { classNames } from 'primereact/utils';
import { InputText } from 'primereact/inputtext';
import { Button } from 'primereact/button';
import { Card } from 'primereact/card';
import SideImage from './SideImage';
import { useVerifyEmailMutation, useResendVerificationEmailMutation } from '../../quires';
import { getFormErrorMessage } from '@utils/helper';

const OTP_LENGTH = 6;
const OTP_EXPIRY_SECONDS = 600; // 10 minutes

function EmailVerification() {
  const { t } = useTranslation("auth");
  const { formState: { errors }, handleSubmit, control, setError, setValue, clearErrors } = useForm();
  const verifyEmail = useVerifyEmailMutation();
  const resendEmail = useResendVerificationEmailMutation();
  const toast = useRef(null);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [isResending, setIsResending] = useState(false);
  const [resendCountdown, setResendCountdown] = useState(0);
  const [otp, setOtp] = useState(Array(OTP_LENGTH).fill(''));
  const [activeInput, setActiveInput] = useState(0);
  const [expiry, setExpiry] = useState(Date.now() + OTP_EXPIRY_SECONDS * 1000);
  const [timeLeft, setTimeLeft] = useState(OTP_EXPIRY_SECONDS);
  const [expired, setExpired] = useState(false);

  // Get email and token from URL params
  const emailFromUrl = searchParams.get('email');
  const tokenFromUrl = searchParams.get('token');

  // OTP input refs
  const inputRefs = Array.from({ length: OTP_LENGTH }, () => useRef(null));

  // Set email and token from URL
  useEffect(() => {
    if (emailFromUrl) {
      setValue('email', emailFromUrl);
    }
    if (tokenFromUrl && tokenFromUrl.length === OTP_LENGTH) {
      setOtp(tokenFromUrl.split(''));
    }
  }, [emailFromUrl, tokenFromUrl, setValue]);

  // Countdown for resend button
  useEffect(() => {
    let timer;
    if (resendCountdown > 0) {
      timer = setTimeout(() => setResendCountdown(resendCountdown - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [resendCountdown]);

  // Countdown for OTP expiry
  useEffect(() => {
    let timer;
    if (!expired && timeLeft > 0) {
      timer = setTimeout(() => setTimeLeft(Math.max(0, Math.floor((expiry - Date.now()) / 1000))), 1000);
    } else if (timeLeft <= 0) {
      setExpired(true);
    }
    return () => clearTimeout(timer);
  }, [expiry, timeLeft, expired]);

  // Focus on next input when typing
  const handleOtpChange = (e, idx) => {
    const val = e.target.value.replace(/[^0-9]/g, '');
    if (!val) return;
    const newOtp = [...otp];
    newOtp[idx] = val[val.length - 1];
    setOtp(newOtp);
    if (idx < OTP_LENGTH - 1) {
      setActiveInput(idx + 1);
      inputRefs[idx + 1].current?.focus();
    }
    clearErrors('otp');
  };

  // Handle backspace
  const handleOtpKeyDown = (e, idx) => {
    if (e.key === 'Backspace') {
      if (otp[idx]) {
        const newOtp = [...otp];
        newOtp[idx] = '';
        setOtp(newOtp);
      } else if (idx > 0) {
        setActiveInput(idx - 1);
        inputRefs[idx - 1].current?.focus();
      }
    } else if (e.key === 'ArrowLeft' && idx > 0) {
      setActiveInput(idx - 1);
      inputRefs[idx - 1].current?.focus();
    } else if (e.key === 'ArrowRight' && idx < OTP_LENGTH - 1) {
      setActiveInput(idx + 1);
      inputRefs[idx + 1].current?.focus();
    }
  };

  // Handle paste
  const handleOtpPaste = (e) => {
    const pasted = e.clipboardData.getData('Text').replace(/[^0-9]/g, '').slice(0, OTP_LENGTH);
    if (pasted.length === OTP_LENGTH) {
      setOtp(pasted.split(''));
      setActiveInput(OTP_LENGTH - 1);
      inputRefs[OTP_LENGTH - 1].current?.focus();
      clearErrors('otp');
    }
    e.preventDefault();
  };

  // Submit
  const onSubmit = async (data) => {
    if (expired) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Verification code has expired. Please resend.',
        life: 4000
      });
      return;
    }
    if (otp.some((d) => !d)) {
      setError('otp', { type: 'manual', message: 'Please enter the full verification code.' });
      return;
    }
    try {
      const response = await verifyEmail.mutateAsync({ ...data, otp: otp.join('') });
      // Store values from response.user and response.token
      if (response && response.token && response.user) {
        localStorage.setItem('token', response.token);
        localStorage.setItem('user_id', response.user.id);
        localStorage.setItem('email', response.user.email);
        localStorage.setItem('user_name', response.user.name);
      }
      toast.current.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Email verified successfully! Redirecting... ',
        life: 2000
      });
      setTimeout(() => {
        navigate('/users/members');
      }, 1200);
    } catch (error) {
      if (error.response?.data?.errors) {
        const serverErrors = error.response.data.errors;
        Object.keys(serverErrors).forEach(field => {
          const message = serverErrors[field][0];
          setError(field, {
            type: 'manual',
            message: message,
          });
        });
      } else {
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: error.response?.data?.message || 'Failed to verify email',
          life: 3000
        });
      }
    }
  };

  // Resend code
  const handleResendEmail = async () => {
    const email = emailFromUrl || document.querySelector('input[name="email"]')?.value;
    if (!email) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Please enter your email address first',
        life: 3000
      });
      return;
    }
    setIsResending(true);
    try {
      await resendEmail.mutateAsync({ email });
      toast.current.show({
        severity: 'success',
        summary: 'Success',
        detail: 'New verification code has been sent to your email',
        life: 3000
      });
      setResendCountdown(60); // 60 seconds cooldown
      setExpiry(Date.now() + OTP_EXPIRY_SECONDS * 1000);
      setTimeLeft(OTP_EXPIRY_SECONDS);
      setExpired(false);
      setOtp(Array(OTP_LENGTH).fill(''));
      setActiveInput(0);
      inputRefs[0].current?.focus();
    } catch (error) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: error.response?.data?.message || 'Failed to resend verification code',
        life: 3000
      });
    } finally {
      setIsResending(false);
    }
  };

  // Format timer mm:ss
  const formatTime = (seconds) => {
    const m = Math.floor(seconds / 60).toString().padStart(2, '0');
    const s = (seconds % 60).toString().padStart(2, '0');
    return `${m}:${s}`;
  };

  return (
    <div className='w-full md:h-[100vh] overflow-hidden flex'>
      <Toast ref={toast} />
      <SideImage />
      <div className='w-full sm:w-7/12 h-full p-6 md:p-12 flex flex-col justify-center'>
        <Card className="shadow-lg border-0">
          <div className="text-center mb-6">
            <div className="mb-4">
              <i className="pi pi-envelope text-4xl text-blue-500"></i>
            </div>
            <h1 className='text-3xl font-bold text-gray-800 mb-2'>Email Verification</h1>
            <p className="text-gray-600">Please enter the 6-digit verification code sent to your email</p>
            <div className="flex justify-center gap-2 mt-6 mb-2">
              {otp.map((digit, idx) => (
                <InputText
                  key={idx}
                  ref={inputRefs[idx]}
                  value={digit}
                  onChange={e => handleOtpChange(e, idx)}
                  onKeyDown={e => handleOtpKeyDown(e, idx)}
                  onPaste={handleOtpPaste}
                  maxLength={1}
                  className={`otp-input w-12 h-14 text-2xl text-center border-2 rounded-lg shadow-sm ${classNames({ 'p-invalid': errors.otp })}`}
                  style={{ fontWeight: 'bold', fontSize: '2rem', background: expired ? '#f8d7da' : '#fff' }}
                  disabled={expired}
                  autoFocus={idx === activeInput}
                />
              ))}
            </div>
            {errors.otp && <div className="text-red-500 text-sm mt-1">{errors.otp.message}</div>}
            <div className="mt-2 text-gray-500 text-sm">
              Code expires in: <span className={expired ? 'text-red-500 font-bold' : 'font-bold'}>{formatTime(timeLeft)}</span>
            </div>
            {expired && <div className="text-red-600 font-semibold mt-2">Verification code has expired. Please resend.</div>}
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="field">
              <label className="form-label mb-2 text-gray-700 font-medium">Email Address</label>
              <Controller 
                name="email" 
                control={control}
                rules={{ 
                  required: 'Email is required',
                  pattern: {
                    value: /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
                    message: 'Please enter a valid email address',
                  }
                }}
                render={({ field, fieldState }) => (
                  <InputText
                    {...field}
                    placeholder="Enter your email address"
                    className={`w-full p-3 ${classNames({ 'p-invalid': fieldState.invalid })}`}
                    disabled={!!emailFromUrl}
                  />
                )} 
              />
              {getFormErrorMessage('email', errors)}
            </div>

            <Button
              type="submit"
              label={verifyEmail.isLoading ? 'Verifying...' : 'Verify Email'}
              icon={verifyEmail.isLoading ? 'pi pi-spin pi-spinner' : 'pi pi-check'}
              className="w-full p-3 text-lg"
              disabled={verifyEmail.isLoading || expired}
            />
          </form>

          <div className="mt-6 text-center">
            <p className="text-gray-600 mb-4">Didn't receive the code?</p>
            <Button
              label={
                resendCountdown > 0 
                  ? `Resend in ${resendCountdown}s` 
                  : isResending 
                    ? 'Sending...' 
                    : 'Resend Code'
              }
              icon={isResending ? 'pi pi-spin pi-spinner' : 'pi pi-refresh'}
              className="p-button-outlined p-button-secondary"
              onClick={handleResendEmail}
              disabled={resendCountdown > 0 || isResending}
            />
          </div>

          <div className="mt-6 text-center">
            <Button
              label="Back to Login"
              icon="pi pi-arrow-left"
              className="p-button-text"
              onClick={() => navigate('/login')}
            />
          </div>
        </Card>
      </div>
      <style>{`
        .otp-input::-webkit-input-placeholder { color: #bbb; }
        .otp-input:focus { border-color: #427bf0; box-shadow: 0 0 0 2px #427bf033; }
      `}</style>
    </div>
  );
}

export default EmailVerification; 