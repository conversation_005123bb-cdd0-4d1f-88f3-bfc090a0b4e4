import React, { useState } from 'react';
import { useDesignSpace } from '@contexts/DesignSpaceContext';
import { BsTable, BsGrid1X2, BsGrid3X3, BsGrid } from 'react-icons/bs';
import { MdOutlineTableRows, MdOutlineTableChart } from 'react-icons/md';
import { v4 as uuidv4 } from 'uuid';

// Table categories and items
const tableCategories = [
  {
    id: 'basic-tables',
    name: 'Basic Tables',
    icon: <BsTable size={20} />,
    items: [
      { id: 'table-2x2', name: '2×2 Table', icon: <BsGrid1X2 size={32} />, type: 'table', rows: 2, cols: 2 },
      { id: 'table-3x3', name: '3×3 Table', icon: <BsGrid3X3 size={32} />, type: 'table', rows: 3, cols: 3 },
      { id: 'table-4x4', name: '4×4 Table', icon: <BsGrid size={32} />, type: 'table', rows: 4, cols: 4 },
      { id: 'table-5x5', name: '5×5 Table', icon: <BsGrid size={32} />, type: 'table', rows: 5, cols: 5 },
    ]
  },
  {
    id: 'custom-tables',
    name: 'Custom Tables',
    icon: <MdOutlineTableChart size={20} />,
    items: [
      { id: 'table-2x3', name: '2×3 Table', icon: <BsGrid size={32} />, type: 'table', rows: 2, cols: 3 },
      { id: 'table-3x2', name: '3×2 Table', icon: <BsGrid size={32} />, type: 'table', rows: 3, cols: 2 },
      { id: 'table-4x2', name: '4×2 Table', icon: <BsGrid size={32} />, type: 'table', rows: 4, cols: 2 },
      { id: 'table-2x4', name: '2×4 Table', icon: <BsGrid size={32} />, type: 'table', rows: 2, cols: 4 },
    ]
  },
  {
    id: 'styled-tables',
    name: 'Styled Tables',
    icon: <MdOutlineTableRows size={20} />,
    items: [
      { id: 'table-modern', name: 'Modern', icon: <BsTable size={32} />, type: 'table', rows: 3, cols: 3, style: 'modern' },
      { id: 'table-minimal', name: 'Minimal', icon: <BsTable size={32} />, type: 'table', rows: 3, cols: 3, style: 'minimal' },
      { id: 'table-bordered', name: 'Bordered', icon: <BsTable size={32} />, type: 'table', rows: 3, cols: 3, style: 'bordered' },
      { id: 'table-striped', name: 'Striped', icon: <BsTable size={32} />, type: 'table', rows: 3, cols: 3, style: 'striped' },
    ]
  }
];

function TablesSettings() {
  const { addElement } = useDesignSpace();
  const [activeCategory, setActiveCategory] = useState('basic-tables');
  const [searchTerm, setSearchTerm] = useState('');

  // Get the active category data
  const activeCategoryData = tableCategories.find(cat => cat.id === activeCategory);

  // Filter items based on search term
  const filteredItems = activeCategoryData?.items.filter(item =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle adding a table to the canvas
  const handleAddTable = (item) => {
    // Create table element data
    const tableData = {
      id: uuidv4(),
      type: 'table',
      rows: item.rows,
      cols: item.cols,
      width: item.cols * 80,
      height: item.rows * 40,
      x: 100,
      y: 100,
      style: item.style || 'default',
      data: generateSampleData(item.rows, item.cols),
      backgroundColor: 'transparent',
      borderColor: '#d1d5db',
      textColor: '#374151'
    };

    // Add the table to the canvas
    addElement('table', '', tableData);
  };

  // Generate sample data based on rows and columns
  const generateSampleData = (rows, cols) => {
    const data = [];
    
    for (let i = 0; i < rows; i++) {
      const row = [];
      for (let j = 0; j < cols; j++) {
        if (i === 0) {
          // Header row
          row.push(`Header ${j+1}`);
        } else {
          // Data rows
          row.push(`Cell ${i},${j+1}`);
        }
      }
      data.push(row);
    }
    
    return data;
  };

  return (
    <div className="canva-tables">
      <h3 className="text-xl font-semibold mb-4 text-gray-800">Tables</h3>
      
      {/* Search */}
      <div className="mb-4">
        <div className="relative">
          <input
            type="text"
            placeholder="Search tables..."
            className="w-full p-3 pr-12 border border-gray-200 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>
      
      {/* Categories */}
      <div className="mb-6">
        <h4 className="text-md font-medium mb-4 text-gray-700">Categories</h4>
        <div className="grid grid-cols-3 gap-3 overflow-x-auto hide-scrollbar pb-2">
          {tableCategories.map(category => {
            // Define category colors
            let bgGradient = '';
            
            switch(category.id) {
              case 'basic-tables':
                bgGradient = 'from-blue-500 to-indigo-600';
                break;
              case 'custom-tables':
                bgGradient = 'from-indigo-500 to-purple-600';
                break;
              case 'styled-tables':
                bgGradient = 'from-purple-500 to-pink-600';
                break;
              default:
                bgGradient = 'from-gray-400 to-gray-500';
            }
            
            return (
              <div 
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`relative overflow-hidden rounded-xl cursor-pointer transition-all duration-300 ${
                  activeCategory === category.id 
                    ? 'ring-2 ring-offset-2 ring-purple-500 scale-105 shadow-lg' 
                    : 'hover:shadow-md hover:scale-105'
                }`}
              >
                {/* Background gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${bgGradient} ${activeCategory === category.id ? 'opacity-100' : 'opacity-80'}`}></div>
                
                {/* Content */}
                <div className="relative p-3 flex flex-col items-center justify-center h-20">
                  <span className="text-2xl mb-1 text-white">{category.icon}</span>
                  <span className="text-xs font-medium text-white text-center truncate w-full">
                    {category.name}
                  </span>
                </div>
                
                {/* Active indicator */}
                {activeCategory === category.id && (
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-white"></div>
                )}
              </div>
            );
          })}
        </div>
      </div>
      
      {/* Tables Grid */}
      <div className="grid grid-cols-2 gap-3">
        {filteredItems?.map(item => (
          <div
            key={item.id}
            className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-3 aspect-square flex flex-col items-center justify-center cursor-pointer hover:shadow-sm transition-all duration-200 border border-gray-200"
            onClick={() => handleAddTable(item)}
          >
            <div className="text-purple-600 mb-2 text-2xl">{item.icon}</div>
            <span className="text-xs font-medium text-center text-gray-700">{item.name}</span>
          </div>
        ))}
        
        {filteredItems?.length === 0 && (
          <div className="col-span-2 py-10 text-center text-gray-500">
            <div className="text-3xl mb-2">🔍</div>
            <div className="font-medium">No tables found</div>
            <div className="text-xs mt-1">Try a different search term or category</div>
          </div>
        )}
      </div>
    </div>
  );
}

export default TablesSettings;
