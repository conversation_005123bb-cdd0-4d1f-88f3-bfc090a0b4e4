// Templates data for different categories

export const socialMediaTemplates = [
    {
        id: 'social_1',
        name: 'Instagram Post 1',
        category: 'social',
        thumbnail: 'https://via.placeholder.com/300x300/FF5733/FFFFFF?text=Instagram+Post',
        width: 1080,
        height: 1080,
        elements: [
            {
                id: 'el_bg_1',
                type: 'img',
                x: 0,
                y: 0,
                width: 1080,
                height: 1080,
                value: 'https://images.unsplash.com/photo-1579546929518-9e396f3cc809?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxleHBsb3JlLWZlZWR8MXx8fGVufDB8fHx8&w=1000&q=80',
                borderRadius: 0,
            },
            {
                id: 'el_text_1',
                type: 'text',
                x: 50,
                y: 400,
                width: 980,
                height: 200,
                value: 'YOUR AMAZING HEADLINE',
                fontSize: '48px',
                fontFamily: 'Arial',
                textAlign: 'center',
                color: 'white',
                fontWeight: 'bold',
            },
            {
                id: 'el_text_2',
                type: 'text',
                x: 100,
                y: 600,
                width: 880,
                height: 100,
                value: 'Add your subheading or description here',
                fontSize: '24px',
                fontFamily: 'Arial',
                textAlign: 'center',
                color: 'white',
            },
        ]
    },
    {
        id: 'social_2',
        name: 'Facebook Cover',
        category: 'social',
        thumbnail: 'https://via.placeholder.com/820x312/3357FF/FFFFFF?text=Facebook+Cover',
        width: 820,
        height: 312,
        elements: [
            {
                id: 'el_bg_2',
                type: 'img',
                x: 0,
                y: 0,
                width: 820,
                height: 312,
                value: 'https://images.unsplash.com/photo-1557682250-33bd709cbe85?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8Z3JhZGllbnQlMjBiYWNrZ3JvdW5kfGVufDB8fDB8fA%3D%3D&w=1000&q=80',
                borderRadius: 0,
            },
            {
                id: 'el_text_3',
                type: 'text',
                x: 50,
                y: 100,
                width: 400,
                height: 100,
                value: 'YOUR BRAND NAME',
                fontSize: '36px',
                fontFamily: 'Arial',
                textAlign: 'left',
                color: 'white',
                fontWeight: 'bold',
            },
            {
                id: 'el_text_4',
                type: 'text',
                x: 50,
                y: 170,
                width: 400,
                height: 50,
                value: 'Your slogan or tagline here',
                fontSize: '18px',
                fontFamily: 'Arial',
                textAlign: 'left',
                color: 'white',
            },
        ]
    },
    {
        id: 'social_3',
        name: 'Twitter Post',
        category: 'social',
        thumbnail: 'https://via.placeholder.com/1200x675/00C4CC/FFFFFF?text=Twitter+Post',
        width: 1200,
        height: 675,
        elements: [
            {
                id: 'el_bg_3',
                type: 'img',
                x: 0,
                y: 0,
                width: 1200,
                height: 675,
                value: 'https://images.unsplash.com/photo-1579546929662-711aa81148cf?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxleHBsb3JlLWZlZWR8Mnx8fGVufDB8fHx8&w=1000&q=80',
                borderRadius: 0,
            },
            {
                id: 'el_text_5',
                type: 'text',
                x: 100,
                y: 250,
                width: 1000,
                height: 150,
                value: 'SHARE YOUR MESSAGE',
                fontSize: '60px',
                fontFamily: 'Arial',
                textAlign: 'center',
                color: 'white',
                fontWeight: 'bold',
            },
        ]
    },
];

export const presentationTemplates = [
    {
        id: 'pres_1',
        name: 'Business Presentation',
        category: 'presentation',
        thumbnail: 'https://via.placeholder.com/1920x1080/8B3DFF/FFFFFF?text=Business+Presentation',
        width: 1920,
        height: 1080,
        elements: [
            {
                id: 'el_bg_4',
                type: 'img',
                x: 0,
                y: 0,
                width: 1920,
                height: 1080,
                value: 'https://img.freepik.com/free-vector/gradient-network-connection-background_23-2148865392.jpg',
                borderRadius: 0,
            },
            {
                id: 'el_text_6',
                type: 'text',
                x: 100,
                y: 100,
                width: 1720,
                height: 150,
                value: 'PRESENTATION TITLE',
                fontSize: '72px',
                fontFamily: 'Arial',
                textAlign: 'left',
                color: 'white',
                fontWeight: 'bold',
            },
            {
                id: 'el_text_7',
                type: 'text',
                x: 100,
                y: 300,
                width: 800,
                height: 400,
                value: 'Add your main points here\n• Point 1\n• Point 2\n• Point 3',
                fontSize: '36px',
                fontFamily: 'Arial',
                textAlign: 'left',
                color: 'white',
            },
        ]
    },
    {
        id: 'pres_2',
        name: 'Creative Presentation',
        category: 'presentation',
        thumbnail: 'https://via.placeholder.com/1920x1080/FF5C5C/FFFFFF?text=Creative+Presentation',
        width: 1920,
        height: 1080,
        elements: [
            {
                id: 'el_bg_5',
                type: 'img',
                x: 0,
                y: 0,
                width: 1920,
                height: 1080,
                value: 'https://img.freepik.com/free-vector/colorful-abstract-background_23-2148810353.jpg',
                borderRadius: 0,
            },
            {
                id: 'el_text_8',
                type: 'text',
                x: 960,
                y: 400,
                width: 1000,
                height: 200,
                value: 'CREATIVE IDEAS',
                fontSize: '96px',
                fontFamily: 'Arial',
                textAlign: 'center',
                color: 'white',
                fontWeight: 'bold',
            },
            {
                id: 'el_text_9',
                type: 'text',
                x: 960,
                y: 600,
                width: 800,
                height: 100,
                value: 'Think outside the box',
                fontSize: '36px',
                fontFamily: 'Arial',
                textAlign: 'center',
                color: 'white',
            },
        ]
    },
];

export const printTemplates = [
    {
        id: 'print_1',
        name: 'Business Card',
        category: 'print',
        thumbnail: 'https://via.placeholder.com/1050x600/00C48C/FFFFFF?text=Business+Card',
        width: 1050,
        height: 600,
        elements: [
            {
                id: 'el_bg_6',
                type: 'img',
                x: 0,
                y: 0,
                width: 1050,
                height: 600,
                value: 'https://img.freepik.com/free-vector/elegant-white-background-with-shiny-lines_1017-17580.jpg',
                borderRadius: 0,
            },
            {
                id: 'el_text_10',
                type: 'text',
                x: 50,
                y: 200,
                width: 400,
                height: 100,
                value: 'JOHN DOE',
                fontSize: '36px',
                fontFamily: 'Arial',
                textAlign: 'left',
                color: '#333333',
                fontWeight: 'bold',
            },
            {
                id: 'el_text_11',
                type: 'text',
                x: 50,
                y: 250,
                width: 400,
                height: 50,
                value: 'CEO & Founder',
                fontSize: '18px',
                fontFamily: 'Arial',
                textAlign: 'left',
                color: '#666666',
            },
            {
                id: 'el_text_12',
                type: 'text',
                x: 50,
                y: 350,
                width: 400,
                height: 150,
                value: '<EMAIL>\n+1 234 567 890\nwww.example.com',
                fontSize: '16px',
                fontFamily: 'Arial',
                textAlign: 'left',
                color: '#666666',
            },
        ]
    },
    {
        id: 'print_2',
        name: 'Flyer',
        category: 'print',
        thumbnail: 'https://via.placeholder.com/2480x3508/FFB237/FFFFFF?text=Flyer+A4',
        width: 2480,
        height: 3508,
        elements: [
            {
                id: 'el_bg_7',
                type: 'img',
                x: 0,
                y: 0,
                width: 2480,
                height: 3508,
                value: 'https://img.freepik.com/free-vector/abstract-background-with-squares_23-2148995948.jpg',
                borderRadius: 0,
            },
            {
                id: 'el_text_13',
                type: 'text',
                x: 200,
                y: 500,
                width: 2080,
                height: 300,
                value: 'SPECIAL EVENT',
                fontSize: '120px',
                fontFamily: 'Arial',
                textAlign: 'center',
                color: 'white',
                fontWeight: 'bold',
            },
            {
                id: 'el_text_14',
                type: 'text',
                x: 400,
                y: 800,
                width: 1680,
                height: 200,
                value: 'Date: January 1, 2023\nLocation: Your Venue\nTime: 7:00 PM',
                fontSize: '48px',
                fontFamily: 'Arial',
                textAlign: 'center',
                color: 'white',
            },
        ]
    },
];

export const allTemplates = [
    ...socialMediaTemplates,
    ...presentationTemplates,
    ...printTemplates
];
