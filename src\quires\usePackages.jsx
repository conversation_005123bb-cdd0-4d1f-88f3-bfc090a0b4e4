import { useQuery } from 'react-query';
import axiosInstance from "../config/Axios"; // تأكد من أن المسار صحيح
import { useState, useEffect } from 'react';

const fetchPackages = async () => {
  try {
    const token = localStorage.getItem('token');

    if (!token) {
      console.error("Token not found in localStorage");
      return; 
    }

    const response = await axiosInstance.get('packages/show-all-packages', {
      headers: {
        Authorization: `Bearer ${token}`,
      }
    });

    return response.data;
  } catch (error) {
    console.error("Error fetching packages:", error);
    throw error;
  }
};

export const usePackages = () => {
  return useQuery('packages', fetchPackages);
};
