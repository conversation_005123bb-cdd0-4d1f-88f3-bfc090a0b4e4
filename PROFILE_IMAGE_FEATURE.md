# ميزة تغيير صورة الملف الشخصي

## نظرة عامة
تم إضافة ميزة جديدة لتغيير صورة الملف الشخصي للمستخدمين بشكل منفصل عن تحديث البيانات الأخرى.

## الميزات المضافة

### 1. واجهة المستخدم (Frontend)
- **موقع الميزة**: `src/pages/Dashboard/Setting/index.jsx`
- **الحالات الجديدة**:
  - `isEditingImage`: حالة منفصلة لتحرير الصورة
  - `imageLoading`: حالة التحميل أثناء رفع الصورة
  - `userImage`: تخزين مسار الصورة الحالية

### 2. الخادم (Backend)
- **الدالة الجديدة**: `uploadImage` في `UserController.php`
- **Route الجديد**: `POST /users/{id}/upload-image`

## كيفية الاستخدام

### للمستخدم:
1. انتقل إلى صفحة الإعدادات
2. انقر على أيقونة الكاميرا بجانب الصورة الشخصية
3. اختر "Choose File" لرفع صورة جديدة
4. انتظر اكتمال الرفع
5. ستظهر رسالة نجاح عند اكتمال العملية

### للمطور:
```javascript
// رفع صورة جديدة
const formData = new FormData();
formData.append('image', file);

const response = await axiosInstance.post(`/users/${userId}/upload-image`, formData, {
    headers: {
        'Content-Type': 'multipart/form-data',
    },
});
```

## التحقق من الصحة

### Frontend:
- نوع الملف: JPEG, PNG, JPG, GIF
- حجم الملف: أقل من 5MB
- عرض رسائل خطأ مناسبة

### Backend:
- التحقق من نوع الملف
- التحقق من حجم الملف
- حذف الصورة القديمة تلقائياً
- حفظ الصورة الجديدة في `storage/users/profile-images/`

## الملفات المعدلة

### Frontend:
- `src/pages/Dashboard/Setting/index.jsx`
  - إضافة حالة `isEditingImage`
  - إضافة دالة `handleImageUpload`
  - إضافة واجهة رفع الصورة
  - إزالة الاستيرادات غير المستخدمة

### Backend:
- `/var/www/inknull/current/app/Http/Controllers/UserController.php`
  - إضافة دالة `uploadImage`
- `/var/www/inknull/current/routes/api.php`
  - إضافة route `POST /users/{id}/upload-image`

## الميزات الإضافية

1. **تحديث تلقائي للتصاميم**: عند تغيير الصورة، يتم تحديث جميع تصاميم المستخدم تلقائياً
2. **إعادة تعيين حالة الطباعة**: يتم إعادة تعيين حالة الطباعة لجميع التصاميم
3. **معالجة الأخطاء**: رسائل خطأ واضحة ومفيدة
4. **واجهة مستخدم بديهية**: تصميم بسيط وسهل الاستخدام

## ملاحظات تقنية

- الصور تُحفظ في مجلد `storage/users/profile-images/`
- يتم حذف الصورة القديمة تلقائياً عند رفع صورة جديدة
- يتم استخدام FormData لرفع الملفات
- يتم التحقق من صحة الملف في كلا الجانبين (Frontend و Backend) 