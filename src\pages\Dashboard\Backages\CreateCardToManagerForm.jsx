import { useState, useEffect } from "react";
import { MultiSelect } from "primereact/multiselect";
import { Dialog } from "primereact/dialog";
import { Button } from "primereact/button";
import { useGlobalContext } from "@contexts/GlobalContext";

const CreateCardToManagerForm = ({ fetchPackages, packages, userId: passedUserId }) => {
  const { openDialog, dialogHandler } = useGlobalContext();
  const [formData, setFormData] = useState({
    name: "",
    number: "",
    type: null,
    card_type: null,
  });
  const [errors, setErrors] = useState({});
  const [allCardOptions, setAllCardOptions] = useState([]);
  const [selectedCards, setSelectedCards] = useState([]);
  const [loading, setLoading] = useState(false);
  const [userId, setUserId] = useState(passedUserId || null);
  useEffect(() => {
    setUserId(passedUserId || null);
  }, [passedUserId]);

  const groupCardsByType = (cards) => {
    const grouped = {};
  
    cards.forEach((card) => {
      const cardTypeName = card.card_type?.name || "Unknown";
  
      if (!grouped[cardTypeName]) {
        grouped[cardTypeName] = [];
      }
  
      grouped[cardTypeName].push({ label: card.number, value: card.id });
    });
  
    return Object.entries(grouped).map(([typeName, cards]) => ({
      label: `${typeName}`,
      items: cards,
    }));
  };
  

  useEffect(() => {
    const fetchAllCards = async () => {
      const backendUrl = import.meta.env.VITE_BACKEND_URL;
      const token = localStorage.getItem("token");

      if (!userId) {
        console.error("User ID is missing");
        return;
      }

      try {
        const response = await fetch(`${backendUrl}/all_cards?user_id=${userId}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          console.log("Fetched cards:", data);

          const groupedOptions = groupCardsByType(data); 
          setAllCardOptions(groupedOptions);
        } else {
          console.error("Failed to fetch cards");
        }
      } catch (error) {
        console.error("Error fetching cards:", error);
      }
    };

    fetchAllCards();
  }, [userId]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setErrors({});
  
    if (selectedCards.length === 0) {
      setErrors({ message: "Please select at least one card." });
      return;
    }
  
    const token = localStorage.getItem("token");
  
    if (!userId || !token) {
      console.error("User ID or Token is missing");
      return;
    }
  
    const firstSelectedCard = allCardOptions
      .flatMap(group => group.items)
      .find(item => selectedCards.includes(item.value));
  
    const requestData = {
      name: formData.name,
      number: formData.number,
      type: formData.type,
      user_id: userId,
      card_id: selectedCards,
      card_type: firstSelectedCard?.card_type?.name || null, 
    };

    const backendUrl = import.meta.env.VITE_BACKEND_URL;
    const requestUrl = `${backendUrl}/packages/add-cards`;

    try {
      setLoading(true);
      const response = await fetch(requestUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(requestData),
      });

      if (response.ok) {
        setFormData({ name: "", number: "", type: null });
        setSelectedCards([]);
        setErrors({});
        dialogHandler("CreateCardToManagerForm", false);
        if (fetchPackages) fetchPackages();
      } else {
        const errorData = await response.json();
        setErrors(errorData);
      }
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      visible={openDialog?.CreateCardToManagerForm || false}
      onHide={() => dialogHandler("CreateCardToManagerForm", false)}
      style={{ width: "50vw" }}
      modal
      className="p-fluid"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Multi-select with grouped cards */}
        <div className="p-field">
          <label htmlFor="allCards" className="block text-sm font-medium text-gray-600 mb-2">
            Select Cards (Multiple)
          </label>
          <MultiSelect
            id="allCards"
            value={selectedCards}
            options={allCardOptions}
            onChange={(e) => setSelectedCards(e.value)}
            optionLabel="label"
            optionGroupLabel="label"
            optionGroupChildren="items"
            placeholder="Select cards"
            display="chip"
            filter
            showClear
            selectionLimit={5}
            className="w-full"
            disabled={loading || allCardOptions.length === 0}
            optionGroupTemplate={(group) => (
                <div className="flex items-center justify-center w-full">
                  <div className="flex items-center w-full gap-2 text-gray-500 text-sm">
                    <span className="flex-grow border-t border-gray-300"></span>
                    <span className="whitespace-nowrap font-medium text-gray-600">{group.label}</span>
                    <span className="flex-grow border-t border-gray-300"></span>
                  </div>
                </div>
              )}
              
            />


          {allCardOptions.length === 0 && (
            <small className="text-red-500">No cards available in the system</small>
          )}
          {selectedCards.length > 0 && (
            <small className="text-blue-500">
              {selectedCards.length} card(s) selected
            </small>
          )}
        </div>

        {/* Submit Button */}
        <div className="flex justify-center mt-6">
          <Button
            type="submit"
            label={loading ? "Submitting..." : "Submit"}
            className="p-button-primary"
            disabled={loading || allCardOptions.length === 0}
            icon={loading ? "pi pi-spinner pi-spin" : "pi pi-check"}
          />
        </div>

        {errors.message && (
          <div className="p-4 bg-red-100 text-red-700 rounded mt-4">
            {errors.message}
          </div>
        )}
      </form>
    </Dialog>
  );
};

export default CreateCardToManagerForm;
