/* Additional rotation handles styles */

/* Rotation mode toggle button in element controls */
.element-control-btn.active-mode {
  background-color: rgba(138, 61, 255, 0.9) !important;
  color: white !important;
  box-shadow: 0 0 12px rgba(138, 61, 255, 0.7) !important;
  position: relative;
  overflow: visible;
}

/* Simple rotation icon */
.rotation-icon-simple {
  display: block;
  margin: 0 auto;
}

/* Active state for rotation icon */
.rotation-icon-simple.active {
  stroke: white;
  animation: rotate-simple 2s linear infinite;
}

@keyframes rotate-simple {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes rotate-icon {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse-glow {
  0% { opacity: 0.5; transform: translate(-50%, -50%) scale(0.8); }
  100% { opacity: 1; transform: translate(-50%, -50%) scale(1.2); }
}

/* Main rotation handle at the top */
.rotation-handle {
  position: absolute;
  width: 14px;
  height: 14px;
  background-color: #00c4cc;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 0.2),
    0 2px 4px rgba(0, 0, 0, 0.2);
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  cursor: grab;
  z-index: 10;
  transition: all 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  opacity: 0;
}

.selected .rotation-handle {
  opacity: 1;
}

/* Rotation icon inside the handle */
.rotation-handle::after {
  content: '↻';
  position: absolute;
  top: -5px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 10px;
  color: white;
}

/* Connecting line */
.rotation-handle::before {
  content: '';
  position: absolute;
  top: 14px;
  left: 50%;
  height: 10px;
  width: 1px;
  background-color: rgba(0, 0, 0, 0.3);
  transform: translateX(-50%);
}

/* Rotation cursor for resize handles */
.resize-handle.rotation-mode {
  cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="%23000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2"/></svg>') 12 12, auto;
  background-color: rgba(138, 61, 255, 0.7) !important; /* Purple background for rotation mode */
  border: 2px solid white !important;
  box-shadow: 0 0 8px rgba(138, 61, 255, 0.5) !important;
  transform: scale(1.2) !important;
}

/* Add animation to rotation mode handles */
.resize-handle.rotation-mode::before {
  content: '↻';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 10px;
  animation: rotate-icon 2s linear infinite;
}

@keyframes rotate-icon {
  from { transform: translate(-50%, -50%) rotate(0deg); }
  to { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Highlight resize handles when hovered for better visibility */
.resize-handle:hover {
  transform: scale(1.3);
  background-color: #00c4cc;
  opacity: 1;
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 0.2),
    0 3px 6px rgba(0, 0, 0, 0.3),
    0 0 10px rgba(0, 196, 204, 0.4);
}

/* Add rotation indicator to resize handles on right-click */
.resize-handle:active {
  background-color: #ff6b6b;
}

/* Mode indicator - vertical orientation */
.mode-indicator {
  position: absolute;
  top: 50%;
  right: -45px; /* Position closer to the element */
  transform: translateY(-50%); /* Center vertically */
  background-color: rgba(138, 61, 255, 0.9);
  color: white;
  padding: 3px 10px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  animation: pulse-opacity 1.5s infinite alternate;
  z-index: 100;
  white-space: nowrap;
  writing-mode: vertical-rl; /* Use vertical text instead of rotation */
  text-orientation: mixed; /* Keep text readable */
}

.mode-indicator::after {
  content: '';
  position: absolute;
  left: -4px; /* Arrow on the left side */
  top: 50%; /* Center vertically */
  transform: translateY(-50%) rotate(45deg);
  width: 8px;
  height: 8px;
  background-color: rgba(138, 61, 255, 0.9);
}

@keyframes pulse-opacity {
  0% { opacity: 0.8; }
  100% { opacity: 1; }
}
