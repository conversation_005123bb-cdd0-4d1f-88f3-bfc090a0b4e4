import { useDesignSpace } from '@contexts/DesignSpaceContext';
import { BlockUI } from 'primereact/blockui';


function ResizeInputs() {
    const { selectedIds, updateElement, elements, isMultiSelectActive } = useDesignSpace();

    const handleResizeInput = (field, value) => {
        if (selectedIds.length === 0) return;
        const id = selectedIds[0];
        const parsedValue = Math.max(1, parseInt(value, 10) || 0); // Ensure a minimum size of 20
        updateElement(id, { [field]: parsedValue });
    };

    return (

        <div className="flex items-center p-3 py-1 me-3 rounded-[6px]" onClick={(e) => e.stopPropagation()}>
            <div className="flex flex-col">
                <label className="mr-1 text-sm">Width</label>
                <BlockUI blocked={isMultiSelectActive} className='rounded-[6px]'>
                    <input
                        type="number"
                        min="1"
                        className="border border-[black] p-1 w-[70px] ps-2 rounded-[6px]"
                        value={
                            selectedIds.length === 1
                                ? elements.find((el) => el.id === selectedIds[0])?.width || ""
                                : ""
                        }
                        onChange={(e) => handleResizeInput("width", e.target.value)}
                    />
                </BlockUI>
            </div>
            <div className="flex flex-col mx-1">
                <label className="text-sm ">Height</label>
                <BlockUI blocked={isMultiSelectActive} className='rounded-[6px]' >
                    <input
                        type="number"
                        min="1"
                        className="border border-[black] p-1 w-[70px] ps-2 rounded-[6px] "
                        value={
                            selectedIds.length === 1
                                ? elements.find((el) => el.id === selectedIds[0])?.height || ""
                                : ""
                        }
                        onChange={(e) => handleResizeInput("height", e.target.value)}
                    />
                </BlockUI>
            </div>
        </div>
    )
}

export default ResizeInputs