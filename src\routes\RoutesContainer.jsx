import { Route, Routes } from 'react-router-dom'

import CompanyRoutes from './CompanyRoutes';
import AdminRoutes from './AdminRoutes';

import Registration from '@pages/Auth/Registration';
import Login from '@pages/Auth/Login';
import ForgotPassword from '@pages/Auth/ForgotPassword';
import ResetPassword from '@pages/Auth/ResetPassword';
import EmailVerification from '@pages/Auth/EmailVerification';
import { AuthChecker } from './AuthChecker';

function RoutesContainer() {
  return (
    <>
      <Routes >

        {/* Auth */}
        <Route element={<AuthChecker />}>
          <Route path='/' element={<Login />} />
          <Route path='/login' element={<Login />} />
          <Route path='/register' element={<Registration />} />
          <Route path='/verify-email' element={<EmailVerification />} />
          <Route path='/forget-password' element={<ForgotPassword />} />
          <Route path='/reset-password/:token' element={<ResetPassword />} />
        </Route>
        <Route path='/admin/*' element={<AdminRoutes />} />
        <Route path='/*' element={<CompanyRoutes />} />

        <Route path="*" element={<div>Page Not Found</div>} />
      </Routes>
    </>
  )
}

export default RoutesContainer