import React from 'react'
import { useDesignSpace } from '@contexts/DesignSpaceContext';
import { IoDuplicateOutline } from 'react-icons/io5';

function DuplicateControl() {
    const { selectedIds, setElements, elements } = useDesignSpace();

    const duplicateElement = () => {
        if (selectedIds.length > 0) {
            const element = elements.find((el) => el.id === selectedIds[0]); // Duplicate the first selected element
            const newElement = {
                ...element,
                id: Date.now().toString(),
                x: element.x + 20, // Offset the position so it doesn't overlap
                y: element.y + 20,
            };
            setElements((prev) => [...prev, newElement]);
        }
    };
    return (
        <button
            className="p-2 mx-1 "
            onClick={duplicateElement} disabled={selectedIds.length === 0}>
            <IoDuplicateOutline size={24} className="hover:text-[green] cursor-pointer" />
        </button>
    )
}

export default DuplicateControl