import { useState, useRef } from 'react';
import { MdOutlineUpload, MdClose } from 'react-icons/md';
import LoadOnScroll from './LoadOnScroll';
import { FileUpload } from 'primereact/fileupload';
import { useUploadMutation } from '@quires';

const UploadsSettings = () => {
    const [searchTerm, setSearchTerm] = useState('');
    const [refetch, setRefetch] = useState(true);
    const [preview, setPreview] = useState(null);
    const [selectedFile, setSelectedFile] = useState(null);
    const [isDragActive, setIsDragActive] = useState(false);
    const fileUploadRef = useRef(null);
    const uploadImage = useUploadMutation();

    // دالة آمنة لمسح الريفرانس
    const safeClear = () => {
        if (!preview && !isDragActive && fileUploadRef.current) {
            fileUploadRef.current.clear();
        }
    };

    // رفع الصورة
    const imageHandler = async (file) => {
        if (file) {
            setSelectedFile(file);
            const reader = new FileReader();
            reader.onload = (e) => {
                setPreview(e.target.result);
            };
            reader.readAsDataURL(file);
        }
    };

    // رفع الصورة فعليًا
    const handleUpload = async () => {
        if (!selectedFile) return;
        const formData = new FormData();
        formData.append("file", selectedFile);
        formData.append("file_type", "upload_image");
        formData.append("user_id", localStorage.getItem("user_id"));
        await uploadImage.mutateAsync(formData, {
            onSuccess: (data) => {
                if (data?.file_url) {
                    setPreview(null);
                    setSelectedFile(null);
                    safeClear();
                    setRefetch(true);
                } else {
                    console.error("No file_url received from upload");
                }
            }
        });
    };

    // إزالة المعاينة
    const removePreview = () => {
        setPreview(null);
        setSelectedFile(null);
        safeClear();
    };

    // تأثيرات CSS متقدمة
    const dragActiveClass = isDragActive
        ? 'bg-gradient-to-br from-purple-100/80 to-indigo-100/80 border-4 border-purple-400 shadow-2xl ring-4 ring-purple-300 animate-gradient-move backdrop-blur-md'
        : '';

    // زر الرفع
    const ButtonIcon = () => (
        <span
            className={`relative flex flex-col items-center justify-center mb-2 transition-transform duration-500 ${isDragActive ? 'animate-float' : ''}`}
            style={{ width: 110, height: 110 }}
        >
            {/* خلفية زجاجية مع ظل داخلي وخارجي */}
            <span
                className="absolute glass-bg"
                style={{
                    width: 110,
                    height: 110,
                    left: '50%',
                    top: '50%',
                    transform: 'translate(-50%, -50%)',
                    borderRadius: '32px',
                    zIndex: 1,
                }}
            />
            {/* Glow حول الأيقونة */}
            <span
                className="absolute animate-glow"
                style={{
                    width: 120,
                    height: 120,
                    left: '50%',
                    top: '50%',
                    transform: 'translate(-50%, -50%)',
                    borderRadius: '50%',
                    background: 'radial-gradient(circle, #a78bfa55 0%, #6366f122 80%, transparent 100%)',
                    zIndex: 2,
                    filter: 'blur(6px)',
                }}
            />
            {/* SVG أيقونة سحابة رفع ثلاثية الأبعاد */}
            <svg
                width="72"
                height="72"
                viewBox="0 0 72 72"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="relative z-10 drop-shadow-2xl"
            >
                <defs>
                    <linearGradient id="cloud3d" x1="0" y1="0" x2="72" y2="72" gradientUnits="userSpaceOnUse">
                        <stop stopColor="#a78bfa" />
                        <stop offset="1" stopColor="#6366f1" />
                    </linearGradient>
                    <linearGradient id="arrow3d" x1="36" y1="20" x2="36" y2="52" gradientUnits="userSpaceOnUse">
                        <stop stopColor="#fff" />
                        <stop offset="1" stopColor="#a78bfa" />
                    </linearGradient>
                </defs>
                {/* ظل السحابة */}
                <ellipse cx="36" cy="60" rx="18" ry="6" fill="#a78bfa22" />
                {/* جسم السحابة */}
                <path
                    d="M54 48H18C12.4772 48 8 43.5228 8 38C8 32.4772 12.4772 28 18 28C18.3813 28 18.7598 28.0182 19.1342 28.0536C20.2563 22.2452 26.2563 18 33 18C39.7437 18 45.7437 22.2452 46.8658 28.0536C47.2402 28.0182 47.6187 28 48 28C53.5228 28 58 32.4772 58 38C58 43.5228 53.5228 48 48 48Z"
                    fill="url(#cloud3d)"
                    stroke="#fff"
                    strokeWidth="2.5"
                    filter="drop-shadow(0 4px 16px #a78bfa55)"
                />
                {/* السهم */}
                <path
                    d="M36 44V28"
                    stroke="url(#arrow3d)"
                    strokeWidth="4"
                    strokeLinecap="round"
                />
                <path
                    d="M30 34L36 28L42 34"
                    stroke="url(#arrow3d)"
                    strokeWidth="4"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                />
            </svg>
        </span>
    );

    return (
        <div className="canva-uploads">
            <div
                className={`mb-8 relative transition-all duration-300 ${dragActiveClass}`}
                onDragOver={e => { e.preventDefault(); setIsDragActive(true); }}
                onDragLeave={e => { e.preventDefault(); setIsDragActive(false); }}
                onDrop={e => {
                    e.preventDefault();
                    setIsDragActive(false);
                    if (e.dataTransfer && e.dataTransfer.files && e.dataTransfer.files.length > 0) {
                        const file = e.dataTransfer.files[0];
                        imageHandler(file);
                        e.dataTransfer.clearData();
                    }
                }}
                style={{ minHeight: 200, borderRadius: 18 }}
            >
                {preview && (
                    <div className="absolute inset-0 z-10 bg-white/60 backdrop-blur-md rounded-2xl flex flex-col items-center justify-center transition-all duration-300" style={{ minHeight: 200 }}>
                        <button
                            className="absolute top-3 right-3 bg-white/80 rounded-full p-1 shadow hover:bg-red-100 transition z-20"
                            onClick={removePreview}
                            title="Remove preview"
                            type="button"
                        >
                            <MdClose size={22} className="text-gray-500 hover:text-red-500" />
                        </button>
                        <img
                            src={preview}
                            alt="Preview"
                            className="object-contain rounded-xl border-2 border-purple-200 shadow-2xl max-h-52 max-w-full mb-4 transition-all duration-300"
                            style={{ boxShadow: '0 8px 32px 0 rgba(139,61,255,0.18)' }}
                        />
                        <div className="flex gap-4">
                            <button
                                className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-2 rounded-xl hover:from-purple-700 hover:to-indigo-700 shadow-lg transition-all duration-300 text-base font-bold disabled:opacity-60"
                                onClick={handleUpload}
                                disabled={uploadImage.isLoading}
                            >
                                {uploadImage.isLoading ? 'Uploading...' : 'Upload'}
                            </button>
                            <button
                                className="bg-white/80 border border-gray-300 text-gray-700 px-6 py-2 rounded-xl hover:bg-gray-100 shadow transition-all duration-300 text-base font-bold"
                                onClick={removePreview}
                                disabled={uploadImage.isLoading}
                            >
                                Cancel
                            </button>
                        </div>
                    </div>
                )}
                {!preview && !isDragActive && (
                    <FileUpload
                        mode="basic"
                        name="image"
                        accept="image/*"
                        maxFileSize={500 * 1024}
                        customUpload
                        onSelect={(e) => imageHandler(e.files[0])}
                        ref={fileUploadRef}
                        onClick={safeClear}
                        className={`w-full glass-bg border-2 border-dashed border-gray-300 rounded-3xl flex flex-col items-center justify-center hover:bg-purple-50 transition-colors cursor-pointer min-h-[240px] relative z-0 shadow-xl`}
                        style={{ paddingTop: 36, paddingBottom: 18 }}
                        disabled={uploadImage.isLoading}
                        chooseOptions={{
                            icon: <ButtonIcon />,
                            label: uploadImage.isLoading ? 'Uploading...' : (
                                <span className="text-lg font-bold text-purple-700 tracking-wide">Drop your image here<br /><span className="text-base font-semibold text-indigo-500">or click to select</span></span>
                            ),
                            style: {
                                display: "flex",
                                flexDirection: "column",
                                background: 'transparent',
                                color: '#6B7280',
                                width: "100%",
                                border: "none",
                                fontWeight: "normal",
                                fontSize: "16px",
                                padding: 0,
                            }
                        }}
                    />
                )}
                {isDragActive && !preview && (
                    <div className="pointer-events-none absolute inset-0 z-10 flex flex-col items-center justify-center rounded-2xl animate-pulse bg-gradient-to-br from-purple-200/60 to-indigo-200/60 border-4 border-purple-400 shadow-2xl backdrop-blur-sm">
                        <MdOutlineUpload size={48} className="text-purple-500 animate-bounce mb-2 drop-shadow-xl" />
                        <span className="text-lg font-bold text-purple-700">Drop your image here!</span>
                    </div>
                )}
            </div>

            <div className="mb-4">
                <div className="relative">
                    <input
                        type="text"
                        placeholder="Search uploaded images..."
                        className="w-full p-2 pr-8 border border-gray-200 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <div className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                </div>
            </div>

            {/* Library Title with modern design */}
            <div className="flex items-center gap-4 mb-8">
                <span className="inline-flex items-center justify-center w-12 h-12 rounded-2xl bg-gradient-to-br from-purple-500 to-indigo-500 shadow-lg">
                    <MdOutlineUpload size={32} className="text-white drop-shadow" />
                </span>
                <div>
                    <h1 className="text-2xl font-extrabold text-gray-900 tracking-tight drop-shadow-sm leading-tight">
                        Uploads Library
                    </h1>
                    <p className="text-sm text-gray-500 font-medium mt-1">Manage and browse your uploaded images</p>
                </div>
            </div>

            <div className="flex flex-col justify-start">
                <LoadOnScroll fileType="uploaded" refetch={refetch} setRefetch={setRefetch} />
            </div>
        </div>
    );
};

export default UploadsSettings;

/*
CSS المطلوب (أضفه في ملف css):
.glass-bg {
  background: rgba(255,255,255,0.55);
  box-shadow: 0 8px 32px 0 rgba(99,102,241,0.12), 0 1.5px 8px 0 rgba(139,61,255,0.10) inset;
  backdrop-filter: blur(8px);
}
.animate-glow {
  animation: glowPulse 2.2s infinite alternate;
}
@keyframes glowPulse {
  0% { opacity: 0.7; filter: blur(6px); }
  100% { opacity: 1; filter: blur(12px); }
}
.animate-float {
  animation: floatUpDown 2.2s infinite ease-in-out;
}
@keyframes floatUpDown {
  0% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0); }
}
*/
