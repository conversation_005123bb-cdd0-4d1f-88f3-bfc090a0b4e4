// اختبار دالة استبدال المتغيرات

// بيانات المستخدم
const userData = {
    name: "أحمد محمد",
    type: "موظف",
    position: "مطور برمجيات",
    department: "تقنية المعلومات",
    custom_field_1: "رقم الهاتف: 0123456789",
    custom_field_2: "العنوان: القاهرة، مصر"
};

// دالة استبدال المتغيرات (نفس الدالة في Element.jsx)
const replaceVariables = (text, userData) => {
    if (!text || typeof text !== 'string') return text;
    
    return text.replace(/\{\{(\w+)\}\}/g, (match, variable) => {
        // Handle different variable formats
        const cleanVariable = variable.replace(/^\$/, ''); // Remove $ prefix if exists
        const replacement = userData[cleanVariable];
        
        // If we found a replacement value, return it without any brackets
        if (replacement !== undefined && replacement !== null) {
            return replacement;
        }
        
        // If no replacement found, return the variable name without brackets
        return cleanVariable;
    });
};

// اختبارات
console.log("=== اختبار استبدال المتغيرات ===");

// اختبار 1: متغير موجود
console.log("اختبار 1 - متغير موجود:");
console.log("المدخل: {{name}}");
console.log("النتيجة:", replaceVariables("{{name}}", userData));
console.log("المتوقع: أحمد محمد");
console.log("---");

// اختبار 2: حقل مخصص موجود
console.log("اختبار 2 - حقل مخصص موجود:");
console.log("المدخل: {{custom_field_1}}");
console.log("النتيجة:", replaceVariables("{{custom_field_1}}", userData));
console.log("المتوقع: رقم الهاتف: 0123456789");
console.log("---");

// اختبار 3: متغير غير موجود
console.log("اختبار 3 - متغير غير موجود:");
console.log("المدخل: {{custom_field_999}}");
console.log("النتيجة:", replaceVariables("{{custom_field_999}}", userData));
console.log("المتوقع: custom_field_999");
console.log("---");

// اختبار 4: نص مختلط
console.log("اختبار 4 - نص مختلط:");
console.log("المدخل: اسمي {{name}} وأعمل في {{department}}");
console.log("النتيجة:", replaceVariables("اسمي {{name}} وأعمل في {{department}}", userData));
console.log("المتوقع: اسمي أحمد محمد وأعمل في تقنية المعلومات");
console.log("---");

// اختبار 5: نص بدون متغيرات
console.log("اختبار 5 - نص بدون متغيرات:");
console.log("المدخل: هذا نص عادي");
console.log("النتيجة:", replaceVariables("هذا نص عادي", userData));
console.log("المتوقع: هذا نص عادي");
console.log("---");

// اختبار 6: نص فارغ
console.log("اختبار 6 - نص فارغ:");
console.log("المدخل: ''");
console.log("النتيجة:", replaceVariables("", userData));
console.log("المتوقع: ''");
console.log("---");

// اختبار 7: null
console.log("اختبار 7 - null:");
console.log("المدخل: null");
console.log("النتيجة:", replaceVariables(null, userData));
console.log("المتوقع: null");
console.log("---"); 