import React, { useState, useEffect } from 'react'
import { useQuery } from 'react-query'
import axiosInstance from "../../config/Axios";
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import './Dashboard.css';

// Icons
import {
  FiCreditCard,
  FiPackage,
  FiUsers,
  FiUser,
  FiActivity,
  FiCalendar,
  FiBarChart2,
  FiSettings,
  FiTrendingUp,
  FiAlertCircle,
  FiCheckCircle,
  FiInfo
} from 'react-icons/fi';

// Fetch admin's total cards count
const fetchCardsCount = async () => {
  try {
    const token = localStorage.getItem('token')
    const userId = localStorage.getItem('user_id')

    if (!token || !userId) {
      console.error("Token or user_id not found in localStorage")
      return 0
    }

    const response = await axiosInstance.get(`all_cards_admin`, {
      headers: {
        Authorization: `Bear<PERSON> ${token}`,
      }
    })

    return response.data.data?.total || 0
  } catch (error) {
    console.error('Error fetching cards count:', error)
    return 0
  }
}

// Fetch user's own cards count and package info
const fetchUserCardsCount = async () => {
  try {
    const token = localStorage.getItem('token')
    const userId = localStorage.getItem('user_id')

    if (!token || !userId) {
      console.error("Token or user_id not found in localStorage")
      return { count: 0, packageInfo: null }
    }

    const response = await axiosInstance.get(`packages/show-package-by-id/${userId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      }
    })

    // Return the length of the cards array and the package info
    console.log('User Cards:', response.data?.cards)
    console.log('Package Info:', response.data)

    return {
      count: response.data?.cards?.length || 0,
      packageInfo: response.data || null
    }
  } catch (error) {
    console.error('Error fetching user cards count:', error)
    return { count: 0, packageInfo: null }
  }
}

// Fetch members count using the same API as the members data table
const fetchMembersCount = async () => {
  try {
    const token = localStorage.getItem('token')
    const userId = localStorage.getItem('user_id')

    if (!token || !userId) {
      console.error("Token or user_id not found in localStorage")
      return 0
    }

    // Using the same API endpoint as the members data table
    const response = await axiosInstance.post(`/datatable/users/view?page=1&per_page=1&render_html=0`, {
      "order_by": {
        "id": "desc"
      },
      "filters": {},
      "filters_date": null
    }, {
      headers: {
        Authorization: `Bearer ${token}`,
      }
    })

    // Return the total count from pagination
    console.log('Members count:', response.data?.pagination?.total)
    return response.data?.pagination?.total || 0
  } catch (error) {
    console.error('Error fetching members count:', error)
    return 0
  }
}

// Fetch groups count
const fetchGroupsCount = async () => {
  try {
    const token = localStorage.getItem('token')
    const userId = localStorage.getItem('user_id')

    if (!token || !userId) {
      console.error("Token or user_id not found in localStorage")
      return 0
    }

    // Using the groups API endpoint with the correct URL
    const backendUrl = import.meta.env.VITE_BACKEND_URL
    const response = await fetch(`${backendUrl}/groups?users=true`, {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: "application/json",
      }
    })

    if (!response.ok) {
      console.error(`Error fetching groups: ${response.status}`)
      return 0
    }

    const data = await response.json()
    const groupsCount = data.data?.length || 0

    // Log the count
    console.log('Groups count:', groupsCount)
    return groupsCount
  } catch (error) {
    console.error('Error fetching groups count:', error)
    return 0
  }
}

const fetchAllPackages = async () => {
  try {
    const token = localStorage.getItem('token');
    const userId = localStorage.getItem('user_id');

    if (!token || !userId) {
      console.error("Token or user_id not found in localStorage");
      return [];
    }

    const backendUrl = import.meta.env.VITE_BACKEND_URL;

    const apiUrl = `${backendUrl}/packages/show-all-packages`;

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error(`Error fetching packages: ${response.status}`);
      const errorText = await response.text();
      console.error('Error Response Body:', errorText);
      return [];
    }

    const contentType = response.headers.get('Content-Type');
    if (contentType && contentType.includes('application/json')) {
      const data = await response.json();
      console.log('Packages Data:', data);
      return data || [];
    } else {
      const responseText = await response.text();
      console.warn('Received non-JSON response:', responseText);
      return [];
    }

  } catch (error) {
    console.error('Error fetching all packages:', error);
    return [];
  }
};

// Function to fetch latest cards for the current user
const fetchLatestCards = async () => {
  try {
    const token = localStorage.getItem('token')
    const userId = localStorage.getItem('user_id')

    if (!token || !userId) {
      console.error("Token or user_id not found in localStorage")
      return []
    }

    // Using the same API endpoint as used for user cards count
    const response = await axiosInstance.get(`packages/show-package-by-id/${userId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      }
    })

    // Get the user's cards from the response
    const userCards = response.data?.cards || []

    // Sort by created_at date if available, otherwise just take the first few
    const sortedCards = userCards.sort((a, b) => {
      if (a.created_at && b.created_at) {
        return new Date(b.created_at) - new Date(a.created_at)
      }
      return 0
    })

    // Return the latest 3 cards
    return sortedCards.slice(0, 3)
  } catch (error) {
    console.error('Error fetching latest user cards:', error)
    return []
  }
};

// Function to fetch all user cards (used for filtering printed/unprinted cards)
const fetchAllUserCards = async () => {
  try {
    const token = localStorage.getItem('token')
    const userId = localStorage.getItem('user_id')

    if (!token || !userId) {
      console.error("Token or user_id not found in localStorage")
      return []
    }

    // Using the same API endpoint as used for latest cards
    const response = await axiosInstance.get(`packages/show-package-by-id/${userId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      }
    })

    // Get the user's cards from the response
    const userCards = response.data?.cards || []

    // Sort by created_at date if available
    const sortedCards = userCards.sort((a, b) => {
      if (a.created_at && b.created_at) {
        return new Date(b.created_at) - new Date(a.created_at)
      }
      return 0
    })

    return sortedCards
  } catch (error) {
    console.error('Error fetching user cards:', error)
    return []
  }
};

function Dashboard() {
  const { data: adminCardsCount, isLoading: adminCardsLoading, isError: adminCardsError } = useQuery('adminCardsCount', fetchCardsCount)
  const { data: userCardsData, isLoading: userCardsLoading, isError: userCardsError } = useQuery('userCardsCount', fetchUserCardsCount)
  const { data: allPackages, isLoading: packagesLoading, isError: packagesError } = useQuery('allPackages', fetchAllPackages)
  const { data: membersCount, isLoading: membersLoading, isError: membersError } = useQuery('membersCount', fetchMembersCount)
  const { data: groupsCount, isLoading: groupsLoading, isError: groupsError } = useQuery('groupsCount', fetchGroupsCount)
  const { data: latestCards, isLoading: latestCardsLoading, isError: latestCardsError } = useQuery('latestCards', fetchLatestCards)
  // Use the same API for all card lists
  const { data: allUserCards, isLoading: allUserCardsLoading, isError: allUserCardsError } = useQuery('allUserCards', fetchAllUserCards)
  const [greeting, setGreeting] = useState('');
  const [currentTime, setCurrentTime] = useState('');

  // Extract user cards count and package info
  const userCardsCount = userCardsData?.count || 0;
  const userPackageInfo = userCardsData?.packageInfo || null;
  const cardLimit = userPackageInfo?.card_limit || 0;

  // Calculate active packages count
  const activePackagesCount = allPackages?.filter(pkg => pkg.status === 'active').length || 0

  // Filter cards from the same data source (remove slice to show all cards)
  const printedCards = allUserCards?.filter(card => card.print_status === 'printed') || []
  const unprintedCards = allUserCards?.filter(card =>
    !card.print_status || card.print_status === 'unPrinted' || card.print_status === 'pending'
  ) || []

  // Calculate time-based greeting
  useEffect(() => {
    const updateGreeting = () => {
      const hour = new Date().getHours();
      let greetingText = '';

      if (hour < 12) greetingText = 'Good Morning';
      else if (hour < 18) greetingText = 'Good Afternoon';
      else greetingText = 'Good Evening';

      setGreeting(greetingText);
    };

    const updateTime = () => {
      const now = new Date();
      const options = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      };
      setCurrentTime(now.toLocaleDateString(undefined, options));
    };

    updateGreeting();
    updateTime();

    const interval = setInterval(updateTime, 60000); // Update time every minute

    return () => clearInterval(interval);
  }, []);



  if (userCardsLoading || adminCardsLoading || packagesLoading || membersLoading || groupsLoading || latestCardsLoading || allUserCardsLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="animate-pulse flex flex-col items-center">
          <div className="w-16 h-16 border-4 border-t-transparent border-[#00c3ac] rounded-full animate-spin"></div>
          <p className="mt-4 text-gray-600">Loading dashboard data...</p>
        </div>
      </div>
    );
  }

  if (userCardsError || adminCardsError || packagesError || membersError || groupsError || latestCardsError || allUserCardsError) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline"> Unable to load dashboard data. Please try again later.</span>
        </div>
      </div>
    );
  }

  return (
    <section className='w-full flex flex-col rounded-[6px] min-h-full '>
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full bg-gradient-to-r from-[#00c3ac] to-[#02aa96] p-6 rounded-[10px] shadow-lg mb-6 text-white"
      >
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">{greeting}, {localStorage.getItem("user_name") || "User"}</h1>
            <p className="text-white/80 mt-1">{currentTime}</p>
          </div>
          <div className="hidden md:flex space-x-2">
            <Link to="/manager/settings" className="bg-white/20 hover:bg-white/30 p-2 rounded-full transition-all">
              <FiSettings size={20} />
            </Link>
            <Link to="/manager/billing" className="bg-white/20 hover:bg-white/30 p-2 rounded-full transition-all">
              <FiActivity size={20} />
            </Link>
          </div>
        </div>
      </motion.div>

      {/* Stats Cards */}
      <div className="w-full grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className="dashboard-white-container p-5 rounded-[10px] shadow-md border border-gray-200 border-l-4 border-l-[#00c3ac] hover:shadow-lg transition-all"
        >
          <div className="flex items-start justify-between">
            <div>
              <p className="text-gray-500 text-sm font-medium">Your Cards</p>
              <h3 className="text-2xl font-bold mt-2">{userCardsCount}</h3>
            </div>
            <div className="bg-[#00c3ac]/10 p-3 rounded-full">
              <FiCreditCard size={20} className="text-[#00c3ac]" />
            </div>
          </div>
          <div className="mt-4 text-xs flex items-center text-green-600">
            <FiInfo className="mr-1" />
            <span>{unprintedCards.length} Unprinted Cards </span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="dashboard-white-container p-5 rounded-[10px] shadow-md border border-gray-200 border-l-4 border-l-blue-500 hover:shadow-lg transition-all"
        >
          <div className="flex items-start justify-between">
            <div>
              <p className="text-gray-500 text-sm font-medium">Active Packages</p>
              <h3 className="text-2xl font-bold mt-2">{activePackagesCount}</h3>
            </div>
            <div className="bg-blue-500/10 p-3 rounded-full">
              <FiPackage size={20} className="text-blue-500" />
            </div>
          </div>
          <div className="mt-4 text-xs flex items-center text-blue-600">
            {activePackagesCount === 0 ? (
              <>
                <FiAlertCircle className="mr-1" />
                <span>No active packages. You can purchase one
                  <Link to="/manager/Packages">
                    <span className="text-blue-600 hover:underline"> here</span>
                  </Link>
                </span>
              </>
            ) : (
              <>
                <FiCheckCircle className="mr-1" />
                <span>All packages running smoothly</span>
              </>
            )}
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: 0.3 }}
          className="dashboard-white-container p-5 rounded-[10px] shadow-md border border-gray-200 border-l-4 border-l-purple-500 hover:shadow-lg transition-all"
        >
          <div className="flex items-start justify-between">
            <div>
              <p className="text-gray-500 text-sm font-medium">Active Members</p>
              <h3 className="text-2xl font-bold mt-2">{membersCount || 0}</h3>
            </div>
            <div className="bg-purple-500/10 p-3 rounded-full">
              <FiUser size={20} className="text-purple-500" />
            </div>
          </div>
          {/* <div className="mt-4 text-xs flex items-center text-purple-600">
            <FiTrendingUp className="mr-1" />
            <span>{membersCount ? `${membersCount} active members` : 'No members yet'}</span>
          </div> */}
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: 0.4 }}
          className="dashboard-white-container p-5 rounded-[10px] shadow-md border border-gray-200 border-l-4 border-l-green-500 hover:shadow-lg transition-all"
        >
          <div className="flex items-start justify-between">
            <div>
              <p className="text-gray-500 text-sm font-medium">Groups</p>
              <h3 className="text-2xl font-bold mt-2">{groupsCount || 0}</h3>
            </div>
            <div className="bg-green-500/10 p-3 rounded-full">
              <FiUsers size={20} className="text-green-500" />
            </div>
          </div>
          {/* <div className="mt-4 text-xs flex items-center text-green-600">
            <FiCheckCircle className="mr-1" />
            <span>{groupsCount ? `${groupsCount} active groups` : 'No groups created yet'}</span>
          </div> */}
        </motion.div>
      </div>

      {/* Main Content Area - Three Card Lists */}
      <div className="w-full grid grid-cols-1 lg:grid-cols-3 md:grid-cols-2 gap-6">
        {/* Printed Cards Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="dashboard-white-container p-5 border border-gray-200 rounded-[10px] shadow-md"
        >
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-bold text-gray-700">Printed Cards</h2>
            <span className="text-green-600 text-sm font-medium bg-green-50 px-2 py-1 rounded-full">
              {printedCards.length} cards
            </span>
          </div>

          {printedCards && printedCards.length > 0 ? (
            <div className="space-y-4 max-h-96 overflow-y-auto pr-2">
              {printedCards.map((card) => (
                <div key={card.id} className="flex items-start space-x-3 pb-3 border-b border-gray-100 last:border-b-0">
                  <div className="p-2 rounded-full bg-green-600/10 text-green-600 flex-shrink-0">
                    <FiCreditCard />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex justify-between items-start">
                      <p className="text-sm font-medium truncate pr-2">{card.name || 'Unnamed Card'}</p>
                      <p className="text-xs text-gray-500 flex-shrink-0">{card.number || 'No Number'}</p>
                    </div>
                    <p className="text-xs text-gray-500 truncate">Type: {card.card_type?.name || 'Unknown Type'}</p>
                    <p className="text-xs text-gray-500 truncate">Template: {card.template?.name || card.design_template || 'Default'}</p>
                    <p className="text-xs text-gray-500 truncate">Group: {card.group?.title || card.group_name || 'No Group'}</p>
                    <p className="text-xs text-gray-400 mt-1">
                      Printed {card.printed_at
                        ? new Date(card.printed_at).toLocaleDateString(undefined, {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric'
                          })
                        : 'Date unknown'}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-48 text-gray-500">
              <FiCreditCard size={32} className="mb-2" />
              <p className="text-sm">No printed cards</p>
            </div>
          )}
        </motion.div>

        {/* Unprinted Cards Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="dashboard-white-container p-5 border border-gray-200 rounded-[10px] shadow-md"
        >
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-bold text-gray-700">Unprinted Cards</h2>
            <span className="text-orange-600 text-sm font-medium bg-orange-50 px-2 py-1 rounded-full">
              {unprintedCards.length} cards
            </span>
          </div>

          {unprintedCards && unprintedCards.length > 0 ? (
            <div className="space-y-4 max-h-96 overflow-y-auto pr-2">
              {unprintedCards.map((card) => (
                <div key={card.id} className="flex items-start space-x-3 pb-3 border-b border-gray-100 last:border-b-0">
                  <div className="p-2 rounded-full bg-orange-600/10 text-orange-600 flex-shrink-0">
                    <FiCreditCard />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex justify-between items-start">
                      <p className="text-sm font-medium truncate pr-2">{card.name || 'Unnamed Card'}</p>
                      <p className="text-xs text-gray-500 flex-shrink-0">{card.number || 'No Number'}</p>
                    </div>
                    <p className="text-xs text-gray-500 truncate">Type: {card.card_type?.name || 'Unknown Type'}</p>
                    <p className="text-xs text-gray-500 truncate">Template: {card.template?.name || card.design_template || 'Default'}</p>
                    <p className="text-xs text-gray-500 truncate">Group: {card.group?.title || card.group_name || 'No Group'}</p>
                    <p className="text-xs text-gray-400 mt-1">
                      Created {card.created_at
                        ? new Date(card.created_at).toLocaleDateString(undefined, {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric'
                          })
                        : 'Date unknown'}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-48 text-gray-500">
              <FiCreditCard size={32} className="mb-2" />
              <p className="text-sm">No unprinted cards</p>
            </div>
          )}
        </motion.div>

        {/* Latest Cards Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="dashboard-white-container p-5 border border-gray-200 rounded-[10px] shadow-md"
        >
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-bold text-gray-700">Latest Cards</h2>
            <Link to="/manager/my_cards" className="text-[#00c3ac] text-sm hover:underline">
              View All
            </Link>
          </div>

          {latestCards && latestCards.length > 0 ? (
            <>
              <div className="space-y-4">
                {latestCards.map((card) => (
                  <div key={card.id} className="flex items-start space-x-3 pb-3 border-b border-gray-100 last:border-b-0">
                    <div className="p-2 rounded-full bg-[#00c3ac]/10 text-[#00c3ac] flex-shrink-0">
                      <FiCreditCard />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex justify-between items-start">
                        <p className="text-sm font-medium truncate pr-2">{card.name || 'Unnamed Card'}</p>
                        <p className="text-xs text-gray-500 flex-shrink-0">{card.number || 'No Number'}</p>
                      </div>
                      <p className="text-xs text-gray-500 truncate">Type: {card.card_type?.name || 'Unknown Type'}</p>
                      <p className="text-xs text-gray-500 truncate">Template: {card.template?.name || card.design_template || 'Default'}</p>
                      <p className="text-xs text-gray-500 truncate">Group: {card.group?.title || card.group_name || 'No Group'}</p>
                      <p className="text-xs text-gray-400 mt-1">
                        Last update {card.created_at
                          ? new Date(card.updated_at).toLocaleDateString(undefined, {
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric'
                            })
                          : 'Date unknown'}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              {/* Divider */}
              <div className="my-2 border-t border-gray-200"></div>

              {/* Card Usage Progress Section */}
              {cardLimit > 0 && (
                <div className="mt-2">
                  <div className="flex justify-between items-center mb-3">
                    <h3 className="text-sm font-medium text-gray-700">Card Usage</h3>
                    <p className="text-xs font-medium text-[#00c3ac] bg-transparent px-2 py-1 rounded-full border border-[#00c3ac]">
                      {userCardsCount}/{cardLimit}
                    </p>
                  </div>
                  <div className="w-full h-4 bg-gray-100 rounded-full overflow-hidden border border-gray-200 shadow-inner">
                    <div
                      className="h-full bg-gradient-to-r from-[#00c3ac] to-[#82f570] rounded-full transition-all duration-500 shadow-sm"
                      style={{ width: `${Math.min((userCardsCount / cardLimit) * 100, 100)}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    {cardLimit - userCardsCount > 0
                      ? `You have ${cardLimit - userCardsCount} cards remaining in your package`
                      : 'You have reached your card limit'}
                  </p>
                </div>
              )}
            </>
          ) : (
            <div className="flex flex-col items-center justify-center h-48 text-gray-500">
              <FiCreditCard size={32} className="mb-2" />
              <p className="text-sm">No cards available</p>
              <Link to="/manager/Packages" className="mt-2 text-[#00c3ac] hover:underline text-sm">
                Purchase a package to access cards
              </Link>
            </div>
          )}
        </motion.div>
      </div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
        className="dashboard-white-container w-full p-5 border border-gray-200 rounded-[10px] shadow-md mt-6"
      >
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-bold text-gray-700">Quick Actions</h2>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Link
            to="/users/groups"
            className="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-all duration-300 hover:-translate-y-2 hover:shadow-md"
          >
            <div className="p-3 bg-green-500/10 rounded-full mb-2">
              <FiUsers size={24} className="text-green-500" />
            </div>
            <span className="text-sm font-medium text-gray-700">Manage Groups</span>
          </Link>

          <Link
            to="/users/members"
            className="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-all duration-300 hover:-translate-y-2 hover:shadow-md"
          >
            <div className="p-3 bg-purple-500/10 rounded-full mb-2">
              <FiUser size={24} className="text-purple-500" />
            </div>
            <span className="text-sm font-medium text-gray-700">Manage Members</span>
          </Link>

          <Link
            to="/manager/Packages"
            className="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-all duration-300 hover:-translate-y-2 hover:shadow-md"
          >
            <div className="p-3 bg-blue-500/10 rounded-full mb-2">
              <FiPackage size={24} className="text-blue-500" />
            </div>
            <span className="text-sm font-medium text-gray-700">View Packages</span>
          </Link>

          <Link
            to="/manager/billing"
            className="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-all duration-300 hover:-translate-y-2 hover:shadow-md"
          >
            <div className="p-3 bg-amber-500/10 rounded-full mb-2">
              <FiCalendar size={24} className="text-amber-500" />
            </div>
            <span className="text-sm font-medium text-gray-700">Billing History</span>
          </Link>
        </div>
      </motion.div>
    </section>
  )
}

export default Dashboard