import React, { useState, useEffect } from "react";
import { Dialog } from "primereact/dialog";
import { InputText } from "primereact/inputtext";
import { InputNumber } from "primereact/inputnumber";
import { But<PERSON> } from "primereact/button";
import { MultiSelect } from "primereact/multiselect";
import { ProgressSpinner } from "primereact/progressspinner";
import { Divider } from "primereact/divider";
import axiosInstance from "../../../config/Axios";

const PackageFormModal = ({ 
  isModalOpen, 
  setIsModalOpen, 
  packageToEdit = null,
  onSuccess 
}) => {
  const [formData, setFormData] = useState({
    name: "",
    total_price: null,
    card_limit: null,
    type: [],
    subscription_duration: null,
    monthly_price: null,
    yearly_discount: null,
  });

  const [errors, setErrors] = useState({});
  const [cardTypes, setCardTypes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    const fetchCardTypes = async () => {
      setLoading(true);
      try {
        const backendUrl = import.meta.env.VITE_BACKEND_URL;
        const response = await axiosInstance.get(
          `${backendUrl}/packages/card_type_index`
        );

        if (response.status === 200) {
          setCardTypes(response.data.data);
        } else {
          console.error("Failed to fetch card types");
          setErrors({
            message: "Failed to load card types. Please try again.",
          });
        }
      } catch (error) {
        console.error("Error while fetching card types:", error);
        setErrors({ message: "Network error. Please check your connection." });
      } finally {
        setLoading(false);
      }
    };

    fetchCardTypes();
  }, []);

  useEffect(() => {
    if (packageToEdit && isModalOpen) {
      console.log("Package to edit:", packageToEdit); 
      setFormData({
        name: packageToEdit.name || "",
        total_price: packageToEdit.total_price || null,
        card_limit: packageToEdit.card_limit || null,
        type: packageToEdit.card_types || [], 
        subscription_duration: packageToEdit.subscription_duration || null,
        monthly_price: packageToEdit.monthly_price || null,
        yearly_discount: packageToEdit.yearly_discount || null,
      });
    } else if (!packageToEdit && isModalOpen) {
      setFormData({
        name: "",
        total_price: null,
        card_limit: null,
        type: [],
        subscription_duration: null,
        monthly_price: null,
        yearly_discount: null,
      });
    }
  }, [packageToEdit, isModalOpen]);

  const handleChange = (name, value) => {
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const calculateYearlyPrice = () => {
    const monthly = formData.monthly_price || 0;
    const discount = formData.yearly_discount || 0;
    const yearly = monthly * 12;
    return yearly - yearly * (discount / 100);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);

    let missingFields = [];
    if (!formData.name) missingFields.push("Package Name");
    if (!formData.monthly_price) missingFields.push("Monthly Price");
    if (!formData.card_limit) missingFields.push("Card Limit");
    if (formData.type.length === 0) missingFields.push("Card Type");

    if (missingFields.length > 0) {
      setErrors({ message: `Required fields: ${missingFields.join(", ")}` });
      setSubmitting(false);
      return;
    }

    const requestData = {
      name: formData.name,
      monthly_price: formData.monthly_price,
      card_limit: formData.card_limit,
      yearly_discount: formData.yearly_discount || 0,
      card_type_id: formData.type.map((type) => type.id),
      subscription_duration: formData.subscription_duration || null,
      total_price: formData.total_price || 0,
    };

    console.log("Request payload:", requestData);

    const token = localStorage.getItem("token");
    const backendUrl = import.meta.env.VITE_BACKEND_URL;
    const isEditing = !!packageToEdit;
    const requestUrl = isEditing 
      ? `${backendUrl}/packages/${packageToEdit.id}`
      : `${backendUrl}/packages`;

    try {
      const response = await fetch(requestUrl, {
        method: isEditing ? "PUT" : "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(requestData),
      });

      const responseData = await response.json();

      if (!response.ok) {
        console.error("Full error response:", responseData);
        setErrors({
          message: responseData.message || "Validation failed",
          details: responseData.errors,
        });
        return;
      }

      setFormData({
        name: "",
        total_price: null,
        card_limit: null,
        type: [],
        subscription_duration: null,
        monthly_price: null,
        yearly_discount: null,
      });
      
      setIsModalOpen(false);
      setErrors({});
      
      if (onSuccess) {
        onSuccess(responseData.data, isEditing ? 'updated' : 'created');
      }
    } catch (error) {
      console.error("Network error:", error);
      setErrors({ message: "Network error. Please check your connection." });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Dialog
      visible={isModalOpen}
      onHide={() => !submitting && setIsModalOpen(false)}
      header={
        <div className="text-xl font-semibold text-gray-800">
          {packageToEdit ? "Edit Package" : "Create New Package"}
        </div>
      }
      style={{ width: "60vw", minWidth: "300px" }}
      modal
      className="p-fluid"
      closeOnEscape={!submitting}
      closable={!submitting}
    >
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <ProgressSpinner />
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-5 p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
            {/* Package Name */}
            <div className="space-y-2">
              <label
                htmlFor="name"
                className="block text-sm font-medium text-gray-700"
              >
                Package Name <span className="text-red-500">*</span>
              </label>
              <InputText
                id="name"
                value={formData.name}
                onChange={(e) => handleChange("name", e.target.value)}
                placeholder="e.g. Premium Package"
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>

            {/* Card Limit */}
            <div className="space-y-2">
              <label
                htmlFor="card_limit"
                className="block text-sm font-medium text-gray-700"
              >
                Card Limit <span className="text-red-500">*</span>
              </label>
              <InputNumber
                id="card_limit"
                value={formData.card_limit}
                onValueChange={(e) => handleChange("card_limit", e.value)}
                placeholder="e.g. 5"
                min={1}
                className="w-full border-gray-300 rounded-lg"
                required
              />
            </div>

            {/* Yearly Discount */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label
                  htmlFor="yearly_discount"
                  className="block text-sm font-medium text-gray-700"
                >
                  Annual Discount
                </label>
                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                  Percentage
                </span>
              </div>
              <div className="p-inputgroup">
                <InputNumber
                  id="yearly_discount"
                  value={formData.yearly_discount}
                  onValueChange={(e) =>
                    handleChange("yearly_discount", e.value)
                  }
                  mode="decimal"
                  min={0}
                  max={100}
                  placeholder="0-100"
                  className="w-full border-gray-300 rounded-r-lg"
                />
                <span className="p-inputgroup-addon bg-gray-100 border border-l-0 border-gray-300 rounded-r-lg">
                  %
                </span>
              </div>
            </div>

            {/* Monthly Price */}
            <div className="space-y-2">
              <label
                htmlFor="monthly_price"
                className="block text-sm font-medium text-gray-700"
              >
                Monthly Price <span className="text-red-500">*</span>
              </label>
              <div className="p-inputgroup">
                <span className="p-inputgroup-addon bg-gray-100 border border-r-0 border-gray-300 rounded-l-lg">
                  $
                </span>
                <InputNumber
                  id="monthly_price"
                  value={formData.monthly_price}
                  onValueChange={(e) => handleChange("monthly_price", e.value)}
                  placeholder="0.00"
                  mode="currency"
                  currency="USD"
                  locale="en-US"
                  className="w-full border-gray-300 rounded-r-lg"
                  required
                />
              </div>
            </div>

            {/* Card Type */}
            <div className="space-y-2 md:col-span-2">
              <label
                htmlFor="type"
                className="block text-sm font-medium text-gray-700"
              >
                Card Types <span className="text-red-500">*</span>
              </label>
              <MultiSelect
                value={formData.type}
                options={cardTypes}
                onChange={(e) => handleChange("type", e.value)}
                optionLabel="name"
                placeholder="Select Card Types"
                filter
                display="chip"
              />
            </div>

            {/* Calculated Yearly Price */}
            <div className="space-y-2">
              <label
                htmlFor="calculated_price"
                className="block text-sm font-medium text-gray-700"
              >
                Annual Price
              </label>
              <div className="p-inputgroup">
                <span className="p-inputgroup-addon bg-gray-100 border border-r-0 border-gray-300 rounded-l-lg">
                  $
                </span>
                <InputText
                  id="calculated_price"
                  value={calculateYearlyPrice().toFixed(2)}
                  readOnly
                  className="w-full bg-gray-50 border-gray-300 rounded-r-lg font-medium"
                />
              </div>
            </div>
          </div>

          <Divider />

          {/* Error Message */}
          {errors.message && (
            <div className="p-4 bg-red-50 border border-red-200 text-red-600 rounded-lg">
              <div className="font-bold">{errors.message}</div>
              {errors.details && (
                <ul className="mt-2 list-disc list-inside">
                  {Object.entries(errors.details).map(([field, messages]) => (
                    <li key={field}>
                      {field}: {messages.join(", ")}
                    </li>
                  ))}
                </ul>
              )}
            </div>
          )}

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-2">
            <Button
              type="button"
              label="Cancel"
              className="p-button-outlined p-button-secondary"
              onClick={() => setIsModalOpen(false)}
              disabled={submitting}
            />
            <Button
              type="submit"
              label={submitting ? "Processing..." : (packageToEdit ? "Update Package" : "Create Package")}
              className="p-button-primary"
              icon={submitting ? "pi pi-spinner pi-spin" : "pi pi-check"}
              disabled={submitting}
            />
          </div>
        </form>
      )}
    </Dialog>
  );
};

export default PackageFormModal;