import { CiGrid41 } from "react-icons/ci";
import { LiaIdCardSolid } from "react-icons/lia";
import { CiSettings } from "react-icons/ci";
import { HiOutlineTemplate } from "react-icons/hi";
import { BsCalendar4Event } from "react-icons/bs";
import { TbIcons, TbUser, TbUsers } from 'react-icons/tb';
import { PiBuildingOfficeLight } from "react-icons/pi";
import { LiaLockSolid } from "react-icons/lia";
import { LuPackageOpen } from "react-icons/lu";
import { LuPackageCheck } from "react-icons/lu";



export const _menuTabs = {
    
    //Menu items/tabs for each user type 

    manager: [
        {
            title: "Dashboard",
            icon: <CiGrid41 size={20} />,
            route: "/manager/dashboard",
            onlyAdmin: false,
            activeKey: "dashboard"
        },
        {
            title: "Companies",
            icon: <PiBuildingOfficeLight size={20} />,
            route: "/manager/companies",
            onlyAdmin: true,
            activeKey: "companies"
        },
        {
            title: "Members",
            icon: <TbUser size={20} />,
            route: "/users/members",
            onlyAdmin: false,
            activeKey: "members"
        },
 

        {
            title: "Groups",
            icon: <TbUsers size={20} />,
            route: "/users/groups",
            onlyAdmin: false,
            activeKey: "groups"
        },
        // {
        //   title: "Events",
        //   icon: <BsCalendar4Event size={20} />,
        //   route: "/manager/events",
        // onlyAdmin: false,
        // activeKey: "events"
        // },
        {
            title: "Templates",
            icon: <HiOutlineTemplate size={20} />,
            route: "/manager/template-design",
            onlyAdmin: false,
            activeKey: "template-design"
        },
        {
            title: "Design Space",
            icon: <TbIcons size={20} />,
            route: "/manager/design-space",
            onlyAdmin: false,
            activeKey: "design-space"
        },
        // {
        //   title: "Settings",
        //   icon: <CiSettings size={20} />,
        //   route: "/manager/settings",
        // onlyAdmin: false,
        // activeKey: "settings"
        // },

        {
            title: "Packages",
            icon: <LuPackageOpen  size={20} />,
            route: "/manager/Packages",
            onlyAdmin: false,
            activeKey: "Packages"

        },

        {
            title: "Cards",
            icon: <LiaIdCardSolid size={20} />,
            route: "/manager/my_cards",                 // Page for both cards and purchased packages
            onlyAdmin: false,
            activeKey: "cards"

        },


        

    ],
    admin: [
        {
            title: "Dashboard",
            icon: <CiGrid41 size={20} />,
            route: "/admin/dashboard",
            onlyAdmin: true,
            activeKey: "dashboard"
        },
        {
            title: "Companies",
            icon: <PiBuildingOfficeLight size={20} />,
            route: "/admin/companies",
            onlyAdmin: true,
            activeKey: "companies"
        },
        {
            title: "Managers",
            icon: <TbUser size={20} />,
            route: "/managers",
            onlyAdmin: true,
            activeKey: "managers"
        },
        // {
        //     title: "Groups",
        //     icon: <TbUsers size={20} />,
        //     route: "/users/groups",
        //     onlyAdmin: false,
        //     activeKey: "groups"
        // },
        // {
        //   title: "Events",
        //   icon: <BsCalendar4Event size={20} />,
        //   route: "/admin/events",
        // onlyAdmin: false,
        // activeKey: "events"
        // },
        // {
        //     title: "Templates",
        //     icon: <HiOutlineTemplate size={20} />,
        //     route: "/admin/template-design",
        //     onlyAdmin: true,
        //     activeKey: "template-design"
        // },
        // {
        //     title: "Design Space",
        //     icon: <TbIcons size={20} />,
        //     route: "/design-space",
        //     onlyAdmin: true,
        //     activeKey: "design-space"
        // },
        // {
        //   title: "Settings",
        //   icon: <CiSettings size={20} />,
        //   route: "/admin/settings",
        // onlyAdmin: false,
        // activeKey: "settings"
        // },

        
        {
            title: "Card Types",
            icon: <LiaIdCardSolid size={20} />,
            route: "/admin/c_types",
            onlyAdmin: true,
            activeKey: "c_types"

        },



        {
            title: "Permissions Groups",
            icon: <LiaLockSolid  size={20} />,
            route: "/admin/permissions_groups",
            onlyAdmin: false,
            activeKey: "permissions_groups"

        },

        {
            title: "Packages",
            icon: <LuPackageOpen  size={20} />,
            route: "/admin/Packages",
            onlyAdmin: false,
            activeKey: "Packages"

        },


        {
            title: "Sold Packages",
            icon: <LuPackageCheck  size={20} />,
            route: "/admin/sold_P",
            onlyAdmin: false,
            activeKey: "sold_P"

        },

        {
            title: "Cards",
            icon: <LiaIdCardSolid size={20} />,
            route: "/admin/cards",
            onlyAdmin: false,
            activeKey: "cards"

        },

        
    ],
    user: [
        {
            title: "Overview",
            icon: <CiGrid41 size={20} />,
            route: "/user/dashboard",
            onlyAdmin: false,
            activeKey: "dashboard"
        },
       
        {
            title: "Members",
            icon: <TbUser size={20} />,
            route: "/users/members",
            onlyAdmin: false,
            activeKey: "members"
        },




        {
            title: "Design Space",
            icon: <TbIcons size={20} />,
            route: "/user/design-space",
            onlyAdmin: false,
            activeKey: "design-space"
        },
        
        
    ]
    
}


