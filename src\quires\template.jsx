import { useMutation, useQuery } from 'react-query';
import axiosInstance from '../config/Axios';

import { useGlobalContext } from '@contexts/GlobalContext';
import { handleErrors } from '@utils/helper';

//--------------Get All designs-------------- //
const getDesigns = async (payload) => {
  const { data } = await axiosInstance.get(`/designs?per_page=50`, payload);

  return data.data;
}

export const useGetDesigns = () => {
  const { showToast } = useGlobalContext();
  let { isLoading, data, error, isError } = useQuery('getDesigns', getDesigns);

  if (isError) {
    showToast("error", "Fetch Designs ", error.response?.data?.message)
  }

  return { isLoading, data };
}


//--------------Create template-------------- //
const createTemplate = async (payload) => {
  console.log("Creating template with payload:", {
    name: payload.name,
    hasTemplate: !!payload.template,
    templateLength: payload.template ? payload.template.length : 0,
    hasInitTemplate: !!payload.init_template,
    cardTypeId: payload.card_type_id,
    background: payload.background,
    backgroundStyle: payload.background_style
  });

  // Ensure template is not empty
  if (!payload.template || payload.template.trim() === '') {
    payload.template = '<div>Default Template Content</div>';
    console.warn("Template was empty, using default content");
  }

  // Log the exact payload being sent to the server
  console.log("Full payload being sent to server:", JSON.stringify(payload));

  const { data } = await axiosInstance.post("/designs", payload);

  console.log("Template created successfully:", data);
  return data.data;
}

export const useCreateTemplateMutation = () => {
  const { showToast } = useGlobalContext();

  return useMutation(createTemplate, {
    onSuccess: async () => {
      showToast("success", "Success", "Design template created successfully!")
    },
    onError: (error) => {
      handleErrors(showToast, error)
    }
  })
}


//--------------Update template-------------- //
const updateTemplate = async (payload) => {
  console.log("Updating template with payload:", {
    id: payload?.id,
    hasFormData: payload.data instanceof FormData,
    formDataKeys: payload.data instanceof FormData ? [...payload.data.keys()] : null,
    background: payload.data instanceof FormData ? payload.data.get('background') : null,
    backgroundStyle: payload.data instanceof FormData ? payload.data.get('background_style') : null
  });

  // If using FormData, ensure template is not empty
  if (payload.data instanceof FormData) {
    const template = payload.data.get('template');
    if (!template || (typeof template === 'string' && template.trim() === '')) {
      payload.data.set('template', '<div>Default Template Content</div>');
      console.warn("Template was empty in FormData, using default content");
    }

    // Log all FormData entries for debugging
    console.log("FormData entries being sent to server:");
    for (let [key, value] of payload.data.entries()) {
      if (typeof value === 'string') {
        console.log(`${key}: ${value.length > 100 ? value.substring(0, 100) + '...' : value}`);
      } else {
        console.log(`${key}: [non-string value]`);
      }
    }
  }

  const { data } = await axiosInstance.post(`/designs/${payload?.id}`, payload.data);

  console.log("Template updated successfully:", data);
  return data.data;
}

export const useUpdateTemplateMutation = () => {
  const { showToast } = useGlobalContext();

  return useMutation(updateTemplate, {
    onSuccess: async () => {
      showToast("success", "Success", "Design template updated successfully!")
    },
    onError: (error) => {
      handleErrors(showToast, error)
    }
  })
}

//--------------Get All designs-------------- //
const getCardsTypes = async () => {
  const { data } = await axiosInstance.get(`/get-card-types`);

  return data.data;
}

export const useGetCardsTypes = () => {
  const { showToast } = useGlobalContext();
  let { isLoading, data, error, isError } = useQuery('getCardsTypes', getCardsTypes);

  if (isError) {
    showToast("error", "Fetch Types ", error.response?.data?.message)
  }

  return { isLoading, data };
}

//--------------Delete template-------------- //
const deleteTemplate = async (payload) => {
  const { data } = await axiosInstance.delete(`/designs/${payload?.id}`);

  return data.data;
}

export const useDeleteTemplate = () => {
  const { showToast } = useGlobalContext();

  return useMutation(deleteTemplate, {
    onSuccess: async () => {
      showToast("success", "Success", "Design template deleted successfully!")
    },
    onError: (error) => {
      handleErrors(showToast, error)
    }
  })
}

//--------------Delete Design-------------- //
const getDesign = async (id) => {
  if (id) {
    const { data } = await axiosInstance.get(`/designs/${id}`);
    return data.data;
  }
}

export const useGetDesign = (id) => {
  const { showToast } = useGlobalContext();
  let { isLoading, data, error, isError } = useQuery('getDesign', () => getDesign(id));

  if (isError) {
    showToast("error", "Fetch Designs ", error.response?.data?.message)
  }

  return { isLoading, data };
}
