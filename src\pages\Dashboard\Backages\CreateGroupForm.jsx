import React, { useState, useEffect, useCallback, useRef } from "react";
import { Dialog } from "primereact/dialog";
import { InputText } from "primereact/inputtext";
import { InputTextarea } from "primereact/inputtextarea";
import { MultiSelect } from "primereact/multiselect";
import { Dropdown } from "primereact/dropdown";
import { Button } from "primereact/button";
import { Toast } from 'primereact/toast';
import { useLayout } from '@contexts/LayoutContext';
import axiosInstance from "../../../config/Axios";

// Do remember to clean the insane amount of logs

const GroupForm = ({
    isModalOpen,
    setIsModalOpen,
    onSuccess,
    groupToEdit = null,
    toast,
    preselectedMembers = []
}) => {
    const { isMobile } = useLayout();
    const toastRef = useRef(null);
    const isEditMode = Boolean(groupToEdit?.id);

    const getUserDisplayName = useCallback((user) => {
        return user?.name || user?.username || user?.email || `User ${user?.id}`;
    }, []);

    const getInitialFormData = useCallback(() => ({
        groupName: "",
        groupDescription: "",
        selectedMembers: [],
        groupStatus: "active", // Set default status
        cardTypeId: null, // Add new field for card type
        pendingCardAssignments: {}, // Add pending card assignments
    }), []);

    const [formData, setFormData] = useState(getInitialFormData());
    const [potentialMembers, setPotentialMembers] = useState([]);
    const [cardTypes, setCardTypes] = useState([]);
    const [loadingCardTypes, setLoadingCardTypes] = useState(false);
    const [statusOptions] = useState([
        { label: "Active", value: "active" },
        { label: "Inactive", value: "inactive" },
        // { label: "Published", value: "published" },
        // { label: "Draft", value: "draft" },
    ]);
    const [errors, setErrors] = useState({});
    const [loading, setLoading] = useState(false);
    const [fetchingMembers, setFetchingMembers] = useState(false);
    const [isInitialized, setIsInitialized] = useState(false);

    // No need for backendUrl or token as we're using axiosInstance

    const fetchCardTypes = useCallback(async () => {
        setLoadingCardTypes(true);
        try {
            const response = await axiosInstance.get('get-card-types', {
                headers: {
                    Accept: "application/json",
                }
            });

            setCardTypes(response.data.data || []);
        } catch (error) {
            console.error("Error fetching card types:", error);
            setCardTypes([]);
        } finally {
            setLoadingCardTypes(false);
        }
    }, []);

    const fetchAllUsers = useCallback(async () => {
        try {
            const response = await axiosInstance.get('datatable/users/view', {
                headers: {
                    Accept: "application/json",
                }
            });

            const usersData = response.data;
            console.log("Raw users data from API:", usersData);

            let users = [];
            if (Array.isArray(usersData)) {
                users = usersData;
            } else if (usersData?.data && Array.isArray(usersData.data)) {
                users = usersData.data;
            } else if (usersData?.users && Array.isArray(usersData.users)) {
                users = usersData.users;
            }

            if (users.length === 0) {
                console.warn("No users found in the response");
                (toast || toastRef).current?.show({
                    severity: 'warn',
                    summary: 'Warning',
                    detail: 'No users found in the system',
                    life: 5000
                });
                return [];
            }

            console.log("Processed users array:", users);

            const formattedUsers = users.map(user => ({
                id: user.id,
                name: getUserDisplayName(user),
            }));

            console.log("Formatted users for MultiSelect:", formattedUsers);
            setPotentialMembers(formattedUsers);
            return formattedUsers;
        } catch (error) {
            console.error("Error fetching users:", error);
            (toast || toastRef).current?.show({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to load users list. Please try again.',
                life: 5000
            });
            setPotentialMembers([]);
            throw error;
        }
    }, [getUserDisplayName, toast]);

    const fetchGroupDetails = useCallback(async (groupId) => {
        try {
            // Use axiosInstance instead of fetch directly
            const response = await axiosInstance.get(`groups/${groupId}?users=true`, {
                headers: {
                    Accept: "application/json",
                }
            });

            return response.data;
        } catch (error) {
            console.error("Error fetching group details:", error);

            // Show error in toast
            (toast || toastRef).current?.show({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to retrieve group data. The server returned an error.',
                life: 5000
            });

            // Return a minimal fallback object to prevent UI errors
            return {
                data: {
                    id: groupId,
                    title: "Group Information Unavailable",
                    description: "Could not load group details. Please try again later.",
                    status: "active",
                    users: []
                }
            };
        }
    }, [toast]);

    const initializeForm = useCallback(async () => {
        if (isInitialized) return;
        
        setFetchingMembers(true);
        try {
            // Fetch users and card types in parallel
            const [formattedUsers] = await Promise.all([
                fetchAllUsers(),
                fetchCardTypes()
            ]);

            // Handle preselected members
            if (preselectedMembers?.length > 0) {
                const missingMembers = preselectedMembers.filter(preselected =>
                    !formattedUsers.some(user => user.id === preselected.id)
                );

                if (missingMembers.length > 0) {
                    const formattedMissingMembers = missingMembers.map(member => ({
                        id: member.id,
                        name: getUserDisplayName(member)
                    }));
                    setPotentialMembers(prev => [...prev, ...formattedMissingMembers]);
                }
            }

            // If in edit mode, fetch group details
            if (isEditMode && groupToEdit?.id) {
                const groupDetails = await fetchGroupDetails(groupToEdit.id);
                const currentGroup = groupDetails.data || groupDetails;

                // Update form data with group details
                setFormData(prev => ({
                    ...prev,
                    groupName: currentGroup.title || "",
                    groupDescription: currentGroup.description || "",
                    selectedMembers: currentGroup.users?.map(user => ({
                        id: user.id,
                        name: getUserDisplayName(user)
                    })) || [],
                    groupStatus: currentGroup.status || "active",
                    cardTypeId: currentGroup.card_type_id || null,
                    pendingCardAssignments: groupToEdit.pendingCardAssignments || {},
                }));

                // Update potential members with group users
                if (currentGroup.users?.length > 0) {
                    const groupUsers = currentGroup.users.map(user => ({
                        id: user.id,
                        name: getUserDisplayName(user)
                    }));
                    setPotentialMembers(prev => {
                        const existingIds = new Set(prev.map(u => u.id));
                        const newUsers = groupUsers.filter(u => !existingIds.has(u.id));
                        return [...prev, ...newUsers];
                    });
                }
            } else if (preselectedMembers?.length > 0) {
                // Update form data with preselected members
                setFormData(prev => ({
                    ...prev,
                    selectedMembers: preselectedMembers.map(member => ({
                        id: member.id,
                        name: getUserDisplayName(member)
                    }))
                }));
            }

            setIsInitialized(true);
        } catch (error) {
            console.error("Error initializing form:", error);
            setErrors(prev => ({ ...prev, members: "Could not load data." }));
            (toast || toastRef).current?.show({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to initialize form. Please try again later.',
                life: 5000
            });
        } finally {
            setFetchingMembers(false);
        }
    }, [
        isEditMode,
        groupToEdit?.id,
        fetchAllUsers,
        fetchCardTypes,
        fetchGroupDetails,
        getUserDisplayName,
        preselectedMembers,
        isInitialized
    ]);

    useEffect(() => {
        if (isModalOpen && !isInitialized) {
            initializeForm();
        }
        return () => {
            if (!isModalOpen) {
                setIsInitialized(false);
                setErrors({});
                setFormData(getInitialFormData());
            }
        };
    }, [isModalOpen, initializeForm, isInitialized, getInitialFormData]);

    const handleChange = useCallback((name, value) => {
        console.log(`Updating ${name} with value:`, value);
        setFormData(prev => {
            const newData = {
                ...prev,
                [name]: value,
            };
            console.log('New form data:', newData);
            return newData;
        });

        if (errors[name]) {
            setErrors(prev => {
                const newErrors = { ...prev };
                delete newErrors[name];
                return newErrors;
            });
        }
    }, [errors]);

    const validateForm = useCallback(() => {
        const newErrors = {};

        if (!formData.groupName?.trim()) newErrors.groupName = "Group name is required.";
        if (!formData.groupDescription?.trim()) newErrors.groupDescription = "Group description is required.";

        if (!fetchingMembers) {
            if (formData.selectedMembers?.length === 0) {
                newErrors.selectedMembers = errors.members
                    ? "Cannot select members; list failed to load."
                    : "At least one member must be selected.";
            }
        }

        if (!formData.groupStatus) newErrors.groupStatus = "Group status is required.";
        if (!formData.cardTypeId) newErrors.cardTypeId = "Card type is required.";

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    }, [formData, fetchingMembers, errors.members]);

    const handleSubmit = async (e) => {
        e.preventDefault();
        if (!validateForm()) return;

        setLoading(true);
        try {
            const payload = {
                title: formData.groupName.trim(),
                description: formData.groupDescription.trim(),
                status: formData.groupStatus,
                card_type_id: formData.cardTypeId,
                user_ids: formData.selectedMembers.map(member => member.id),
                print_status: "unprinted",
                group_type: "regular",
                parent_group_id: null,
            };

            console.log('Submitting payload:', payload);

            let response;
            if (isEditMode) {
                response = await axiosInstance.put(`groups/${groupToEdit.id}`, payload);
                console.log('Edit response:', response.data);
            } else {
                response = await axiosInstance.post('groups', payload);
                console.log('Create response:', response.data);
            }

            // Show success message
            (toast || toastRef).current?.show({
                severity: 'success',
                summary: 'Success',
                detail: isEditMode ? 'Group has been updated successfully' : 'Group has been created successfully',
                life: 3000
            });

            // Close modal and call onSuccess with the response data
            setIsModalOpen(false);
            if (onSuccess) {
                // Pass the complete response data to onSuccess
                onSuccess({
                    ...response.data,
                    selectedMembers: formData.selectedMembers // Include selected members in the response
                });
            }
            
        } catch (err) {
            console.error("Submission error:", err);
            (toast || toastRef).current?.show({
                severity: 'error',
                summary: 'Error',
                detail: err.response?.data?.message || err.message || 'Failed to save group',
                life: 5000
            });
        } finally {
            setLoading(false);
        }

    };

    const renderFooter = () => {
        const submitLabel = isEditMode
            ? (loading ? "Updating..." : "Update Group")
            : (loading ? "Creating..." : "Create Group");

        return (
            <div className={`flex ${isMobile ? 'flex-col gap-3' : 'justify-end gap-2'} pt-4`}>
                <Button
                    label="Cancel"
                    icon="pi pi-times"
                    onClick={() => setIsModalOpen(false)}
                    className={`p-button-text ${isMobile ? 'w-full' : ''}`}
                    disabled={loading}
                />
                <Button
                    type="submit"
                    form="group-form"
                    label={submitLabel}
                    icon="pi pi-check"
                    className={`p-button-primary ${isMobile ? 'w-full' : ''}`}
                    disabled={loading || fetchingMembers}
                    loading={loading}
                />
            </div>
        );
    };

    return (
        <>
            <Toast ref={toast || toastRef} position="top-right" />
            <Dialog
                header={isEditMode ? "Edit Group" : "Create New Group"}
                visible={isModalOpen}
                style={isMobile ? { width: "95vw", height: "90vh" } : { width: "50vw", minWidth: '450px' }}
                breakpoints={isMobile ? {} : { '960px': '75vw', '641px': '95vw' }}
                modal
                className={`p-fluid ${isMobile ? 'mobile-group-dialog' : ''}`}
                onHide={() => !loading && setIsModalOpen(false)}
                footer={renderFooter()}
                closeOnEscape={!loading}
                maximizable={false}
                resizable={false}
            >
                <form onSubmit={handleSubmit} id="group-form" className="space-y-4">
                    {errors.message && (
                        <div className="p-error text-center mb-2 text-sm">{errors.message}</div>
                    )}

                    <div>
                        <label htmlFor="groupName" className="block text-sm font-medium text-gray-700 mb-1">
                            Group Name <span className="text-red-500">*</span>
                        </label>
                        <InputText
                            id="groupName"
                            value={formData.groupName}
                            onChange={(e) => handleChange("groupName", e.target.value)}
                            placeholder="Enter group name"
                            className={`w-full ${errors.groupName ? 'p-invalid' : ''}`}
                            disabled={loading}
                            autoFocus
                        />
                        {errors.groupName && (
                            <small className="p-error">{errors.groupName}</small>
                        )}
                    </div>

                    <div>
                        <label htmlFor="groupDescription" className="block text-sm font-medium text-gray-700 mb-1">
                            Group Description <span className="text-red-500">*</span>
                        </label>
                        <InputTextarea
                            id="groupDescription"
                            value={formData.groupDescription}
                            onChange={(e) => handleChange("groupDescription", e.target.value)}
                            rows={3}
                            placeholder="Enter group description"
                            className={`w-full ${errors.groupDescription ? 'p-invalid' : ''}`}
                            autoResize
                            disabled={loading}
                        />
                        {errors.groupDescription && (
                            <small className="p-error">{errors.groupDescription}</small>
                        )}
                    </div>

                    <div>
                        <label htmlFor="cardTypeId" className="block text-sm font-medium text-gray-700 mb-1">
                            Card Type <span className="text-red-500">*</span>
                        </label>
                        <Dropdown
                            id="cardTypeId"
                            value={formData.cardTypeId}
                            options={cardTypes}
                            onChange={(e) => handleChange("cardTypeId", e.value)}
                            optionLabel="name"
                            optionValue="id"
                            placeholder={loadingCardTypes ? "Loading card types..." : "Select card type"}
                            className={`w-full ${errors.cardTypeId ? 'p-invalid' : ''}`}
                            disabled={loading || loadingCardTypes}
                            loading={loadingCardTypes}
                        />
                        {errors.cardTypeId && (
                            <small className="p-error">{errors.cardTypeId}</small>
                        )}
                    </div>

                    <div>
                        <label htmlFor="selectedMembers" className="block text-sm font-medium text-gray-700 mb-1">
                            Select Members <span className="text-red-500">*</span>
                        </label>
                        <MultiSelect
                            id="selectedMembers"
                            value={formData.selectedMembers}
                            options={potentialMembers}
                            onChange={(e) => handleChange("selectedMembers", e.value)}
                            optionLabel="name"
                            dataKey="id"
                            placeholder={fetchingMembers
                                ? "Loading members..."
                                : potentialMembers.length === 0
                                    ? "No members available"
                                    : "Choose members"}
                            filter
                            display="chip"
                            className={`w-full ${errors.selectedMembers ? 'p-invalid' : ''}`}
                            disabled={loading || fetchingMembers || potentialMembers.length === 0}
                            showClear={!fetchingMembers && potentialMembers.length > 0}
                        />
                        {fetchingMembers && (
                            <small className="text-xs text-gray-500 pt-1">Fetching member list...</small>
                        )}
                        {!fetchingMembers && potentialMembers.length === 0 && !errors.members && (
                            <small className="text-xs text-yellow-600 pt-1">No potential members found.</small>
                        )}
                        {errors.selectedMembers && (
                            <small className="p-error block pt-1">{errors.selectedMembers}</small>
                        )}
                        {errors.members && (
                            <small className="p-error block pt-1">{errors.members}</small>
                        )}
                    </div>

                    <div>
                        <label htmlFor="groupStatus" className="block text-sm font-medium text-gray-700 mb-1">
                            Group Status <span className="text-red-500">*</span>
                        </label>
                        <Dropdown
                            id="groupStatus"
                            value={formData.groupStatus}
                            options={statusOptions}
                            onChange={(e) => handleChange("groupStatus", e.value)}
                            optionLabel="label"
                            optionValue="value"
                            placeholder="Select status"
                            className={`w-full ${errors.groupStatus ? 'p-invalid' : ''}`}
                            disabled={loading}
                        />
                        {errors.groupStatus && (
                            <small className="p-error">{errors.groupStatus}</small>
                        )}
                    </div>
                </form>
            </Dialog>
        </>
    );
};

export default GroupForm;