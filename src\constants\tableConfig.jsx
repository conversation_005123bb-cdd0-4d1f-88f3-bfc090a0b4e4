export const defaultTableConfig = {
    url: "",
    first: 0,
    rows: 10,
    page: 0,
    sortField: 'id',
    sortOrder: 1,
    filters: {},
}

// export const cardsTableConfig = {
//     url: "cards",
//     filters: {
//         //     'status': { value: 'ARCHIVED', matchMode: 'equals' },
//         //     'repetition': { value: '', matchMode: 'contains' },
//         //     'frequency': { value: '', matchMode: 'contains' },
//     }
// }

export const cardsTableConfig = {
    url: "datatable/cards/view",
    filters: {
        'name': { value: '', matchMode: 'contains' },
        'number': { value: '', matchMode: 'contains' },
        'type': { value: '', matchMode: 'equals' },
        'created_at': { value: '', matchMode: 'date' }, 
        'updated_at': { value: '', matchMode: 'date' }
    }
};


export const usersTableConfig = {
    url: "datatable/users/view",
    filters: {
        'company_name': { value: '', matchMode: 'contains' },
        'department': { value: '', matchMode: 'contains' },
        'position': { value: '', matchMode: 'contains' },
        'status': { value: '', matchMode: 'contains' },
        'phone': { value: '', matchMode: 'contains' },
        'email': { value: '', matchMode: 'contains' },
        'name': { value: '', matchMode: 'contains' },
        "type": { value: '', matchMode: 'contains' },
    }
}

export const groupsTableConfig = {
    url: "get-groups",
    filters: {
        'description': { value: '', matchMode: 'contains' },
        'group_type': { value: '', matchMode: 'contains' },
        'department': { value: '', matchMode: 'contains' },
        "status": { value: '', matchMode: 'contains' },
        'title': { value: '', matchMode: 'contains' },
        'name': { value: '', matchMode: 'contains' },
    }
}

export const designsTableConfig = {
    url: "get-designs-list",
    filters: {
        'name': { value: '', matchMode: 'contains' },
    }
}

export const companiesTableConfig = {
    url: "get-companies-list",
    filters: {
        'number_of_allowed_users': { value: '', matchMode: 'contains' },
        'number_of_allowed_cards': { value: '', matchMode: 'contains' },
        'description': { value: '', matchMode: 'contains' },
        'status': { value: '', matchMode: 'contains' },
        'name': { value: '', matchMode: 'contains' },
        'type': { value: '', matchMode: 'contains' },
    }
}